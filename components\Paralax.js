import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import Image from 'next/image';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const Parallax = ({ mediaSrc, altText = 'Image', isFull = false, isVideo = false, isBorder = true }) => {
  useEffect(() => {
    const hasParallax = gsap.utils.toArray('.has-parallax');

    hasParallax.forEach((hParallax) => {
      const bgMedia = hParallax.querySelector('img') || hParallax.querySelector('video');

      const parallax = gsap.fromTo(
        bgMedia,
        // { y: '-10%', scale: 1.2 },
        // { y: ' 10%', scale: 1.3, duration: 1, ease: 'none' }
        { scale: 1.2 },
        {scale: 1, duration: 2, ease: 'none' }
      );

      ScrollTrigger.create({
        trigger: hParallax,
        start: 'top 100%',
        end: () => `+=${hParallax.offsetHeight + window.innerHeight}`,
        animation: parallax,
        scrub: true,
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach((st) => st.kill());
    };
  }, []);

  return (
    <>
      <figure  className={`has-parallax  ${isFull ? 'full' : ''} ${isBorder == false ? 'zero_border' : ''} `}>
        <style jsx>{`
    .has-parallax {
      overflow: hidden;
 
   
      position:relative;
      }
   
    .full.has-parallax {
      overflow: hidden;
      height: 100vh;
      position: relative;
    }

    .has-parallax img, 
    .has-parallax video {
      max-width: 100%;
    }

    .has-parallax-content > .img,
    .has-parallax-content > video,
    .has-parallax > img,
    .has-parallax > video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

  `}</style>
        {isVideo ? (
          <video src={mediaSrc} autoPlay muted loop playsInline />
        ) : (
          // <img className='img' src={mediaSrc} alt={altText}  quality={100}/>
          <Image src={mediaSrc} fill alt={altText} className='img' quality={100} />
        )}
      </figure>

    </>
  );
};

export default Parallax;
