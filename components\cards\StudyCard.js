import React from 'react';
import styles from './card.module.scss'

const StudyCard = ({data}) => {
    const { content, title } = data;
  return (
    <div className={styles.study_card}>
        <div className={styles.card_contents}>
             <h3>{title}</h3>
             <div className={styles.desc}>
                {content.map((para, index) => (
                    <p key={index}>{para}</p>
                ))}
          </div>
        </div>
    </div>
  )
}

export default StudyCard