@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.partners_page{
    position: relative;
    padding:120px 0;
    .leaf{
        position: absolute;
        left: 0;
        top: -215px;
        @include max(1550) {
            top: -215px;
            width: 20%;
        }
        @include max(1060) {
            top: -205px;
            width: 35%;
        }
        @include max(767) {
            top: -115px;
        }

    }
    @include max(1200) {
        padding:80px 0 100px 0;
    }
    @include max(1060) {
        padding:50px 0;
    }
    @include max(767) {
        padding:40px 0;
    }
    h2{
        @include max(1550) {
            margin-bottom: 25px;
        }
    }
}
.logo_list{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    gap: 30px;
    @include max(1550) {
        gap: 25px;
    }
    @include max(1060) {
        gap: 15px;
    }
    @include max(992) {
        grid-template-columns: repeat(3,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(2,1fr);
    }
    li{
        background-color: #F0EFEB;
        border-radius: 16px;
        height: 275px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        @include max(1550) {
            height: 245px;
        }
        @include max(1060) {
            height: 200px;
        }
        @include max(1060) {
            height: 180px;
        }
        @include max(767) {
            height: 140px;
            gap: 10px;
            border-radius: 13px;
        }

    }
    .logo_img{
        height: fit-content;
        width: fit-content;
       
        img{
            height: fit-content;
            width: fit-content;
            max-width: 223px;
            max-height: 219px;
            @include max(1550) {
                max-width: 190px;
                max-height: 170px;
            }
            @include max(1060) {
                max-width: 150px;
                max-height: 140px;
            }
            @include max(767) {
                max-width: 110px;
                max-height: 95px;
            }
        }
    }   
}
:global(body.rtl) {
    .partners_page{
        .leaf{
            left: auto;
            right: 0;
        }
    }
}