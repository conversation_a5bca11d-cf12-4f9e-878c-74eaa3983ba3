import React, { useEffect, useRef } from 'react';
import parse from "html-react-parser";

import Image from 'next/image';
import styles from './card.module.scss';
const TextOverImg = ({
  image = '/images/direct-sourcing-bg.jpg', // default image path
  title = '',
  description = '',
}) => (
  <div className={styles.card_text_over_img}>
    {image && (
    <div className={styles.imageWrapper}>
      <Image
        src={image}
        alt={title}
        layout="fill"
        objectFit="cover"
        quality={90}
        priority
      />    
    </div>
      )}
    <div className={styles.overlay}>
      <div className={styles.text_wrap}>
        {title && <h2>{parse(title)}</h2>}
         {description && parse(description)}
      </div>
    </div>
  </div>
);

export default TextOverImg;