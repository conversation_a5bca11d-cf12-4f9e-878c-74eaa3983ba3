import React, { useState, useEffect, useRef } from "react";
import InnerBanner from '@/components/innerBanner/InnerBanner';
import TimelineSlider from '@/components/timelineSlider/TimelineSlider';
import styles from './about.module.scss';
import AnimatedTitle from '@/components/anim';
import ImageBg from '@/components/imageBgSet/imageBg';
import OurValueCard from '@/components/cards/ourValueCard';
import AOS from "aos";
import "aos/dist/aos.css";
import TeamCard from '@/components/cards/TeamCard';
import PartnersDistributers from "@/components/PartnersDistributers";
import Acceditations from "@/components/Acceditations";
import SingleTitleBanner from '@/components/single_title_banner';

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getAbout } from "@/utils/lib/server/publicServices";   

const About = (props) => {
       
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    const router = useRouter();
    const yoastData = props?.pageData?.yoast_head_json;

    if (!props?.pageData) {
        return null;
    }
    return (
        <>
            {yoastData && <Yoast meta={yoastData} />}

            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_image) && (
                <InnerBanner
                    title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                    bannerBg={props?.pageData?.acf?.banner_image?.url}
                    content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                />
            )}
           
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                    props?.pageData?.acf?.ourstory_title ||
                    props?.pageData?.acf?.ourstory_content ||
                    props?.pageData?.acf?.ourstory_image) && (
                    <div className={styles.story_top}>
               
                        <div className={`${styles.pt_150} ${styles.pb_120}`}>
                            <ImageBg
                                subtitle={props?.pageData?.acf?.ourstory_title}
                                description={props?.pageData?.acf?.ourstory_content}
                                imageSrc={props?.pageData?.acf?.ourstory_image?.url}
                                imageAlt={props?.pageData?.acf?.ourstory_image?.alt}
                            />

                        </div>
                    </div>
                )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && ( props?.pageData?.acf?.our_journey_title || props?.pageData?.acf?.our_journey) && (
                    <div className={styles.timeline_sec}>
                    <TimelineSlider
                        timelineData={props?.pageData?.acf?.our_journey}
                        title={props?.pageData?.acf?.our_journey_title} />
                    </div>
            )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.ov_title || props?.pageData?.acf?.our_purpose_vedio) && (
                    <div className={`${styles.purpose_sec}`} id="our-purpose">
                        <SingleTitleBanner
                            videoSrc={props?.pageData?.acf?.our_purpose_vedio?.url}
                            title={props?.pageData?.acf?.ov_title}
                        />
                    </div>
            )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.om_title || props?.pageData?.acf?.om_content || props?.pageData?.acf?.om_image) && (
                <div className={`${styles.pt_120}`}>
                    <ImageBg
                        title={props?.pageData?.acf?.om_title}
                        description={props?.pageData?.acf?.om_content}
                        imageSrc={props?.pageData?.acf?.om_image?.url}
                        imageAlt={props?.pageData?.acf?.om_image?.alt}
                    />
                </div>
            )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.section_title || props?.pageData?.acf?.our_values_listing) && (
            <div className={`${styles.pt_120} ${styles.pb_120} ${styles.our_values_sec}`}>
                <div className='container'>
                        <h2 className='main_title' data-aos="fade-up" data-aos-duration="1000">
                            {props?.pageData?.acf?.section_title && parse(props?.pageData?.acf?.section_title)}
                        </h2>
                    <ul className={styles.our_values_list}>
                        {props?.pageData?.acf?.our_values_listing && props?.pageData?.acf?.our_values_listing.length > 0 &&
                            props?.pageData?.acf?.our_values_listing.map((item, index) => (
                            <li key={index} data-aos="fade-up" data-aos-duration="1000" data-aos-delay={index * 100}> 
                                <OurValueCard data={item} />
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
                )}
            
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.our_vision_title || props?.pageData?.acf?.our_vision_image || props?.pageData?.acf?.our_vision_content) && (
            <div className={`${styles.pb_120}`}>
                <ImageBg
                        title={props?.pageData?.acf?.our_vision_title}
                        description={props?.pageData?.acf?.our_vision_content}
                        imageSrc={props?.pageData?.acf?.our_vision_image?.url}
                        imageAlt={props?.pageData?.acf?.our_vision_image?.alt}
                    />

            </div>
                )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.leadership_team_title || props?.pageData?.acf?.leadership_team_image) && (
                <div id="leadership-team">
                    <SingleTitleBanner
                        imageSrc={props?.pageData?.acf?.leadership_team_image?.url}
                        title={props?.pageData?.acf?.leadership_team_title}                    
                    />
                </div>
                )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (props?.pageData?.acf?.board_section_title || props?.pageData?.acf?.board_members_list) && (
                    <div className={`${styles.pt_120} ${styles.pb_120} ${styles.founder_team_sec} text-start`}>
                    <div className='container'>
                        {props?.pageData?.acf?.board_section_title && (
                            <AnimatedTitle
                                title={props?.pageData?.acf?.board_section_title}
                                tag="h2"
                                className="main_title"
                            />
                        )}
                        {props?.pageData?.acf?.board_members_list && props?.pageData?.acf?.board_members_list.length > 0 && (
                            <ul className={styles.founder_team}>
                                {props?.pageData?.acf?.board_members_list.map((founderTeam, index) => (
                                    <li key={index} data-aos="fade-up" data-aos-duration="1000">
                                        <TeamCard data={founderTeam} size="big" />
                                    </li>
                                ))}
                            </ul>
                        )}
                        </div>
                    </div>
                )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf &&
                (props?.pageData?.acf?.partners_title || props?.pageData?.acf?.partners_image || props?.pageData?.acf?.partners_list.length > 0) && (
                    <section id="partners">
                    <PartnersDistributers
                        data={props?.pageData?.acf?.partners_list}
                        title={props?.pageData?.acf?.partners_title}
                        imageSrc={props?.pageData?.acf?.partners_image?.url}
                    />
                    </section>
            )}
            {props &&
                props?.pageData &&
                props?.pageData?.acf &&
                (props?.pageData?.acf?.accreditations_title || props?.pageData?.acf?.accreditations_image || props?.pageData?.acf?.accreditations_logos.length > 0) && (
                    <section id="accreditation-and-certification">
                        <Acceditations
                            data={props?.pageData?.acf?.accreditations_logos}
                            title={props?.pageData?.acf?.accreditations_title}
                            imageSrc={props?.pageData?.acf?.accreditations_image?.url}
                        />
                    </section>
                )}
        </>
    )
}

export default About


export async function getStaticProps(locale) {
    const PageData = await getAbout(locale.locale);    
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}