@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.agriculture_sec{
    position: relative;
    .leaf_ht{
        position: absolute;
        right: 0;
        top: -165px;
        @include max(1060) {
            width: 20%;
        }
    }   
    .leaf_ht2{
        position: absolute;
        left: 0;
        top: -100px;
        z-index: 99;
        @include max(1060) {
            top: -139px;
            width: 20%;
        }
        @include max(992) {
            top: -65px;
        }
    }
}
.nature_sec{
    margin: 0 30px;
    @include max(767) {
        margin: 0 15px;
    }
}
.resilience_slider{
    margin-top: 45px;
    @include max(992) {
        margin-top: 25px;
    }
}
.stacking_sec{
    position: relative;
    z-index: 99;
    background-color: #34241D;

}
.production_top{
    display: flex;
    flex-wrap: wrap;
    .pro_left{
        width: 50%;
        max-width: 620px;
        @include max(767) {
            width:100%;
            max-width: 100%;
            margin-bottom: 30px;
        }
    }
    .pro_right{
        width: 50%;
        padding-left: 75px;
        @include max(1060) {
            padding-left: 40px;
        }
        @include max(767) {
            width:100%;
            padding-left: 0;
        }
        figure{
            height: 444px !important;
            @include max(1600) {
                height: 371px !important;
            }
            @include max(767) {
                height: 235px !important;
            }
        }
    }
    .left_img{
        margin-top: 90px;
        @include max(1060) {
            margin-top: 40px;
        }
        @include max(767) {
            margin-top: 30px;
        }
        figure{
            height: 325px !important;
            @include max(1600) {
                height: 300px !important;
            }
            @include max(767) {
                height: 170px !important;
            }
        }
    }
    figure{
        border-radius: 20px;
        overflow: hidden;
        @include max(767) {
            border-radius: 12px;
        }
        img{
            object-fit: cover;
        }
    }
}
.harvesting_sec{
    .harvesting_card{
        padding-top: 50px;
        @include max(767) {
            padding-top: 30px;
        }
    }
    h2{
        margin-bottom: 50px;
       
    }
    .stacking_card{
        display: flex;
        align-items: center;
    }
}
.main_title{
    margin-bottom: 50px;
    @include max(767) {
        margin-bottom: 30px;
    }
}
.sd_logo_list{
    display: grid;
    grid-template-columns: repeat(5,1fr);
    gap: 30px;
    @include max(1060) {
        gap: 20px;
    }
    @include max(992) {
        grid-template-columns: repeat(4,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(3,1fr);
        gap: 15px;
    }
    li{
        margin-bottom: 0;
    }
    img{
        width: 100%;
    }
}
:global(body.rtl) {
    .agriculture_sec{
        .leaf_ht{
            right: auto;
            left: 0;
        }
        .leaf_ht2{
            left: auto;
            right: 0;
            transform: scaleX(-1) !important;
        }
    }
    .production_top {
        .pro_right{
            padding-left: 0;
            padding-right: 75px;
        }
    }
}
.oil_sec{
    margin-bottom:60px;
}


 
.two_cl_layout{
    margin: 0; padding: 0; display: flex; flex-wrap: wrap; justify-content: space-between; position: relative; z-index: 5;
    li{ list-style: none; width: 47.9%;
    @include max(767) {
        width: 100%;
    }
    
    }
}

.f_24{
    font-size: size(24px);
    line-height: size(35px);
    font-weight: 400;
  }

  .icon_block{ position: absolute; bottom: 0;left: 0; width: 23%; bottom: -8%; z-index: 1;
    img{ width: 100%;}
}