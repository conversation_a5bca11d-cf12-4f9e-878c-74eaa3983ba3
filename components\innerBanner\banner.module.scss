@import "@/public/styles/mixins/mixins";

.inner_banner {
    // background-color: rgba(0, 0, 0, 0.3);
    // max-width: calc(100% - 60px);
    margin: 0 auto;
    position: relative;
    z-index: 1;
    transition: all .3s ease-in-out;
     height: 900px;
    // margin-top: 145px;

    // @include max(1600) {
    //     margin-top: 134px;
    // }

    // @include max(1060) {
    //     margin-top: 134px;
    // }

    // @include max(992) {
    //     margin-top: 109px;
    //     border-radius: 12px;
    // }

    // @include max(767) {
    //     margin-top: 76px;
    // }

    @include max(1550) {
        height: 800px;
    }

    @include max(1350) {
        height: 720px;
    }

    @include max(1060) {
        height: 600px   ;
    }


    .banner_img {
       
        // background-size: cover;
        // background-repeat: no-repeat;
        // background-position: top;
        display: flex;
        align-items: flex-end;
        padding-bottom:150px;
        position: relative;
        width: 100%;
        height: 100%;
        img{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        @include max(1550) {
            padding-bottom: 110px;
        }
        @include max(1300) {
            padding-bottom: 120px;
        }
        @include max(992) {
            background-size: cover !important;
            padding-bottom:100px;
            border-radius: 12px;
        }
        @include max(767) {
            padding-bottom:80px;
        }

        &:after {
            opacity: 0.6;
            content: "";
            display: block;
            width: 100%;
            height: 40%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
            // background: linear-gradient(0deg, rgb(0 0 0) 0%, rgb(1 1 1) 88.4615361691%), linear-gradient(180deg, rgb(0 0 0) 0%, rgb(0 0 0) 100%);
            position: absolute;
            left: 0;
            bottom: 0;
            
        }
    }

    .banner_text {
        position: relative;
        z-index: 1;
    }

    h1 {
        font-size: size(54px);
        line-height: size(64px);
        color: #fff;
        font-weight: 300;
        text-transform: capitalize;

        @include max(1550) {
            font-size: size(50px);
            line-height: size(60px);
        }
        @include max(1300) {
            font-size: size(43px);
            line-height: size(53px);
        }

        @include max(1060) {
            font-size: size(40px);
            line-height: size(50px);
        }
        @include max(820) {
            font-size: size(35px);  
            line-height: size(38px);
        }
        @include max(767) {
            font-size: size(28px);
            line-height: size(38px);
        }
    }
    p{
        margin-top: 15px;
        font-size: size(24px);
        line-height: size(40px);
        font-weight: 600;
        @include max(1300) {
            font-size: size(18px);
            line-height: size(25px);
        }
        @include max(767) {
            font-size: size(16px);
            line-height: size(22px);
        }
        &.small_txt { font-weight:300; font-size: size(18px); line-height: size(25px); max-width:60%;
            @include max(1550) {font-size: size(16px); max-width:44%; }
            @include max(767) {max-width: 100%; font-size: size(14px); line-height: size(20px);} 
        }
    }

    .banner_sub_img{ position: absolute; right:  0; bottom: 10px; aspect-ratio: 192.66/94.59; z-index: 11; width: 13.8%;
        img{position: unset;}
        @include max(767) {
            bottom: -40px;
        }
    }

    .p_relative{ position: relative;}
}