import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

const ActivityCard = ({ data }) => {
      if (!data) return null;
    
        const { image, title, desc = [] } = data;
        const textRef = useRef(null);
    useEffect(() => {
        gsap.registerPlugin(ScrollTrigger);

        if (textRef.current) {
            const titleElement = textRef.current.querySelector("h3");
            const paragraphElements = textRef.current.querySelectorAll("p");

            // Title: Smooth Text Reveal (Mask Effect)
            const titleAnimation = gsap.fromTo(
                titleElement,
                { opacity: 1, y: "100%", clipPath: "inset(0 100% 0 0)" }, // Start Hidden
                {
                    y: "0%",
                    clipPath: "inset(0 0% 0 0)", // Reveal smoothly
                    duration: 1.5,
                    ease: "power4.out",
                    scrollTrigger: {
                        trigger: titleElement,
                        start: "top 85%",
                        toggleActions: "play none none reset", // Repeats when scrolling back
                    },
                }
            );

            // Paragraphs: Fade-In Effect (Only after title animation completes)
            gsap.fromTo(
                paragraphElements,
                { opacity: 0, y: 20 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 1.2,
                    ease: "power2.out",
                    stagger: 0.3, // Stagger effect for each paragraph
                    scrollTrigger: {
                        trigger: textRef.current,
                        start: "top 90%",
                        toggleActions: "play none none reset",
                        onEnter: () => gsap.delayedCall(1, () => titleAnimation.eventCallback("onComplete", () => {
                            gsap.to(paragraphElements, { opacity: 1, y: 0, duration: 1.2, stagger: 0.3 });
                        })),
                    },
                }
            );
        }
    }, []);
  return (
    <div className={styles.activity_card}>
      <div className={`${styles.activity_desc} value_text`} ref={textRef}>
        <h3 dangerouslySetInnerHTML={{ __html: title }} />
        <p>{desc}</p>
      </div>
      <div className={styles.activity_pic}>
        <Image src={image} width={756} height={438} alt="image" />
      </div>
    </div>
  );
};

export default ActivityCard;
