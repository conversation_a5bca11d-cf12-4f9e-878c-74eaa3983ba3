@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.formContainer{
    max-width:755px;    
    font-family: var(--font-open-sans);
    padding-top:90px;
    padding-left: 50px;
    @include max(1600) {
        max-width: 600px;
    }
    @include max(1060) {
        max-width: 100%;
        padding: 50px;
    }
    @include max(767) {
        padding: 20px;
    }
}

.tabs {
    display: flex;
    margin-bottom: 20px;
    @include max(767) {
        overflow: hidden;
        overflow-x: scroll;
        &::-webkit-scrollbar{
            display: none;
        }
    }
    button {
        padding: 15px 50px;
        border: none;
        background: rgba(255, 255, 255,.5);
        color: #7F4930;
        font-size: size(16px);
        cursor: pointer;
        border-radius: 40px;
        margin: 0 ;
        font-family: var(--font-open-sans);
        font-weight: 600;
        opacity: .5;
        
        @include max(1600) {
            padding: 12px 40px;
            font-size: size(15px);
        }
        @include max(767) {
            padding: 12px 30px;
            font-size: size(14px);
            white-space: nowrap;
        }
        
        &.active{
            opacity: 1;
        }
    }
}

.tabs button:nth-child(2){
    margin-left: -15px;
    @include max(767) {
        margin-left: 15px;
    }
}

.formGroup {
    display: flex;
    flex-wrap: wrap;
     justify-content: space-between;
    margin-bottom: 20px;
    li{
        width: 48%;
        position: relative;
        @include max(767) {
            width:100%;
        }
    }
    input, select, textarea {
        width: 100%;
        border: none;
        border-bottom: 1px solid rgba(127, 73, 48,.6);
        border-radius: 0;
        background-color: transparent;
        height: 58px;
        font-size: size(16px);
        color: #34241D;
        font-family: var(--font-open-sans);
        outline: none;
        transition: all 1s ease-in-out;
        &::placeholder{
            color:#34241D;
        }
        @include max(1600) {
            font-size: size(16px);
        }

        @include max(767) {
            height: 50px;
        }
    }
    select{
        appearance: none;
        background: url(../../public/images/input_arw.png) no-repeat;
        background-position: 98% 50%;
        cursor: pointer;
    }
    

}
.formGroup_1{
    li{
        &:last-child{
            width: 100%;
        }
    }

}
.inquiryForm{
    margin-top: 45px;
    @include max(767) {
        margin-top: 25px;
    }
    .t_wrap{
        position: relative;
        height: 115px;
        @include max(1600) {
            height: 95px;
        }
        @include max(767) {
            height: 75px;
        }
    }
    .inp_line{
        display: block;
        width: 0;
        height:1px ;
        background-color: #e29771;
        position: absolute;
        left: 0;
        bottom: 0;
        transition: all 1s ease-in-out;
    }
    input, select, textarea {
        &:focus{
            color:#e29771;
            &::placeholder{
                color:#e29771;
            }
            &+span{
                width: 100%;
            }
        }
    }
    textarea {
        width: 100%;
        height: 100%;
        border: none;
        border-bottom: 1px solid rgba(127, 73, 48,.6);
        border-radius: 0;
        background-color: transparent;
        font-size: size(16px);
        color: #34241D;
        font-family: var(--font-open-sans);
        outline: none;
        &::placeholder{
            color:#34241D;
        }
        @include max(1600) {
            font-size: size(15px);
        }
    }
}


.fileUpload {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 25px 0;
    @include max(767) {
        flex-wrap: wrap;
    }
    span{
        font-size: size(16px);
        color: #34241D;
        @include max(1600) {
            font-size: size(15px);
        }
    }
}

.uploadBtn {
    background: #FFFFFF;
    color: #7F4930;
    padding: 10px 35px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    font-family: var(--font-open-sans);
    transition: all .3s ease-in-out;
    font-size: size(16px);
    &:hover{
        background-color: #7F4930;
        color: #fff;
    }
    @include max(1600) {
        font-size: size(14px);
        padding: 10px 30px;
    }
}

.uploadBtn input {
    display: none;
}

.terms {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    @include max(767) {
        gap: 15px;
        margin-bottom:0x;
    }
    label{
        font-size: size(16px);
        color: #7F4930;
        cursor: pointer;
        a{
            color: #7F4930;
            text-decoration: underline;
        }
        @include max(1600) {
            font-size: size(15px);
        }
    }
    input[type="checkbox"] {
        appearance: none;
        width: 25px; 
        height: 25px;
        border: 2px solid #34241D; // Brownish border color
        border-radius: 4px;
        background-color: #F5F0EB; // Light beige background
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        position: relative;
        &::after {
            content: "✔";
            font-size: 14px;
            color: #683B22; // Brownish check color
            position: absolute;
            display: none;
        }
        @include max(1600) {
            width: 23px;
            height: 23px;
        }
        @include max(767) {
            width: 30px;
            height: 22px;
        }
    }
    input[type="checkbox"]:checked::after {
        display: block;
    }
    
   
}

.submitBtn {
    background-color: #FFFFFF;
    border-radius: 30px;
    height: 50px;
    display: inline-flex;
    align-items: center;
    min-width: 175px;
    justify-content: space-between;
    position: relative;
    padding: 0 12px 0 30px;
    gap: 25px;
    font-family: var(--font-open-sans);
    border: none;
    box-shadow: 0px 0px 12px rgb(0 0 0 / 5%);
    transition: all .3s ease-in-out;
    cursor: pointer;
    margin-top: 45px;
    @include max(1600) {
        margin-top: 30px;
        height: 47px;
    }
    @include max(767) {
        margin-top: 15px;
    }
    .btn_text{
        color: #7F4930;
        font-size: size(16px);
        font-weight: 600;
        position: relative;
        z-index: 9;
        transition: all 1s ease-in-out;
        @include max(1600) {
            font-size: size(15px);
        }
    }
     .arw {
        position: relative;
        z-index: 1;
        margin-top: 3px;
        transition: all .3s ease-in-out;
        svg{
            @include max(767) {
                width: 20px !important;
                height: 20px !important;
            }
        }
    }
    &::after{
        content: "";
        position: absolute;
        height: 46px;
        width: 46px;
        background-color: #7F4930;
        border-radius: 30px;
        right: 2px;
        top: 2px;
        transition: all .3s ease-in-out;
        @include max(1600) {
            height: 43px;
            width: 43px;
        }
    }
    &:hover{
        &::after{
            width:calc(100% - 4px);
        }
        .btn_text{
            color: #fff;
        }
    }
}
:global(body.rtl) {
    .formContainer{
        padding-left: 0;
        padding-right: 50px;
    }
    .tabs {
        button{
            &:nth-child(2){
                margin-left: 0;
                margin-right: -15px;
            }
        }
    }
    .formGroup{
        select{
            background-position:0% 45%;
        }
    }

    .submitBtn{
        padding: 0 30px 0 12px;
        &::after{
            right: auto;
            left: 2px;
        }
    }
    .submitBtn {
        .arw{
            margin-top: -2px;
            transform: rotate(180deg);
        }
    }
}