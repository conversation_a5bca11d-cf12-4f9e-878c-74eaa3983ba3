@import "@/public/styles/mixins/mixins";
.common_link{
    background-color: rgba(0, 0, 0, 0.3);
    max-width: calc(100% - 60px);
    margin: 0 auto;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
    ul{
        display: flex;
        justify-content: center;
        gap: 70px;
        @include max(1250) {
            gap: 40px;
        }
        @include max(1060) {
            gap:30px;
        }
        @include max(992) {
            justify-content: flex-start;
            overflow: auto;
            padding: 0 20px;
        }
        &::-webkit-scrollbar{
            display: none;
        }
        li{
            margin-bottom: 0;
            a{
                transition: all .3s ease-in-out;
                padding:32px 0;
                display: block;
                font-size: size(16px);
                color: #FCE1CB;
                font-family: var(--font-obsolete);
                position: relative;
                &:after{
                    content: "";
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 100%;
                    height: 1px;
                    background-color: #7F4930;
                    transform: scale(0);
                    transition: all .3s ease-in-out;
                }
                @include max(1060) {
                    font-size: size(15px);
                }
                @include max(992) {
                    white-space: nowrap;
                    padding:25px 0;
                }
                
            }
            &:hover,&.active{
                a{
                    color: #fff;
                    &:after{
                        transform: initial;
                        
                    }
                }
            }
        }
    }
}