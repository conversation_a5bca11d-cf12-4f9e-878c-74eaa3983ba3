:root {
  --background: #34241D;
  --foreground: #171717;
  --primery_color: #FCE1CB;
  --hover-color: #7F4930;
  --container-width: 1400px;
  /* --container-width: 1400px; */
  --font-open-sans: 'Open Sans', sans-serif;
  --font-obsolete: 'Obsolete';

}

@font-face {
  font-family: 'Obsolete';
  src: url('../fonts/ObsoleteBold.eot');
  src: url('../fonts/ObsoleteBold.eot?#iefix') format('embedded-opentype'),
    url('../fonts/ObsoleteBold.woff2') format('woff2'),
    url('../fonts/ObsoleteBold.woff') format('woff'),
    url('../fonts/ObsoleteBold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Obsolete';
  src: url('../fonts/ObsoleteBold_1.eot');
  src: url('../fonts/ObsoleteBold_1.eot?#iefix') format('embedded-opentype'),
    url('../fonts/ObsoleteBold_1.woff2') format('woff2'),
    url('../fonts/ObsoleteBold_1.woff') format('woff'),
    url('../fonts/ObsoleteBold_1.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'icomoon';
  src: url('../fonts/icomoon.eot?pzxbmw');
  src: url('../fonts/icomoon.eot?pzxbmw#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?pzxbmw') format('truetype'),
    url('../fonts/icomoon.woff?pzxbmw') format('woff'),
    url('../fonts/icomoon.svg?pzxbmw#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slider_container {
  max-width: 1400px;
  width: 100%;
}

.icon-insta:before {
  content: "\e90c";
  color: #fff;
}

.icon-star-o:before {
  content: "\e906";
}

.icon-star:before {
  content: "\e907";
}

.icon-phone:before {
  content: "\e904";
}

.icon-arrow-right:before {
  content: "\e901";
}

.icon-arrow-left:before {
  content: "\e902";
}

.icon-play:before {
  content: "\e905";
}

.icon-chevron-left:before {
  content: "\e909";
}

.icon-chevron-right:before {
  content: "\e90a";
}

.icon-mail:before {
  content: "\e903";
}

.icon-cross:before {
  content: "\e90b";
}

.icon-chevron-small-left:before {
  content: "\e900";
}

.icon-cheveron-right:before {
  content: "\e908";
}

.icon-location:before {
  content: "\e947";
}

.icon-instagram:before {
  content: "\ea92";
}

.icon-youtube:before {
  content: "\ea9d";
}

.icon-linkedin2:before {
  content: "\eaca";
}


html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

.overflow_visible,
.overflow_visible body {
  overflow: visible;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: var(--font-open-sans);
  -webkit-tap-highlight-color: transparent;
}

body::-webkit-scrollbar {
  display: none;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: #fff;
  text-decoration: none;
  transition: all .3s ease-in-out;
}

.container {
  width: 100%;
  max-width: var(--container-width);
  margin: 0 auto;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

ul li,
ol li {
  margin-bottom: 15px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--heading-font);
  margin: 0;
  line-height: 100%;
  font-weight: 400;
  color: #fff;
}

h1 {
  font-size: 2.2rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.8rem;
}

h4 {
  font-size: 1.6rem;
}

h5 {
  font-size: 1.4rem;
}

h6 {
  font-size: 1.2rem;
}

p {
  color: #fff;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

p:only-child,
p:last-child {
  margin-bottom: 0 !important;
}

img {
  height: auto;
  max-width: 100%;
  display: block;
}

input:focus,
a:focus {
  outline: none;
}

.main_title {
  color: #FFFFFF;
  font-size: 3.375rem;
  line-height: 4.3rem;
  font-weight: 300;
  margin-bottom: 15px;
  letter-spacing: -1px;
}





.main_btn {
  background-color: #FFFFFF;
  border-radius: 30px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  min-width: 170px;
  justify-content: space-between;
  position: relative;
  padding: 0 12px 0 20px;
  gap: 25px;
  border: none;
  cursor: pointer;
}


.main_btn.download_btn .arrow svg {
  transform: rotate(90deg);
}






.main_btn::after {
  content: "";
  position: absolute;
  height: 46px;
  width: 46px;
  background-color: #7F4930;
  border-radius: 30px;
  right: 2px;
  top: 2px;
  transition: all 1s ease-in-out;
}

.main_btn .arrow {
  position: relative;
  z-index: 1;
  margin-top: 3px;
  /* transform: rotate(90deg); */
}

.main_btn:hover::after {
  width: calc(100% - 4px);
  border-radius: 30px;
}

.rtl .main_btn {
  padding: 0 20px 0 12px;
}

.rtl .main_btn::after {
  right: auto;
  left: 2px;
}

.rtl .main_btn .arrow {
  margin-top: 0;
  transform: rotate(180deg);
}

.button_text {
  color: #7F4930;
  font-size: 1rem;
  font-weight: 600;
  position: relative;
  z-index: 9;
  transition: all 1s ease-in-out;
}

.main_btn:hover .button_text {
  color: #fff;
}

.fade_effect {
  opacity: 0;
  display: block;
  width: 100%;
  transform: scale(1.2);
  transition: opacity 1s ease, transform 1s ease;
}

.text_effect {
  transform: translateY(305px);
  transition: all 3s ease-in-out;
}

.fadeIn .fade_effect {
  opacity: 1;
  position: relative;
  transform: initial;
  transition: opacity 1s ease, transform 1s ease;
}

.fadeIn .text_effect {
  transform: initial;
}

.swiper-pagination-bullet {
  background: #ffffff !important;
}

.swiper-pagination-bullet-active {
  background-color: var(--primery_color) !important;
}

.text_center {
  text-align: center;
}

.top_title_sec p {
  color: #fff;
  max-width: 830px;
  font-size: 1.125rem;
  line-height: 2rem;
}

.top_title_sec h6 {
  font-size: 1.5rem;
  line-height: 1.8rem;
  font-weight: 300;
}

.bg_black {
  background-color: rgba(0, 0, 0, .3);
}

.br_20 {
  border-radius: 20px;
}

.p_relative {
  position: relative;
}

.swiper_button_prev,
.swiper_button_next {
  cursor: pointer;
  opacity: 1;
  transition: opacity 0.3s;
}

.swiper-button-disabled {
  opacity: 0.5;
  pointer-events: all;
}

.swiper-button-lock {
  display: block !important;
}

.news_detail_page,
.news_detail_page body {
  overflow: visible;
}

html.html_loader,
html.html_loader body {
  overflow: hidden;
  height: 100%;
}

@media (max-width: 1550px) {

  .container {
    max-width: 1200px;
  }

  .main_title {
    font-size: 3rem;
    line-height: 4rem;
  }

  p {
    font-size: .9rem;
  }

  .button_text {
    font-size: .9rem;
    font-weight: 600;
  }

  .main_btn {
    height: 48px;
    min-width: 160px;
  }

  .main_btn::after {
    height: 44px;
    width: 44px;
  }
}

@media (max-width: 1300px) {
  .container {
    max-width: 82%;
  }

  .main_title {
    font-size: 2.6rem;
    line-height: 3.6rem;
  }
}

@media (max-width: 1060px) {
  .main_title {
    font-size: 2.6rem;
    line-height: 2.8rem;
  }
  .container {
    max-width: 88%;
  }

}

@media (max-width: 992px) {
  .main_title {
    font-size: 2.3rem;
    line-height: 2.5rem;
  }
}

@media (max-width: 767px) {
  .main_title {
    font-size: 1.75rem;
    line-height: 2rem;
  }

  .main_btn {
    height: 40px;
    min-width: 134px;
    padding: 0 10px 0 15px;
    gap: 12px;
  }

  .main_btn::after {
    height: 35px;
    width: 35px;
  }

  .main_btn svg {
    width: 20px !important;
    height: 20px !important;
  }

  .main_btn:hover::after {
    width: 96.5%;
    height: 36px;
  }

  .top_title_sec p {
    font-size: 1rem;
    line-height: 1.6rem;
  }

  #lottie {
    width: 125px !important;
    height: 180px !important;
  }

  .button_text {
    text-align: center;
    white-space: nowrap;
  }
}

.container-1300 {
  max-width: 1300px;
}





.text-start .main_title {
  text-align: left;
}

.title_color_02 {
  color: #34241D;
}


.scroll-up .header_main {
  background: #34241D;
  position: fixed;
  top: 0;
  /* Base Styles */
  box-shadow: 0px 1px 23px rgba(0, 0, 0, 0.22);
  background-color: var(--background);

  .logo {
    width: 200px;
  }

  .submenu {
    top: 100px;
  }

  .main_ul {
    padding: 0px 0 10px 0;
  }

  .header_social svg {
    width: 12px !important;
    height: 12px !important;
  }

  .top_header .top_contact li a {
    font-size: 12px;
  }

}



/* .scroll-up  .logo_main{    width: 200px !;} */

/* Max-width 992px */
@media (max-width: 992px) {
  .scroll-up.header_main .logo {
    width: 220px;
  }

  .scroll-up.header_main .submenu {
    top: 0;
  }

  .scroll-up.header_main .top_header .top_contact li a {
    font-size: 15px;
  }

  .scroll-up.header_main .toggle_line {
    top: 35px;
  }
}

/* Max-width 767px */
@media (max-width: 767px) {
  .scroll-up.header_main .logo {
    width: 150px;
  }

  .scroll-up.header_main .toggle_line {
    top: 30px;
  }
  h1 {
    font-size:1.7rem;
  }
  
  h2 {
    font-size: 1.55rem;
  }
  
  h3 {
    font-size: 1.45rem;
  }
  
  h4 {
    font-size: 1.4rem;
  }
  
  h5 {
    font-size: 1.25rem;
  }
  
  h6 {
    font-size: 1rem;
  }
}

@media (max-width: 350px) {
.button_text, p{
  font-size: .8rem;  
}
.main_btn{
  padding: 0 10px;
}
}


.loader-active{ overflow: hidden; height: 100%;}