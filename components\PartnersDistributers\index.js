import React, { useState, useEffect, useRef } from "react";
import styles from './partners.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import CommonLink from '@/components/commonLinks/CommonLink';
import Image from 'next/image';
import AOS from "aos";
import "aos/dist/aos.css";
import parse from "html-react-parser";
import SingleTitleBanner from "../single_title_banner";

const PartnersDistributers = ({ title, imageSrc, data }) => {
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);


    return (
        <>
            {imageSrc && title && (
            <SingleTitleBanner
                imageSrc={imageSrc}
                title={title}
            />
            )}
            <div className={styles.partners_page}>
                {/* <div className={styles.leaf}>
                    <Image src='/images/partner_leaf.png' width={236} height={428} quality={100} />
                </div> */}
                <div className='container'>
                    {data && data.length > 0 && 
                        data.map((logoList, index) => (
                            <div className={`${styles.w_100} ${styles.mb_120} `} key={index}>
                        {logoList.section_title && (
                            <div className="w_100">
                                        <h2 className="main_title" data-aos="fade-up" data-aos-duration="1000">
                                            {logoList.section_title && parse(logoList.section_title)}
                                        </h2>
                            </div>
                        )}
                        {logoList.logos_list && logoList.logos_list.length > 0 && (
                            <ul className={styles.logo_list}>
                                {logoList.logos_list.map((logs, index) => (
                                    <li key={index} data-aos="fade-up" data-aos-duration="1000">
                                        <div className={styles.logo_img}>
                                            <Image src={logs?.url} width={263} height={572} alt="image" />
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        )}
                    </div>
                 ) )}                 



                </div>
            </div>
        </>
    )
}

export default PartnersDistributers