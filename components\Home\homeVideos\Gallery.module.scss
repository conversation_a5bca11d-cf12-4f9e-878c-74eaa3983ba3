@import "@/public/styles/mixins/mixins";
.grid_parent{
    // height:100vh;
    transition: all .3s ease-in-out;
    display: flex;
  figure{
    height: 100% !important;
  }
}

.grid_image {
  position: relative;
  width: 100%;
  height:615px;
  margin: 120px 0;
  transition: all 1s ease-in-out;
  @include max(1060) {
    height:475px;
    margin: 100px 0;
  }
  @include max(992) {
    margin:0;
    transform: scale(.75);
  }
  @include max(767) {
    transform: initial;
    height: auto;
    padding: 40px 4%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px 1%;
  }
  img{
     object-fit: cover;
     border-radius: 20px;
     @include max(767) {
      border-radius: 12px;
     }
  }
  &.modalOpen {
    height: 790px;
    margin-top: 0;
    @include max(1060) {
      height:540px;
    }
    @include max(992) {
      transform: scale(1);
      margin-bottom: 50px;
      height: 400px;
      padding: 10px 4%;
    }
    @include max(767) {
      margin-bottom:0px; 
    }
    .item{
      border-radius: 0;
      position: absolute;
      width: 100% !important;
      height:790px;
      &::after{
        border-radius: 0;
      }
      @include max(767) {
        height: 230px !important;
      }
      figure{
          height: 100% !important;
      
      }
    }
    figure{
      
      &::after{
        border-radius: 0;
      }
    }
  }
}
.item img,
.item video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.item {
  position: absolute;
  top: 0;
  transition: all .7s ease-in-out;
  z-index: 1;
  cursor: pointer;
  border-radius: 20px;
  overflow: hidden;
  @include max(767) {
    position: initial;
    width: 100% !important;
    height: auto !important;
    border-radius: 10px;
  }
  figure{
    position: relative;
    @include max(767) {
      height: auto !important;
    }
    @include max(767) {
      height: 150px !important;
    }
    &::after{
      content: "";
      display: block;
      width: 100%;
      height: 100%;
      background: linear-gradient(
      to left,
      rgba(0, 0, 0, 0.29),
      rgba(0, 0, 0, 0.29)
    );
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 20px;
    @include max(767) {
      border-radius: 10px;
    }
    }
  }

}

.item1 {
  width: 318px;
  height: 322px;
  top: 25px;
  left: calc(50% - 318px*2.12);
  @include max(1500) {
    width: 290px;
    height: 294px;  
    left: calc(50% - 290px*2.1);
  }
  @include max(1060) {
    width: 190px;
    height: 192px;

  }
  @include max(767) {
    width: 48% !important;
  }
}

.item2 {
  width: 176px;
  height: 177px;
  left: calc(50% - 176px*2.76);
  top: calc(322px + 30px);

  
  @include max(1500) {
    width: 170px;
    height: 174px;
    left: calc(50% - 170px*2.86);
    top: calc(322px + 8px);
  }
  @include max(1060) {
    width: 146px;
    height: 147px;
  }
  @include max(767) {
    width: 48% !important;
  } 
}

.item3 {
  width: 611px;
  height: 612px;
  left: 50%;
  transform: translateX(-50%);
  @include max(1500) {
    width: 550px;
    height: 551px;
  }
  @include max(1060) {
    width: 470px;
    height: 471px;
    transform: translateX(-53%);
  }
  @include max(767) {
    transform: initial;
  }
  figure{
    @include max(767) {
      height: 200px !important;
    }
  }

}

.item4 {
  top: -10px;
  width: 357px;
  height: 360px;
  right: calc(50% - 357px* 2.02);
  @include max(1500) {
    width: 327px;
    height: 330px;
    right: calc(50% - 327px* 2.01);
  }
  @include max(1060) {
    width: 230px;
    height: 255px;
  }
  @include max(767) {
    width: 48% !important;
  }

}

.item5 {
  width: 217px;
  height: 217px;
  right: calc(50% - 217px * 2.41);
  top: calc(360px + 34px);

  @include max(1500) {
    width: 185px;
    height: 185px;
    right: calc(50% - 185px*2.81);
    top: calc(360px + 1px);
 }
  @include max(1060) {
    width: 157px;
    height: 157px;
    top: calc(157px + 130px);
  }
  @include max(767) {
    width: 48% !important;
  }
}

.item1.expanded+.moved {
  transform: none;
  transform: translateY(200%);
}

.item4.expanded+.moved {
  transform: none;
  transform: translateY(200%);
}


.inViewClass{
  .item{
    &.expanded {
      z-index: 5 !important;
    }
  
    &.moveLeft {
      transform: translateX(500%);
  
    }
  
    &.moveBottom {
      transform: translateY(500%);
  
    }
  }
  .item1{
    left: calc(50% - 318px*2.03);
    &.expanded {
      width: 100%;
      height: 100%;
      z-index: 1;
      left: 0;
      top: 0;
    }
  
    &.moved {
      transform: translateX(-200%);
    }
  
    &+.moded {
      transform: translateY(200%) !important;
    }
  
    &.specialEffect {
      transform: translateY(-200%);
    }
    @include max(1500) {
      left: calc(50% - 290px*2.03);
    }
    @include max(1060) {
      left: calc(50% - 190px*2.45);
    }
  }
  .item2 {
    left: calc(50% - 176px*2.86);
    top: calc(335px + 34px);
    &.expanded {
      width: 100%;
      height: 100%;
      z-index: 1;
      left: 0;
      top: 0;
    }
  
    &.moved {
      transform: translateX(-370%);
    }
    @include max(1500) {
      left: calc(50% - 170px*2.76);
      top: calc(330px + 10px);
    }
    @include max(1060) {
      left: calc(50% - 146px*2.9);
      top: calc(160px + 75px);
    }
    @include max(992) {
      top: calc(165px + 75px);
    }
  }
  .item3{
    &.expanded {
      width: 100%;
      height: 100%;
      top: 0;
      transform: translateX(-50%);
    }
  
    &.moved {
      transform: translateX(180%);
    }
  
    &.specialEffect {
      transform: translateX(-280%);
    }
  }
  .item4 {
    top: 10px;
    right: calc(50% - 357px* 1.92);
    &.expanded {
      width: 100%;
      height: 100%;
      z-index: 1;
      right: 0;
      top: 0;
    }
  
    &.moved {
      transform: translateX(200%);
    }
  
    &.specialEffect {
      transform: translateY(-200%);
    }
    @include max(1500) {
      right: calc(50% - 327px* 1.91);
   }
   @include max(1060) {
    right: calc(50% - 227px* 2.08);
 }
  }
  .item5 {
    right: calc(50% - 217px * 2.51);
    &.expanded {
      width: 100%;
      height: 100%;
      z-index: 1;
      right: 0;
      top: 0;
    }
  
    &.moved {
      transform: translateX(320%);
    }
    @include max(1500) {
      right: calc(50% - 185px*2.61);
    }
    @include max(1060) {
      right: calc(50% - 157px*2.55);
    }
  }
}
.modal {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 6;
  overflow: hidden;
  transition: all 1s ease-in-out;
}

.prev {
  position: absolute;
  z-index: 10;
  width: 50px;
  height: 50px;
  left: 50px;
  top: 50%;
  transform: translateY(-50%) rotate(180deg);
  cursor: pointer;
  @include max(767) {
    transform: translateY(-100%) rotate(180deg);
  }
}

.next {
  position: absolute;
  z-index: 10;
  width: 50px;
  height: 50px;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}



.close {
  position: absolute;
  z-index: 10;
  width: 50px;
  height: 50px;
  right: 50px;
  top: 50px;
  cursor: pointer;
  @include max(767) {
    right: 0;
    top: 20px;
  }
}
.fullscreenVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  
}
.videoThumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.fullscreenVideo {
  width: 100%;
  height: auto;
  max-height: 90vh;
  border-radius: 8px;
}
.modalContent{
   position: absolute;
   left: 0;
   right: 0;
   bottom: -170px;
   text-align: center;
   transition: all 2s ease-in-out;
   transform: translateY(190px);
   z-index: 9;
  h2{
    margin-bottom: 20px;
    @include max(767) {
      margin-bottom: 15px;
    }
  }
   p{
    color: #fff;
    max-width: 550px;
    margin: 0 auto;
    @include max(767) {
      font-size: size(12px);
      line-height: size(18px);
      padding: 0 4%;
    }
   }
}
.centerText{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 30px;
  text-align: center;
  z-index: 9;
  @include max(767) {
     bottom: auto;
     top: 55%;
     transform: translateY(50%);
  }
  h2{
    @include max(767) {
      margin-bottom: 0;
    }
  }
}
.modalOpen{
  .modalContent{
    transform: translateY(0);
  }
  .item{
    img,video{
      border-radius: 0;
    }
  }
}


:global(body.rtl){
   .item1{
     left: auto;
     right: calc(50% - 675px);
   }
   .item2{
    left: auto;
    right: calc(50% - 522px);
   }
   .item4{
    right: auto;
    left: calc(50% - 700px);
   }
   .item5{
    right: auto;
    left: calc(50% - 580px);
   }
   .inViewClass{
    .item1{
      right: calc(50% - 646px);
    }
    .item2{
      right: calc(50% - 505px);
    }
    .item4{
      left: calc(50% - 685px);
     }
     .item5{
      left: calc(50% - 545px);
     }
   }
}
.modalContent {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-in-out;
}

.showContent {
  opacity: 1;
  transform: translateY(0);
  bottom:30px;
}
