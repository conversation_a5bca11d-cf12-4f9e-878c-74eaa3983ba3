import React from "react";
import styles from "./serviceComponent.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import Icons from "@/public/Icons";

const ServiceComponent = ({ links = [
  {
    id: 1,
    title: 'Our Actives',
    desc: "Exosome & Ceramide-Rich Actives",
    image: '/images/property1.jpg',
    link: "/our-actives"
  },
  {
    id: 2,
    title: 'Hotels & Spas Solutions',
    desc: "Natural hotel and Spa products deeply rooted in the desert of Arabia's tradition",
    image: '/images/property2.jpg',
    link: "/hotels-and-spas/"
  }
] }) => {
  const router = useRouter();

  return (
    <div className={styles.service_link_block}>
      <ul className={styles.service_ul_new}>
        {links.map((service) => (
          <li key={service.id} style={{ background: `url(${service.image})` }}>
            <div className={styles.service_text_block_main}>
              <h4>{service.title}</h4>
              <div className={styles.hover_cnt}>
                <p>{service.desc}</p>
                <Link href={service.link} className="main_btn">
                  <span className='button_text'>Learn More</span>
                  <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                </Link>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ServiceComponent;



