import React from "react";
import styles from "./serviceComponent.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";
import Image from "next/image";
import Icons from "@/public/Icons";
import parse from "html-react-parser";

const ServiceComponent = ({ links }) => {
  const router = useRouter();

  return (
    <div className={styles.service_link_block}>
      <ul className={styles.service_ul_new}>
        {links &&
          links.length > 0 &&
          links.map((service) => (
          <li key={service.id} style={{ background: `url(${service.background_image_arc?.url || ''})` }}>
            <div className={styles.service_text_block_main}>
              <h4>{service.mainTitle_arc && parse(service.mainTitle_arc)}</h4>
              <div className={styles.hover_cnt}>
                {service.description_arc && parse(service.description_arc)}
                {service.button_arc && (
                  <Link href={service.button_arc?.url} className="main_btn">
                    <span className='button_text'>{service.button_arc?.title && parse(service.button_arc?.title)}</span>
                    <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                  </Link>
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ServiceComponent;



