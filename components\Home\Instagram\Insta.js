import React, { useEffect, useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import styles from "./insta.module.scss";
import Link from "next/link";
import Parallax from "@/components/Paralax";
import "swiper/css";
import "swiper/css/pagination";
import InstaCard from "@/components/cards/InstaCard";
import AOS from "aos";
import "aos/dist/aos.css";
import splitTextAnimation from "@/components/splitTextAnimation";
import Image from "next/image";

const Instagram = () => {
    const instaPosts = [
        { imageSrc: "/images/insta_1.jpg", link: "#", title: "AlUla Peregrina Oil harnesses nature with sustainable methods, delivering the exceptional potential of the peregrina tree." },
        { imageSrc: "/images/insta_2.jpg", link: "#", title: "Peregrina Season—celebrating heritage and growth." },
        { imageSrc: "/images/insta_3.jpg", link: "#", title: "Unveil the harmony of the past and present, where Al<PERSON><PERSON>’s heritage unfolds." },
        { imageSrc: "/images/insta_4.jpg", link: "#", title: "Experience excellence from the ground up, each harvest is carefully selected and sustainably sourced to bring you ..." },
        { imageSrc: "/images/insta_5.jpg", link: "#", title: "During Peregrina Season, 10 outstanding farmers were honoured with the Gold Farmers Excellence Award ..." },
    ];

    const sectionRef = useRef(null);
    const [animatedSlides, setAnimatedSlides] = useState(new Set());
    splitTextAnimation(sectionRef, "trigger_title");

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting) {
                    // Reset and restart animation each time section is in view
                    setAnimatedSlides(new Set());
                    instaPosts.forEach((_, index) => {
                        setTimeout(() => {
                            setAnimatedSlides((prev) => new Set([...prev, index]));
                        }, index * 300);
                    });
                }
            },
            { threshold: 0.3 } // Trigger when 30% of the section is visible
        );

        if (sectionRef.current) observer.observe(sectionRef.current);

        return () => observer.disconnect(); // Cleanup observer
    }, []);
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    return (
        <section className={styles.instaSection} ref={sectionRef}>
            <div className={styles.leaf}>
                {/* <Parallax mediaSrc="/images/leaf_2.png" /> */}
                <Image src='/images/leaf_2.png' width={269} height={378} alt="image" />
            </div>
            <h2 className="trigger_title"><Link href="#.">#alula_peregrina</Link></h2>
            <Swiper
                slidesPerView={5}
                loop={true}
                autoplay={{ delay: 2500, disableOnInteraction: false }}
                // pagination={{ clickable: true }}
                speed={2000}
                breakpoints={{
                    0: { slidesPerView: 2 },
                    768: { slidesPerView: 3 },
                    992: { slidesPerView: 4 },
                    1060: { slidesPerView: 5 },
                }}
                modules={[Pagination, Autoplay]}
                className="mySwiper"
            >
                {instaPosts.map((post, index) => (
                    <SwiperSlide key={index}>
                        <div
                            className={`${styles.slideWrapper} ${animatedSlides.has(index)
                                    ? index % 2 === 0
                                        ? styles.slideTop
                                        : styles.slideBottom
                                    : ""
                                }`}
                        >
                            <InstaCard imageSrc={post.imageSrc} link={post.link} title={post.title} />
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>
        </section>
    );
};

export default Instagram;
