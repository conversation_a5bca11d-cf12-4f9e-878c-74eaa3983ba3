import { useState, useEffect } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import gsap from "gsap";
import "swiper/css";
import "swiper/css/navigation";
import styles from "./timeline.module.scss";
import AnimatedTitle from "../anim";

const timelineData2 = [
    {
        year: "2024",
        title: "Phase 1",
        description: "Initial research and development phase focusing on core technologies and market analysis.",
        images: [
            { src: "/images/timeline_pic_2.png" },
        ],
    },
    {
        year: "2025",
        title: "Phase 2",
        description: "Product development and testing phase with focus on quality assurance and user feedback.",
        images: [
            { src: "/images/timeline_pic_2.png" },
        ],
    },
    {
        year: "2026",
        title: "Phase 3",
        description: "Market launch preparation including marketing strategy and distribution network setup.",
        images: [
            { src: "/images/timeline_pic_2.png" },
        ],
    },
    {
        year: "2027",
        title: "Phase 4",
        description: "Global expansion and partnership development to reach new markets and customers.",
        images: [
            { src: "/images/timeline_pic_2.png" },
        ],
    },
    {
        year: "2028",
        title: "Phase 5",
        description: "Innovation and growth phase focusing on new product development and market leadership.",
        images: [
            { src: "/images/timeline_pic_2.png" },
        ],
    },
];

const TimelineSlider2 = () => {
    const [activeSlide, setActiveSlide] = useState(0);
    const [swiperInstance, setSwiperInstance] = useState(null);
    const [slidesPerView, setSlidesPerView] = useState("auto");

    useEffect(() => {
        const updateSlidesPerView = () => {
            if (window.innerWidth > 1025) {
                setSlidesPerView("auto");
            } else if (window.innerWidth >= 768) {
                setSlidesPerView(4);
            } else {
                setSlidesPerView(2);
            }
        };

        updateSlidesPerView();
        window.addEventListener("resize", updateSlidesPerView);
        return () => window.removeEventListener("resize", updateSlidesPerView);
    }, []);

    useEffect(() => {
        if (!timelineData2[activeSlide]) return;

        const tl = gsap.timeline();

        tl.fromTo(
            `.${styles.content}`,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" }
        )
            .fromTo(
                `.${styles.images}`,
                { opacity: 0, x: 80 },
                { opacity: 1, x: 0, duration: 1, ease: "power3.out" },
                "-=0.5"
            );
    }, [activeSlide]);

    useEffect(() => {
        setTimeout(() => {
            const swiper = document.querySelector(`.${styles.timelineSwiper}`)?.swiper;
            if (swiper?.navigation) {
                swiper.navigation.init();
                swiper.navigation.update();
            }
        }, 500);
    }, []);

    const handleNext = () => {
        if (swiperInstance) {
            let nextIndex = activeSlide + 1;
            if (nextIndex < timelineData2.length) {
                swiperInstance.slideTo(nextIndex);
                setActiveSlide(nextIndex);
            }
        }
    };

    const handlePrev = () => {
        if (swiperInstance) {
            let prevIndex = activeSlide - 1;
            if (prevIndex >= 0) {
                swiperInstance.slideTo(prevIndex);
                setActiveSlide(prevIndex);
            }
        }
    };

    const currentImages = timelineData2[activeSlide]?.images || [];
    const parentClass = currentImages.length === 1 ? styles.singleImage : styles.twoImages;

    return (
        <div className={styles.timelineContainer}>
            <AnimatedTitle
                title="Future Roadmap"
                tag="h2"
                className={`main_title ${styles.main_title}`}
            />
            <div className={styles.timeline_set}>
                <div className={styles.timelineTop}>
                    <div className={styles.content}>
                        <h3>{timelineData2[activeSlide].title}</h3>
                        <div className={styles.paragraph}>
                            {timelineData2[activeSlide].description
                                .split('\n\n')
                                .map((para, index) => (
                                    <p
                                        key={index}
                                        dangerouslySetInnerHTML={{ __html: para.trim() }}
                                    />
                                ))}
                        </div>
                    </div>
                    <div className={`${styles.images} ${parentClass}`}>
                        {currentImages.length === 1 ? (
                            <div className={styles.singleWrapper}>
                                <div className={styles.imageBox}>
                                    <Image src={currentImages[0].src} width={550} height={394} alt="Event" />
                                </div>
                            </div>
                        ) : (
                            currentImages.map((img, index) => (
                                <div key={index} className={styles.imageBox}>
                                    <Image src={img.src} width={344} height={394} alt="Event" />
                                </div>
                            ))
                        )}
                    </div>
                </div>
                <div className="container">
                    <div className={styles.timelineBottom}>
                        <Swiper
                            slidesPerView={slidesPerView}
                            spaceBetween={0}
                            navigation={{
                                nextEl: ".swiper_button_next",
                                prevEl: ".swiper_button_prev",
                            }}
                            modules={[Navigation]}
                            onSwiper={(swiper) => {
                                setSwiperInstance(swiper);
                                setTimeout(() => {
                                    if (swiper?.navigation) {
                                        swiper.navigation.init();
                                        swiper.navigation.update();
                                    }
                                }, 100);
                            }}
                            onSlideChange={(swiper) => {
                                setActiveSlide(swiper.realIndex);
                            }}
                            centeredSlides={false}
                            loop={false}
                            watchOverflow={true}
                            className={styles.timelineSwiper}
                        >
                            {timelineData2.map((item, index) => (
                                <SwiperSlide key={index} className={activeSlide === index ? 'activeYear' : ""}>
                                    <div onClick={() => setActiveSlide(index)} className={`${styles.year_item} year_item`}>
                                        <span dangerouslySetInnerHTML={{ __html: item.year.replace(/\n/g, '<br />') }}></span>
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TimelineSlider2; 