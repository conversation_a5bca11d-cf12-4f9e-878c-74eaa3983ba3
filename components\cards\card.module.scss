@import "@/public/styles/mixins/mixins";

.instaCard {
    position: relative;

    .instaImg {
        position: relative;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
            transition: all .3s ease-in-out;
        }

        &::after {
            content: "";
            width: 100%;
            height: 100%;
            background: linear-gradient(0deg,
                    rgba(0, 0, 0, 0) 0%,
                    rgba(0, 0, 0, 0.5) 100%);
            position: absolute;
            top: 0;
            left: 0;
        }
    }

    .instaText {
        position: absolute;
        bottom: -100px;
        left: 0;
        padding: 20px;
        transition: all .5s ease-in-out;

        @include max(1600) {
            padding: 18px;
        }

        @include max(992) {
            bottom: 0;
        }

        @include max(767) {
            padding: 12px;
        }

        p {
            color: #fff;
            font-size: size(14px);
            line-height: size(20px);
            transition: all .5s ease-in-out;
            font-weight: 600;

            @include max(1600) {
                font-size: size(13px);
                line-height: size(18px);
            }
            @include max(1300) {
                font-size: size(12px);
                line-height: size(15px);
            }
            @include max(767) {
                font-size: size(12px);
                line-height: size(17px);
            }
        }
    }

    .insta_icon {
        position: absolute;
        left: 30px;
        top: 30px;

        @include max(1060) {
            left: 15px;
            top: 15px;
        }

        svg {
            transition: all .5s ease-in-out;

            @include max(1600) {
                width: 33px !important;
                height: 33px !important;
            }
            @include max(1300) {
                width: 30px !important;
                height: 30px !important;
            }
            @include max(1060) {
                width: 25px !important;
                height: 25px !important;
            }
        }
    }

    &:hover {
        img {
            transform: scale(1.2);
        }

        .insta_icon {
            svg {
                width: 30px !important;
                height: 30px !important;
                @include max(1300) {
                    width: 25px !important;
                    height: 25px !important;
                }
            }
        }

        .instaText {
            bottom: 0;
        }
    }
}

.news_card {
    border-left: 1px solid #7F4930;
    padding: 0 0 0 20px;
    display: block;

    @include max(992) {
        padding: 0 10px;
    }

    h3 {
        font-size: size(22px);
        line-height: size(32px);
        color: #fff;
        margin-bottom: 30px;
        font-weight: 500;
        text-transform: capitalize;

        @include max(1550) {
            font-size: size(18px);
            line-height: size(28px);
            margin-bottom: 27px;
        }

        @include max(1060) {
            font-size: size(16px);
            line-height: size(22px);
        }

        @include max(767) {}
    }

    p {
        color: #fff;
        font-size: size(18px);
        line-height: size(26px);
        margin-top: 20px;

        @include max(1600) {
            font-size: size(14px);
            line-height: size(20px);
        }

        @include max(1060) {
            font-size: size(13px);
            line-height: size(18px);
        }
    }

    .date {
        margin-bottom: 85px;
        font-size: size(13px);
        font-weight: 300;
        display: flex;
        align-items: center;
        color: #fff;

        @include max(1600) {
            margin-bottom: 58px;
        }

        @include max(992) {
            margin-bottom: 55px;
        }

        span {
            font-size: size(40px);
            font-weight: 600;
            font-weight: bold;
            margin-right: 10px;
            color: #fff;

            @include max(1600) {
                font-size: size(35px);
            }
        }
    }

    .news_img {
        img {
            width: 100%;
        }
    }

    &.news_page_card {
        padding-bottom: 15px;

        @include max(992) {
            padding-bottom: 0;
        }

        .date {
            margin-bottom: 80px;

            @include max(767) {
                margin-bottom: 40px;
            }
        }
    }
}

.value_card {
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 420px;
    border-radius: 20px;
    padding: 40px 50px;
    display: flex;
    align-items: flex-end;
    position: relative;

    @include max(1600) {
        min-height: 385px;
    }

    @include max(992) {
        min-height: 330px;
        padding: 35px 40px;
    }

    @include max(767) {
        padding: 20px 25px;
    }

    &::after {
        content: "";
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(343deg, rgba(52, 36, 29, 0) 0%, rgb(16 16 16 / 91%) 100%);
        border-radius: 20px;
    }

    .value_content {
        position: relative;
        z-index: 1;
        max-width: 520px;
        padding-bottom: 80px;

        @include max(1600) {
            max-width: 480px;
        }
    }

    h3 {
        color: #FFFFFF;
        font-size: size(28px);
        line-height: size(38px);
        font-weight: bold;
        margin-bottom: 10px;

        @include max(1060) {
            margin-bottom: 20px;
        }

        @include max(992) {
            font-size: size(25px);
            line-height: size(35px);
        }

        @include max(767) {
            font-size: size(22px);
            line-height: size(30px);
            margin-bottom: 10px;
        }
    }

    p {
        color: #fff;
        line-height: size(28px);

        @include max(1600) {
            line-height: size(23px);
        }

        @include max(767) {
            line-height: size(20px);
        }
    }
}

.team_card {
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    border-bottom-left-radius: 0 !important;
    transition: all .5s ease-in-out;

    @include max(992) {
        border-radius: 10px;
    }

    .team_img {
        position: relative;

        &:after {
            content: "";
            width: 100%;
            height: 100%;
            display: block;
            // background: linear-gradient(249deg, rgba(0, 0, 0, 0) 0%, rgb(0 0 0 / 15%) 100%);
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    figure {
        @include aspectRatio(412, 434);
        overflow: hidden;


        img {
            transition: all .5s ease-in-out;
            object-fit: cover !important;
        }

    }

    .teamDetails {
        position: absolute;
        left: 0;
        bottom: 0;
        background-color: #34241D;
        padding: 15px 30px 5px 0;
        z-index: 1;
        border-top-right-radius: 20px;
        height: 60px;
        transition: all .5s ease-in-out;

        @include max(1550) {
            padding: 10px 25px 5px 0;
        }
        @include max(1300) {
            height: 45px;
        }
        @include max(1060) {
            padding: 10px 25px 10px 10px;
            height: auto !important;
            left: -1px;
            bottom: -1px;
        }
    }

    h3 {
        color: #fff;
        font-size: size(32px);
        line-height: size(42px);
        font-weight: 400;
        transition: all .5s ease-in-out;
        text-transform: capitalize;
        white-space: nowrap;
        width: fit-content;

        @include max(1550) {
            font-size: size(25px);
            line-height: size(35px);
        }

        @include max(1250) {
            font-size: size(22px);
            line-height: size(32px);
        }

        @include max(1060) {
            font-size: size(18px);
            line-height: size(28px);
        }

    }

    p {
        color: #fff;
        transform: translate(-45px,45px);
        opacity: 0;
        visibility: hidden;
        width: 0;
        transition: all .5s ease-in-out;
        white-space: nowrap;

        @include max(1600) {
            margin-top: 0;
        }


        @include max(1060) {
            font-size: size(14px);
            opacity: 1;
            visibility: visible;
            width: 100%;
            transform: initial;
        }
    }

    &:hover {
        .teamDetails {
            height: 85px;
        }

        p {
            transform: initial;
            opacity: 1;
            visibility: visible;

            // @include max(1600) {
            //     width: 245px;

            // }
            // @include max(1060) {
            //     width: auto;

            // }
        }

        figure {
            img {
                transform: scale(1.2);
            }
        }
    }

    &.small {
        border-radius: 12px;

        h3 {
            font-size: size(28px);
            line-height: size(35px);

            @include max(1600) {
                font-size: size(23px);
                line-height: size(30px);
            }

            @include max(1060) {
                font-size: size(17px);
                line-height: size(27px);
            }
        }

        &:hover {
            p {
                width: 240px;
            }
        }

        .teamDetails {
            padding: 5px 30px 5px 0;
            height: 40px;
        }

        &:hover {
            .teamDetails {
                height: 65px;
            }
        }

        .team_img {
            &::after {
                background: linear-gradient(180deg,
                        rgba(0, 0, 0, 0) 0%,
                        rgba(0, 0, 0, 0.39) 100%);
            }
        }
    }

}

.culture_card {
    position: relative;
    margin: 0 auto;

    @include max(767) {
        width: 100% !important;
    }

    figure {
        @include aspectRatio(1400, 662);
        overflow: hidden;
        border-radius: 20px;

        img {
            height: 100% !important;
            width: 100% !important;
            object-fit: cover !important;
        }
    }

    :global {
        .has-parallax {
            height: 662px !important;

            @include max(1060) {
                height: 450px !important;
            }

            @include max(767) {
                height: 380px !important;
            }
        }
    }

    .cultural_content {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 50px 50px 120px 50px;
        width: 100%;

        @include max(1060) {
            opacity: 1 !important;
            padding: 30px;
        }

        @include max(767) {
            padding: 15px;
        }
    }

    p {
        color: #FFFFFF;
        line-height: size(26px);
        max-width: 515px;
        margin-bottom: 30px;

        @include max(1060) {
            margin-bottom: 20px;
        }

        @include max(767) {
            line-height: size(20px);
            margin-bottom: 15px;
        }
    }
}

.join_card {
    max-width: calc(100% - 60px);
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    min-height: 590px;
    display: flex;
    align-items: center;
    padding: 70px;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;

    @include max(1600) {
        min-height: 520px;
    }

    @include max(1200) {
        min-height: 420px;
    }

    @include max(1060) {
        min-height: 400px;
        padding: 50px;
    }

    @include max(767) {
        min-height: 400px;
        padding: 25px;
        align-items: flex-end;
        border-radius: 12px;
        max-width: calc(100% - 30px);
    }

    &::after {
        content: "";
        display: block;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
                rgba(0, 0, 0, 0.9) 0%,
                rgba(0, 0, 0, 0.18) 100%);
        position: absolute;
        left: 0;
        top: 0;
    }

    .join_card_desc {
        position: relative;
        z-index: 1;
        max-width: 710px;
    }

    p {
        color: #fff;
        max-width: 500px;
    }

    a {
        margin-top: 40px;
        padding: 0 15px 0 30px;

        @include max(767) {
            margin-top: 25px;
        }
    }
}

.product_card {
    position: relative;
    max-width: 456px;
    border-radius: 16px;
    overflow: hidden;

    @include max(992) {
        max-width: 380px;
    }

    @include max(767) {
        max-width: 250px;
    }

    .pro_image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: relative;

        &::after {
            content: "";
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg,
                    rgba(52, 36, 29, 0) 0%,
                    rgba(52, 36, 29, 0.7) 100%);
            position: absolute;
            left: 0;
            top: 0;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }
    }

    .pro_desc {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 35px;
        z-index: 1;

        @include max(767) {
            padding: 15px;
        }
    }

    h3 {
        color: #fff;
        font-size: size(32px);
        line-height: size(42px);
        margin-bottom: 25px;
        font-weight: 500;
        padding-right: 45px;

        @include max(1060) {
            font-size: size(28px);
            line-height: size(32px);
            padding-right: 0;
        }

        @include max(767) {
            font-size: size(22px);
            line-height: size(28px);
        }
    }
}

.botanical_card {
    background-color: rgba(0, 0, 0, .3);
    border-radius: 8px;
    padding: 15px 15px 20px 20px;
    min-height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    @include max(1600) {
        min-height: 210px;
    }
    @include max(1300) {
        min-height: 180px;
    }
    @include max(767) {
        padding: 15px;
        min-height: 170px;
    }

    .icon {
        margin-left: auto;
        max-width: 74px;
        min-height: 75px;
        @include max(1300) {
            max-width: 50px;
            min-height: 51px;
        }

        @include max(992) {
            max-width: 70px;
            min-height: 71px;
        }

        @include max(767) {
            max-width: 45px;
            min-height: 51px;
        }
    }

    h3 {
        color: #FFFFFF;
        font-size: size(26px);
        line-height: size(32px);
        font-weight: 300;
        margin-bottom: 10px;
        text-transform: capitalize;

        @include max(1600) {
            font-size: size(22px);
            line-height: size(30px);
        }
        @include max(1300) {
            font-size: size(20px);
            line-height: size(28px);
        }
        @include max(1060) {
            font-size: size(18px);
            line-height: size(25px);
        }

        @include max(767) {
            font-size: size(16px);
            line-height: size(23px);
        }
    }

    h4 {
        font-size: size(24px);
        line-height: size(34px);
        font-weight: 600;
        color: #FFFFFF;

        @include max(1600) {
            font-size: size(20px);
            line-height: size(30px);
        }
        @include max(1300) {
            font-size: size(18px);
            line-height: size(25px);
        }
        @include max(1060) {
            font-size: size(16px);
            line-height: size(26px);
        }

        @include max(767) {
            font-size: size(14px);
            line-height: size(18px);
        }
    }

    .desc {
        padding-right: 15px;

        @include max(1060) {
            padding-right: 0;
        }
    }

    &.no_icon {
        align-items: flex-end;
        flex-direction: row;
    }

    &.no_pad {
        padding-top: 0;
        justify-content: flex-end;

        .icon {
            width: 56px;
            position: absolute;
            top: 0;
            right: 10px;
        }
    }
}

.research_card {
    background-color: #34241D;
    min-height: 330px;
    padding: 20px 20px 25px 20px;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    @include max(1600) {
        min-height: 300px;
    }

    @include max(1060) {
        min-height: 270px;
    }

    @include max(767) {
        min-height: 200px;
        border-radius: 12px;
    }

    .icon {
        background-color: #7F4930;
        border-radius: 8px;
        width: 101px;
        height: 104px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px;

        @include max(1600) {
            width: 91px;
            height: 94px;
        }

        @include max(1060) {
            width: 81px;
            height: 84px;
        }

        @include max(767) {
            width: 71px;
            height: 74px;
        }
    }

    p {
        color: #FFFFFF;
        font-size: size(26px);
        line-height: size(36px);
        padding-right: 25px;

        @include max(1600) {
            font-size: size(22px);
            line-height: size(32px);
        }

        @include max(1060) {
            font-size: size(18px);
            line-height: size(25px);
        }
    }
}

.fragrances_card {
    .fragnance_img {
        border-radius: 16px;
        overflow: hidden;

        img {
            width: 100%;
            display: block;
            transition: all .3s ease-in-out;
        }

        &::after {
            content: "";
            display: block;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    h3 {
        font-size: size(26px);
        line-height: size(36px);
        margin-bottom: 5px;
        color: #fff;
        font-weight: 300;
        margin-top: 15px;
        transition: all .3s ease-in-out;

        @include max(1600) {
            font-size: size(22px);
            line-height: size(32px);
        }

        @include max(767) {
            font-size: size(20px);
            line-height: size(30px);
        }
    }

    p {
        line-height: 160%;

        @include max(767) {
            line-height: size(25px)
        }
    }

    &:hover {
        .fragnance_img {
            img {
                transform: scale(1.1);
            }
        }

        h3 {
            color: var(--primery_color);
        }
    }

    .fragnance_content {
        position: absolute;
        z-index: 100;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 6% 5%;
    }
}

.ingredient_card {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background-color: rgba(0, 0, 0, .3);
    border-radius: 20px;
    overflow: hidden;

    @include max(767) {
        border-radius: 12px;
    }

    .ing_image {
        max-width: 228px;

        @include max(767) {
            max-width: 100%;
            width: 100%;
        }

        img {
            display: block;
            width: 100%;
            height: 310px;
            object-fit: cover;

            @include max(1600) {
                height: 285px;

            }

            @include max(1060) {
                height: 280px;

            }

            @include max(1060) {
                height: 220px;
                object-position: top;
            }
        }
    }

    .ing_content {
        width: calc(100% - 228px);
        padding: 25px;

        @include max(1060) {
            width: calc(100% - 220px);
            padding: 15px;

        }

        @include max(767) {
            width: 100%;
        }
    }

    .desc {
        height: 120px;
        overflow: hidden;
        overflow-y: auto;
        scrollbar-color: #f8ddc8 #241914;
        scrollbar-width: thin;
        padding-right: 10px;

        @include max(1600) {
            height: 125px;
        }

        @include max(1060) {
            height: 135px;
            padding-right: 30px;
        }

    }

    h4 {
        color: #fff;
        font-size: size(30px);
        line-height: size(40px);
        font-weight: 600;
        margin-bottom: 20px;

        @include max(1600) {
            font-size: size(22px);
            line-height: size(30px);
        }

        @include max(1060) {
            font-size: size(22px);
            line-height: size(32px);
            margin-bottom: 15px;
        }
    }

    p {
        color: #fff;

        @include max(1060) {
            line-height: size(22px);
        }
    }
}

.resilience_card {
    background-color: #34241D;
    padding: 15px;
    border-radius: 16px;
    min-height: 520px;

    @include max(1600) {
        min-height: 455px;
    }

    @include max(992) {
        min-height: 395px;
    }

    .rs_image {
        border-radius: 8px;
        overflow: hidden;

        img {
            width: 100%;
        }
    }

    .rs_desc {
        padding: 0 65px 35px 0;

        @include max(1600) {
            padding: 0 25px 30px 0;
        }

        @include max(767) {
            padding: 0 25px 20px 0;
        }

        h3 {
            color: #FFFFFF;
            font-size: size(26px);
            line-height: size(36px);
            font-weight: 300;
            margin: 15px 0;

            @include max(1600) {
                font-size: size(23px);
                line-height: size(33px);
                margin-bottom: 10px;
            }

            @include max(992) {
                font-size: size(20px);
                line-height: size(30px);
            }
        }

        p {
            color: #fff;
        }
    }
}

.agricultural_card {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;

    @include max(767) {
        grid-template-columns: repeat(1, 1fr);
        background-color: #34241D;
        gap: 20px;
    }

    .agri_desc {
        background-color: #34241D;
        padding-top: 50px;
        width: 100%;

        @include max(1600) {
            padding-right: 15px;
        }

        @include max(767) {
            padding: 0 0 50px 0;
            order: 2;
        }

        p {
            color: #fff;

        }

        h3 {
            margin-bottom: 25px;

            @include max(1600) {
                font-size: size(45px);
                line-height: size(60px);
                margin-bottom: 15px;
            }

            @include max(1060) {
                font-size: size(40px);
                line-height: size(50px);
            }

            @include max(992) {
                font-size: size(30px);
                line-height: size(40px);
            }

            @include max(767) {
                font-size: size(25px);
                line-height: size(35px);
            }
        }
    }

    .agri_pic {
        border-radius: 20px;
        overflow: hidden;

        img {
            width: 100%;
        }
    }
}

.activity_card {
    display: flex;
    flex-wrap: wrap;
    background: #34241d;

    .activity_desc {
        width: 46%;
        padding-right: 70px;
        padding-top: 10px;

        @include max(992) {
            width: 100%;
            order: 2;
            padding-right: 30px;
        }

        @include max(767) {
            padding-right: 0;
        }

        h3 {
            color: #fff;
            font-size: size(26px);
            line-height: size(36px);
            font-weight: 300;
            margin-bottom: 20px;

            br {
                @include max(1060) {
                    display: none;
                }
            }

            @include max(767) {
                margin-bottom: 10px;
                font-size: size(20px);
                line-height: size(30px);
            }
        }

        p {
            color: #fff;
            font-size: size(16px);
            line-height: size(26px);

            @include max(767) {
                font-size: size(14px);
                line-height: size(24px);
            }
        }
    }

    .activity_pic {
        width: 54%;

        @include max(992) {
            width: 100%;
        }
    }

}

.cosmetic_card {
    width: 100%;
    background-repeat: no-repeat;
    background-size: 0%;
    min-height: 319px;
    border-radius: 16px;
    overflow: hidden;
    background-color: #FCE1CB !important;
    position: relative;
    transition: all .5s ease-in-out;
    padding: 0 30px 25px 25px;
    display: flex;
    align-items: flex-end;

    @include max(1600) {
        padding: 0 20px 25px 25px;
        min-height: 300px;
    }

    @include max(767) {
        min-height: 260px;
        border-radius: 12px;
    }

    .cos_image {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        transition: all .5s ease-in-out;
        opacity: 0;

        &:after {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            background-color: rgba(0, 0, 0, .4);
            width: 100%;
            height: 100%;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all .5s ease-in-out;
        }

        @include max(767) {
            opacity: 1;
        }
    }

    h3 {
        position: relative;
        z-index: 1;
        color: #34241D;
        font-size: size(26px);
        line-height: size(34px);
        transition: all .5s ease-in-out;
        font-weight: 400;

        @include max(1600) {
            font-size: size(23px);
            line-height: size(31px);
        }

        @include max(767) {
            font-size: size(18px);
            line-height: size(22px);
            color: #fff;
        }
    }

    &:hover {
        .cos_image {
            opacity: 1;

            img {
                transform: scale(1.2);
            }
        }

        h3 {
            transform: translate(10px, -10px);
            color: #fff;

            @include max(1600) {
                transform: translate(5px, -5px);
            }
        }
    }
}

.study_card {
    background-color: #34241D;
    border-radius: 16px;
    padding: 45px 30px;
    min-height: 330px;
    display: flex;
    align-items: flex-end;

    @include max(1600) {
        min-height: 310px;
    }

    @include max(1060) {
        min-height: 367px;
    }

    @include max(992) {
        padding: 30px 25px;
        min-height: auto;
    }

    @include max(767) {
        padding: 25px 20px;
        border-radius: 12px;
    }

    h3 {
        color: #fff;
        font-size: size(26px);
        line-height: size(36px);
        font-weight: 300;
        margin-bottom: 25px;

        @include max(992) {
            font-size: size(22px);
            line-height: size(32px);
        }

        @include max(767) {
            font-size: size(20px);
            line-height: size(28px);
            margin-bottom: 15px;

        }
    }

    p {
        color: #fff;
    }

    .desc {
        @include min(1600) {
            padding-right: 40px;
        }
    }
}

.graph_card {
    position: relative;
    background-color: rgba(0, 0, 0, .3);
    padding: 15px;
    min-height: 270px;
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    @include max(1600) {
        min-height: 240px;
    }

    @include max(1060) {
        min-height: 188px;
    }

    @include max(992) {
        min-height: 265px;
    }

    @include max(767) {
        border-radius: 16px;
    }

    .graph_img {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;

        img {
            width: 100%;
        }
    }

    h4 {
        font-size: size(24px);
        line-height: size(30px);
        color: #fff;
        font-weight: 300;
        margin-top: 10px;

        @include max(1600) {
            font-size: size(22px);
            line-height: size(28px);
        }

        @include max(1060) {
            font-size: size(18px);
            line-height: size(22px);
        }
    }
}

.formulation_card {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .formulation_image {
        width: 48%;
        border-radius: 20px;
        overflow: hidden;

        @include max(767) {
            width: 100%;
            border-radius: 12px;
        }

        img {
            width: 100%;
        }
    }

    .formulation_desc {
        width: 48%;

        @include max(767) {
            width: 100%;
            margin-bottom: 20px;
        }

        p {
            color: #fff;
        }
    }

    &.right {
        .formulation_desc {
            order: 2;

            @include max(767) {
                order: initial;
            }
        }
    }
}

.productline_card {
    border-radius: 16px;
    overflow: hidden;
    position: relative;
    transition: all .5s ease-in-out;

    .product_pic {
        position: relative;
        overflow: hidden;

        &::after {
            content: "";
            display: block;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg,
                    rgba(0, 0, 0, 0) 0%,
                    rgba(0, 0, 0, 0.8) 100%);
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
        }

        img {
            width: 100%;
            transition: all .5s ease-in-out;
        }
    }

    .pro_desc {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 30px;
        z-index: 1;

        @include max(767) {
            padding: 20px;
        }
    }

    h3 {
        font-size: size(26px);
        line-height: size(34px);
        color: #fff;
        margin-bottom: 20px;
        font-weight: 300;

        @include max(767) {
            font-size: size(24px);
            line-height: size(32px);
            margin-bottom: 10px;
        }
    }

    p {
        color: #fff;
        line-height: size(26px);

        @include max(767) {
            line-height: size(24px);
        }

        span {
            font-weight: bold;
        }
    }

    .hover_content {
        padding-right: 30px;
        margin-bottom: -105px;
        min-height: 78px;
        transition: all .5s ease-in-out;

        @include max(767) {
            margin-bottom: 0;
            min-height: auto;
        }
    }

    &:hover {
        .hover_content {
            margin-bottom: 0;
            min-height: auto;
        }

        .product_pic {
            img {
                transform: scale(1.2);
            }
        }
    }
}

:global(body.rtl) {
    .news_card {
        border-right: 1px solid #7F4930;
        padding: 0 20px 0 0;
        border-left: none;

        .date {
            span {
                margin-right: 0;
                margin-left: 10px;
            }
        }
    }

    .value_card {
        &::after {
            background: linear-gradient(9deg, rgba(52, 36, 29, 0) 0%, rgba(16, 16, 16, 0.91) 100%);
        }
    }

    .team_card {
        border-radius: 20px !important;
        border-bottom-right-radius: 0 !important;

        .teamDetails {
            padding: 15px 5px 5px 30px;
            left: auto;
            right: 0;
            border-radius: 0;
            border-top-left-radius: 20px;
        }

        &.small {
            .teamDetails {
                padding: 5px 0 5px 30px;
            }
        }
    }

    .join_card {
        &::after {
            background: linear-gradient(266deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.18) 100%);
        }
    }

    .activity_card {
        .activity_desc {
            padding-right: 0;
            padding-left: 70px;
        }
    }

    .botanical_card {
        .icon {
            margin-left: 0;
            margin-right: auto;
        }

        .desc {
            padding-right: 0;
            padding-left: 15px;
        }
    }

    .research_card {
        p {
            padding-right: 0;
            padding-left: 25px;
        }
    }

    .study_card {
        .desc {
            padding-right: 0;
            padding-left: 40px;
        }
    }

    .ingredient_card {
        .desc {
            padding-right: 0;
            padding-left: 10px;
        }
    }

    .productline_card {
        .hover_content {
            padding-right: 0;
            padding-left: 30px;
        }
    }

    .resilience_card {
        .rs_desc {
            padding: 0 0 35px 65px;
        }
    }
}

.our_value_card {
    position: relative;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.5s ease-in-out;

    &::after {
        content: "";
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(180deg, rgba(52, 36, 29, 0) 0%, rgba(52, 36, 29, 0.9) 100%);

    }

    .card_image {
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.5s ease-in-out;
        }
    }

    .card_content {
        position: absolute;
        width: 100%;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 6% 8%;
        z-index: 9;

        @include max(767) {
            padding: 5%;
        }
    }

    h4 {
        font-size: size(28px);
        line-height: size(38px);
        font-weight: bold;
        margin-bottom: 15px;
        @include max(1300) {
            font-size: size(22px);
            line-height: size(32px);
            margin-bottom: 10px;
        }
        @include max(820) {
            font-size: size(24px);
            line-height: size(32px);
            margin-bottom: 10px;
        }

        @include max(767) {
            font-size: size(20px);
            line-height: size(28px);
        }
    }

    p {
        font-size: size(18px);
        line-height: size(28px);
        max-width: 355px;
        @include max(1300) {
            font-size: size(15px);
            line-height: size(25px);
        }
        @include max(820) {
            font-size: size(15px);
            line-height: size(20px);
        }
    }

    @include max(820) {
        border-radius: 15px;
    }

    @include max(767) {
        border-radius: 10px;
    }

    &:hover {
        .card_image {
            img {
                transform: scale(1.2);
            }
        }
    }
}


.card_text_over_img {
    position: relative;
    width: 100%;
    aspect-ratio: 624 / 343;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    background: #222;

    .text_wrap {
        width: 100%;
    }

    @include max(1550) {
        aspect-ratio: 600 / 330;
    }
    @include max(1024) {
        aspect-ratio: 624 / 600;
    }

    @include max(767) {
        border-radius: 10px;
    }

}

.imageWrapper {
    position: absolute;

    inset: 0;
    z-index: 1;
}

.overlay {
    position: relative;
    z-index: 2;
    color: #fff;

    width: 100%;
    height: 100%;
    padding: 9% 9% 5% 9%;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    background: linear-gradient(180deg,
            rgba(52, 36, 29, 0) 0%,
            rgba(52, 36, 29, 0.9) 100%);

    h2 {
        margin: 0 0 12px 0;
        font-size: size(28px);
        line-height: size(38px);
        font-weight: 700;

        @include max(1550) {
            font-size: size(25px);
            line-height: size(32px);
        }
        @include max(1300) {
            font-size: size(22px);
            line-height: size(30px);
        }
        @include max(1024) {
            font-size: size(25px);
            line-height: size(32px);
        }

        @include max(767) {
            font-size: size(20px);
            line-height: size(25px);
        }
    }

    p {
        margin: 0;
        font-size: size(18px);
        font-weight: 400;
        line-height: size(25px);
        max-width: 90%;
        @include max(1550) {
            font-size: size(16px);
        }
        @include max(1300) {
            font-size: size(14px);
            line-height: size(20px);
        }
        @include max(1024) {
            font-size: size(16px);
            line-height: size(20px);
        }

        @include max(820) {
            font-size: size(14px);
            line-height: size(20px);
            max-width: 100%;
        }
    }

    @include max(1024) {
        padding: 9% 9% 5% 9%;
    }

    @include max(1024) {
        padding: 8% 3% 5% 3%;
    }



}




.card_sustainability {
    display: flex;
    border-radius: 0px 12px 12px 0px;
    overflow: hidden;
    min-height: 100px;
    align-items: stretch;
    background: #fde5d2; // fallback





    .left {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #fff;
        width: 29.3%;
        text-align: center;
        font-weight: bold;
        gap: 0.5rem;
        aspect-ratio: 196.59/196.6;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
        }
    }

    .title {
        font-size: 1rem;
        margin-top: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .right {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 1.5rem 2rem;
        background: transparent;
        color: #3b2921;
        @include max(1550) {
            padding: 1.5rem 1rem  1.5rem  1.5rem;
        }
        @include max(767) {
            padding: 1.5rem 1rem;
        }

        p {
            font-size: size(24px);
            line-height: size(35px);
            color: #34241D;
            font-weight: 600;
            @include max(1550) {
                font-size: size(21px);
                line-height: size(30px);
            }
            @include max(1300) {
                font-size: size(18px);
                line-height: size(26px);
            }
            @include max(1024) {
                font-size: size(16px);
                line-height: size(25px);
            }
        }
    }

}

@media (max-width: 600px) {
    .card {
        flex-direction: column;
    }

    .left,
    .right {
        min-width: unset;
        padding: 1rem;
        text-align: left;
    }
}