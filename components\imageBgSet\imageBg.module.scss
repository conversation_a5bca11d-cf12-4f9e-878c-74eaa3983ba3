@import "@/public/styles/mixins/mixins";

.about_bgean {
  max-width: calc(100% - 60px);
  margin: 0 auto;
  -webkit-border-radius: 20px;
  border-radius: 20px;
  overflow: hidden;
  min-height: 750px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  padding: 0 6.5%;
  transition: all 0.4s ease-out 0s;
  -moz-transition: all 0.4s ease-out 0s;
  -webkit-transition: all 0.4s ease-out 0s;
  -o-transition: all 0.4s ease-out 0s;
 // &::after {
  //   content: "";
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 100%;
  //   background: linear-gradient(90deg,
  //       rgba(0, 0, 0, 0.8) 0%,
  //       rgba(0, 0, 0, 0) 100%);
  //   z-index: 8;
  // }
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(to right, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.8) 30%, rgba(0,0,0,0.0) 100%);
    z-index: 8;
    pointer-events: none;
  }
  @include max(1550) {
    min-height: 650px;
  }
  @include max(1300) {
    min-height: 600px;
    padding: 0px 7.5%;
  }

  @include max(992) {
    min-height: 490px
  }

  @include max(767) {
    max-width: calc(100% - 30px);
    min-height: 640px;
    padding: 40px 46px;
    align-items: flex-end;
    flex-wrap: wrap;
  }

  @include max(480) {
    padding: 35px;
  }
  @include max(350) {
    padding: 20px;
  }
  &.products_open {
    min-height: 1000px;

    @include max(1550) {
      min-height: 800px;
      padding-bottom: 50px;
      align-items: flex-end;
    }

    @include max(1024) {
      min-height: 850px;
    }

    @include max(1024) {
      min-height: 800px;
    }

    @include max(767) {
      min-height: 650px;
    }
  }

  .imageContainer {
    &::after {
      @include max(480) {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 1) 100%);
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
      position: absolute;
      top: 0;
      left: 0;

      @include max(480) {
        object-position: 63%;
      }
    }
  }

  // .desc{
  //     width: 88%;
  // }
  .content {
    transition: all .4s ease-out 0s;
    -moz-transition: all .4s ease-out 0s;
    -webkit-transition: all .4s ease-out 0s;
    -o-transition: all .4s ease-out 0s;
    position: relative;
    z-index: 9;
    // max-width: 460px;
    width: 100%;

    .title_sec {
      max-width: 40%;

      @include max(1800) {
        max-width: 50%;
      }

      @include max(1024) {
        max-width: 100%;
      }
    }

    h2 {
      @include max(767) {
        font-size: 1.5rem;
      }

      b {
        @include max(1550) {
          display: block;
        }
      }
    }

    h3 {
      font-size: size(26px);
      line-height: size(36px);
      font-weight: 300;
      margin-bottom: 30px;
      @include max(1550) {
        font-size: size(23px);
        line-height: size(33px);
        margin-bottom: 20px;
      }
      @include max(1300) {
        font-size: size(20px);
        line-height: size(30px);
        margin-bottom: 20px;
      }
    }

    p {
      margin-bottom: 30px;
      max-width: 38%;
      font-size: size(18px);
      line-height: 160%;

      @include max(1800) {
        max-width: 48%;
      }
      @include max(1550) {
        max-width: 52%;
        font-size: size(16px);
      }

      @include max(1300) {
        font-size: size(14px);
        max-width: 41%;
        margin-bottom: 20px;
      }

      @include max(1024) {
        max-width: 100%;
        font-size: size(17px);
      }

      @include max(767) {
        margin-bottom: 15px;
        font-size: size(15px);
      }

      &.big_txt {
        font-size: size(18px);
        line-height: 160%;

        @include max(1024) {
          font-size: size(17px);
        }

        @include max(767) {
          font-size: size(15px);
          line-height: size(20px);
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    @media screen and (max-width: 1060px) {
      max-width: 100%;

      h3 {
        font-size: size(22px);
        line-height: size(32px);

        @include max(767) {
          font-size: size(16px);
          line-height: size(22px);
          margin-bottom: 10px;
        }
      }

      p {
        font-size: size(14px);
        line-height: size(22px);

      }

    }


    // @include max(767) {
    //     margin-bottom: -264%;

    // }

    // &.content_active{
    //     @include max(767) {
    //         margin-bottom: 0%;  
    //     }   
    // }
  }

  &.full_width {
    width: 100%;
    max-width: 100%;
    border: 0;
    border-radius: 0;
  }

  @include max(767) {
    border-radius: 10px;
  }
}

.email_btn {
  text-decoration: underline;
  font-weight: 200;
  font-size: size(26px);

  @include max(1300) {
    font-size: size(16px)
  }

  @include max(767) {
    font-size: size(18px)
  }
}

.logo_img_block {
  position: relative;
  z-index: 11;
  margin-right: 2%;
  @include max(767) {
    top:135px;
    width: 65%;
  }
}

.content_active {
  @media (max-width: 768px) {
    // Add your mobile-specific styles here
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }
}

.product_list {
  max-width: 1300px;
  margin-top: 75px;

  ul {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;

    @include max(1024) {
      gap: 15px;
    }

    @include max(767) {
      width: 320px;
      overflow: auto;
    }

    li {
      font-size: size(26px);
      line-height: size(34px);
      background-color: #FCE1CB;
      height: 285px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
      font-weight: 600;
      background-image: url(/images/product_bg.png);
      background-repeat: no-repeat;
      background-position: left bottom;
      margin: 0;
      padding: 0 85px;
      text-align: center;

      @include max(1550) {
        font-size: size(24px);
        line-height: size(30px);
        background-size: 60%;
        height: 260px;
      }

      @include max(1024) {
        padding: 0 30px;
        height: 210px;
        font-size: size(20px);
        line-height: size(30px);
      }

      @include max(767) {
        font-size: size(16px);
        line-height: size(22px);
        width: 200px;
        padding: 0 20px;
        background-size: 60%;
      }
    }
  }
}

button.active {
  &::after {
    width: 98%;
    border-radius: 30px;
  }

  :global(.arrow) {
    transform: rotate(90deg);
  }

  :global(.button_text) {
    color: #fff;
  }
}

.show_more_btn {
  background-color: transparent;
  border: none;
  text-decoration: underline;
  color: #fff;
  text-transform: capitalize;
  font-family: var(--font-open-sans);
  font-size: size(14px);
}

 