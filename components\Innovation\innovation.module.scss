@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.innovation_sec{
    max-width: 1400px;
    margin:0 auto;
    position: relative;
    
    .graphic{
        position: absolute;
        left: -270px;
        top: 150px;
        @include max(1600) {
            left: -95px;
        }
        @include max(1060) {
            left: -35px;
            width: 25%;
            top: 70px;
        }
    }
    @include max(1600) {
        max-width: 1250px;
    }
    @include max(1300) {
        max-width: 1150px;
    }
    @include max(1060) {
        max-width: 90%;
    }
    .innovation_image{
        overflow: hidden;
        border-radius: 20px;
        
    }
    .innovation_image_wrapper{
        @include max(767) {
            padding-top: 70px;
        }
        .image{
            width: 100%;
            border-radius: 20px;
            @include max(992) {
                border-radius: 12px;
            }
            @include max(767) {
                // height: 250px !important;
                height:75vh !important;
                object-fit: cover;
            }
        }
    }
    .innovation_title{
        position: absolute;
        top: 44%;
        transform: translateY(-50%);
        left: 0px;
        width: 100%;
        padding-left: 60px;
        @include max(767) {
            padding-left: 20px;
            top: 50%;
        }
    }
    h2{
        margin-bottom: 0;
        @include max(992) {
            font-size: 1.5rem;
            line-height:1.8rem;
        }
        .switch_text{
            font-weight: 600;
            display: block;
        }
    }
    .title_line{
        position: relative;
        width: 100%;
        span{
            width: 100%;
        }
    }
    .switch_text_2{
        @include max(992) {
            top:35px !important
        }
    }

    // @include max(767) {
    //     height: 100vh;
    // }


}