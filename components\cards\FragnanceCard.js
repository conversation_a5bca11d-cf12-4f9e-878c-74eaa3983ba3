import React, { useRef, useEffect } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import parse from "html-react-parser";

gsap.registerPlugin(ScrollTrigger);

const FragnanceCard = ({ image, title, link, description, showDescription = true }) => {
  const cardRef = useRef(null);
  const imageRef = useRef(null);
  const titleRef = useRef(null);

  useEffect(() => {
    const card = cardRef.current;
    const img = imageRef.current;
    const titleText = titleRef.current;

    gsap.fromTo(
      img,
      { opacity: 0, y: 50, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: card,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    gsap.fromTo(
      titleText,
      { opacity: 0, y: 20 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        delay: 0.3,
        scrollTrigger: {
          trigger: card,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    );
  }, []);

  const CardContent = (
    <div className={styles.fragrances_card} ref={cardRef}>
      <div className={styles.fragnance_img} ref={imageRef}>
        <Image src={image} width={411} height={332} alt={title} />

        <div className={styles.fragnance_content}>
        <h3 ref={titleRef}>{title && parse(title)}</h3>
      {showDescription && description && (
        <p>{parse(description)}</p>
      )}
        </div>
      </div>
      
    </div>
  );

  return link ? <Link href={link}>{CardContent}</Link> : CardContent;
};

export default FragnanceCard;
