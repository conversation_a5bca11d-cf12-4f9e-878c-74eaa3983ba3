@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.top_sec{
    .container{
        display: flex;
        flex-wrap: wrap;
        @include max(1600) {
            max-width: 100%;
            padding-left: calc((100% - 1200px) / 2) !important;
            padding-right: 30px;
        }
        @include max(1250) {
            padding-left: 30px !important;
        }
        @include max(992) {
            gap: 30px;
        }
        @include max(767) {
            padding-right: 0px;
        }
    }
    .top_left{
        width: 50%;
        @include max(1060) {
            padding-right: 20px;
        }
        @include max(992) {
            width: 100%;
        }
        p{
            color: #fff;
            max-width: 620px;
        }
    }
    .left_image{
        max-width: 465px;
        margin-top: 55px;
        @include max(1600) {
            max-width: 410px;
        }
        @include max(767) {
            max-width: 100%;
            margin-top: 40px;
        }
        figure{
            border-radius: 20px;
            height: 545px;
            @include max(1600) {
                height: 480px;
            }
            @include max(767) {
                height: 340px;
                border-radius: 12px;
            }
            img{
                object-fit: cover;
            }
        }
    }
    .top_right{
        width: 50%;
        @include max(1060) {
            width: 40%;
            margin-left: auto;
        }
        @include max(992) {
            width: 80%;
        }
        @include max(767) {
            width: 100%;
            padding-right: 30px;
        }
        figure{
            border-radius: 20px;
            overflow: hidden;
            @include max(767) {
                border-radius: 12px;
            }
            img{
                object-fit: cover;
            }
        }
    }
    .right_big_img{
        max-width: 620px;
        margin-left: auto;
        @include max(1600) {
            max-width: 550px;
        }
        @include max(1250) {
            max-width: 490px;
        }
        figure{
            height: 520px;
            @include max(1600) {
                height: 440px;
            }
            @include max(1250) {
                height: 400px;
            }
            @include max(767) {
                height: 325px;
            }
        }
    }
    .right_small_img{
        max-width: 385px;
        top: -110px;
        position: relative;
        left: -65px;
        @include max(1600) {
            max-width: 350px;
            top: -70px;
        }
        @include max(1250) {
            max-width: 320px
        }
        @include max(767) {
            left: 0;
            right: 0;
            margin: 0 auto;
            max-width: 260px;
        }
        figure{
            height: 320px;
            @include max(1250) {
                height: 290px;
            }
            @include max(1060) {
                height: 255px;
            }
            @include max(767) {
                height: 210px;
            }

        }
    }
    @include max(767) {
        padding-bottom: 0px;
    }
}
:global(body.rtl) {
    .top_sec {
        .right_small_img{
            left: auto;
            right: -65px;
        }
    }
}