import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import styles from './card.module.scss';
import Icons from '@/public/Icons';

const InstaCard = ({ imageSrc, link ,title }) => {
  return (
    <Link href={link || '#'} className={styles.instaCard}>
      <div className={styles.instaImg}>
        <Image src={imageSrc} width={320} height={329} alt="Instagram Post" />
      </div>
      <div className={styles.instaText}>
         {title&&<p>{title}</p>}
      </div>
      <div className={styles.insta_icon}>
        <Icons size={40} color="#fff" icon="insta" />
      </div>
    </Link>
  );
};

export default InstaCard;
