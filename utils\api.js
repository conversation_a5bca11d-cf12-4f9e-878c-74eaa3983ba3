const BASE_API_URL = process.env.NEXT_PUBLIC_API_BASE_URL; // Replace this with your actual API base URL


export async function fetchAPI(path, options = {}) {
  const url = `${BASE_API_URL}${path}`;
  
  const headers = {
    Accept: "application/json",
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, headers);
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    // Handle errors here, e.g., logging, displaying a message, etc.
    console.error("API Error:", error.message);
    throw new Error("Failed to fetch data from the API");
  }
}
