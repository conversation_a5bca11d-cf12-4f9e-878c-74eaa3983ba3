import InnerBanner from "@/components/innerBanner/InnerBanner";
import React from "react";
import styles from "./privacy.module.scss";
import Image from "next/image";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getPrivacyPolicy } from "@/utils/lib/server/publicServices"; 

const Privacy = (props) => {  
    
     const router = useRouter();
      const yoastData = props?.pageData?.yoast_head_json;
      if (!props?.pageData) {
            return null;
  }
  
  return (
    <div>
      {yoastData && <Yoast meta={yoastData} />}
         {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_image) && (                
                      <InnerBanner
                          bannerBg={props?.pageData?.acf?.banner_image?.url}
                          title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                          content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                      />           
           
        )}
      {props &&
        props?.pageData &&
         props?.pageData?.content && (
          <div className={` ${styles.privacy_contents} ${styles.pt_120} ${styles.pb_120}`}>
            <div className="container">
                {props?.pageData?.content && parse(props?.pageData?.content?.rendered)}
            </div>
          </div>
        )}
    </div>
  );
};

export default Privacy;


export async function getStaticProps(locale) {
    const PageData = await getPrivacyPolicy(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}
