"use client"
import React, { useState, useEffect, useRef } from "react";
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import styles from './innovation.module.scss';
import Image from "next/image";
import parse from "html-react-parser";
gsap.registerPlugin(ScrollTrigger);

const Innovation = ({scrollingText}) => {
      const innovationRef = useRef(null);
      const image1Ref = useRef(null);
      const image2Ref = useRef(null);
      const fromTextRef = useRef(null);
      const toTextRef = useRef(null);
      const exTextRef = useRef(null);
      const innoTextRef = useRef(null);
  useEffect(() => {
    const ctxs = gsap.context(() => {
        // Check if the ref exists before creating animations
        if (!innovationRef.current) return;

        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: innovationRef.current,
                start: 'top top',
                end: '+=2000', // Increased scroll distance for smoother animation
                scrub: 1.5, // Smooth scrub with 1.5 second lag
                pin: true,
                anticipatePin: 1, // Helps with smooth pinning
                onRefresh: () => {
                    // Ensure elements still exist on refresh
                    if (!innovationRef.current) return;
                },
              },
        });

        // 1. Image scale/parallax effect - smoother and longer
        tl.fromTo(image1Ref.current,
            { scale: 1.4 },
            { scale: 1, duration: 1, ease: 'power2.out' }
        );

        // 2. Fade in "From" - smoother transition
        tl.fromTo(fromTextRef.current,
            { opacity: 0, x: 40 },
            { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }, "-=0.3"
        );

        // 3. Fade in "Extremophile and Resilience" - smoother
        tl.fromTo(exTextRef.current,
            { opacity: 0, x: 40 },
            { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }, "-=0.2"
        );

        // 4. Crossfade image1 -> image2 - smoother transition
        tl.to(image1Ref.current, { opacity: 0, duration: 0.8, ease: 'power2.inOut' }, "+=0.5");
        tl.to(image2Ref.current, { opacity: 1, duration: 0.8, ease: 'power2.inOut' }, "<");

        // 5. Switch "From" to "To" - smoother text transitions
        tl.to(fromTextRef.current, { opacity: 0, x: -40, duration: 0.5, ease: 'power2.inOut' });
        tl.to(exTextRef.current, { opacity: 0, x: -40, duration: 0.5, ease: 'power2.inOut' }, "<");
        tl.fromTo(toTextRef.current,
            { opacity: 0, x: 40 },
            { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }, "-=0.2"
        );

        // 6. Show "Innovation and Purity" - smoother entrance
        tl.fromTo(innoTextRef.current,
            { opacity: 0, x: 40 },
            { opacity: 1, x: 0, duration: 0.6, ease: 'power2.out' }, "-=0.2"
        );
    }, innovationRef);

    return () => {
        // Kill all ScrollTriggers first
        ScrollTrigger.getAll().forEach(trigger => {
            if (trigger.trigger === innovationRef.current) {
                trigger.kill();
            }
        });

        // Then revert the GSAP context
        ctxs.revert();

        // Additional cleanup for any remaining animations
        if (innovationRef.current) {
            gsap.killTweensOf([
                innovationRef.current,
                image1Ref.current,
                image2Ref.current,
                fromTextRef.current,
                toTextRef.current,
                exTextRef.current,
                innoTextRef.current
            ]);
        }
    };
}, []);
  return (
    <div ref={innovationRef} className={`${styles.innovation_sec} ${styles.pb_120} ${styles.pt_120}`}>
      <div className={styles.graphic}>
        <Image
            src='/images/inno_graphic.png'
            width={285}
            height={440}
            alt="image"
            quality={100}
        />
      </div>
          <div className={styles.innovation_image_wrapper}>
              {scrollingText?.from_image && (
            <Image
                  ref={image1Ref}
                  src={scrollingText?.from_image?.url}
                  width={1400}
                  height={662}
                  alt="image"
                  quality={100}
                  className={styles.image}
                  />
                )}
              {scrollingText?.to_image && (
        <Image
            ref={image2Ref}
            src={scrollingText?.to_image?.url}
            width={1400}
            height={662}
            alt="image"
            quality={100}
            className={`${styles.image} ${styles.absolute_image}`}
            style={{
                width: '100%',
                height: 'auto',
                objectFit: 'cover',
                position: 'absolute',
                top: 120,
                left: 0,
                opacity: 0,
                zIndex: 0
            }}
                  />
                )}
    </div>

    <div className={styles.innovation_title}>
        <h2 className="main_title">
                  <div className={styles.title_line} style={{ position: 'relative', height: 'auto' }}>
                        {scrollingText?.from_title && (
                <span
                    ref={fromTextRef}
                    className={styles.switch_text}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        opacity: 1,
                    }}
                >
                    {scrollingText?.from_title && parse(scrollingText.from_title)}
                      </span>
                        )}
                      {scrollingText?.to_title && (
                <span
                    ref={toTextRef}
                    className={styles.switch_text}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        opacity: 0,
                    }}
                >
                    {scrollingText?.to_title && parse(scrollingText.to_title)}
                          </span>
                        )}
            </div>

            <div className={styles.title_line} style={{ position: 'relative', height: 'auto' }}>
                {scrollingText?.from_sub_title && (
                      <span
                    ref={exTextRef}
                    style={{
                        position: 'absolute',
                        top: 70,
                        left: 0,
                        opacity: 1,
                    }}
                    className={styles.switch_text_2}
                >
                   {scrollingText?.from_sub_title && parse(scrollingText.from_sub_title)}
                      </span>
                )}
                {scrollingText?.to_sub_title && (
                <span
                    ref={innoTextRef}
                    style={{
                        position: 'absolute',
                        top: 70,
                        left: 0,
                        opacity: 0,
                    }}
                    className={styles.switch_text_2}
                >
                    {scrollingText?.to_sub_title && parse(scrollingText.to_sub_title)}
                    </span>
                )}
            </div>

        </h2>
    </div>
</div>
  )
}

export default Innovation