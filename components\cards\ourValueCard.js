import React from 'react';
import Image from 'next/image';
import styles from './card.module.scss';
import parse from "html-react-parser";

const OurValueCard = ({ data }) => {


    const { ov_image, ov_title, content } = data;

    return (
        <div className={styles.our_value_card}>
            <div className={styles.card_image}>
                <Image
                    src={ov_image?.url || '/images/placeholder_Img.jpg'}
                    alt={ov_image?.alt}
                    width={624}
                    height={345}
                    quality={100}
                />
                <div className={styles.card_content}>
                    {ov_title && <h4>{parse(ov_title)}</h4>}
                    {content && <p dangerouslySetInnerHTML={{ __html: content?.trim() || "" }} />}
                </div>
            </div>
        </div>
    );
};

export default OurValueCard;
