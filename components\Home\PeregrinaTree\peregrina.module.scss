@import "@/public/styles/mixins/mixins";

.banner {
  width: 100%;
  // height: 100vh;
  padding-top: 120px;
}

.peri_tree {
  @include max(1060) {
    padding-bottom: 100px;
  }
  @include max(767) {
    padding-bottom:70px;
  }
  .tree_wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    position: relative;
    // @include max(992) {
    //   flex-direction: column-reverse;
    // }
    @include max(1060) {
       justify-content: space-between;
    }
    .content {
      width: 30%;
      height: 100%;
      @include max(992) {
        width: 100%;
        padding-bottom: 50px;
        text-align: center;
      }
      @include max(992) {
        padding-bottom: 30px;
      }
      .text {
        max-width: 46%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        @include max(992) {
          max-width: 100%;
          width: 100%;
          position: initial;
        }
        @include max(767) {
          transform:initial !important
        }
      }

      .text2 {
        max-width: 40%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        @include max(1500) {
          max-width: 45%;
        }
        @include max(1200) {
          max-width: 60%;
        }
        @include max(1060) {
          max-width: 45%;
        }
        @include max(992) {
          max-width: 100%;
          position: initial;
          transform: initial;
        }

        p{
          @include max(1200) {
            max-width: 66%;
          }
          @include max(992) {
            max-width: 100%;
          }
         
        }
      }
      a{
        position: relative;
        z-index: 5;
        @include max(992) {
          margin-top: 20px;
        }
        @include max(767) {
          height: 40px;
        }
        &::after{
          @include max(767) {
            height: 37px;
            width: 37px;
          }
        }
        svg{
          @include max(767) {
            width: 18px !important;
            height: 18px !important;
        
          }
        }
      }

      .title {
        margin-bottom: 40px;
        @include max(992) {
          margin-bottom: 0;
        }
        .bigText {
          font-size: size(54px);
          line-height: size(64px);
          color: #fff;
          font-weight: 300;
          display: block;
          @include max(1060) {
            font-size: size(45px);
            line-height: size(55px);
          }
          @include max(992) {
            font-size: size(40px);
            line-height: size(50px);
          }
          @include max(767) {
            font-size: size(28px);
            line-height: size(38px);
          }
        }

        .smallText {
          display: block;
          color: #fff;
          font-size: size(27px);
          line-height: size(37px);
          font-weight: bold;
          margin-bottom: 25px;
          @include max(1550) {
            font-size: size(25px);
            line-height: size(35px);
          }
          @include max(1060) {
            font-size: size(18px);
            line-height: size(30px);
            margin-bottom: 0;
          }
          @include max(767) {
            font-size: size(16px);
            line-height: size(26px);
            margin-bottom: 10px;
          }
        }
      }

      p {
        margin-top: 30px;
        color: #fff;
        line-height: size(26px);
        font-weight: 400;
        @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
          font-size: size(13px);
          line-height: size(23px);
        }
        @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
          font-size: size(13px);
          line-height: size(23px);
        }
        @include max(767) {
          margin-top: 20px;
          line-height: size(20px);
        }
      }
    }

    .images {
      width: 70%;
      display: flex;
      align-items: center;
      position: relative;
      @include max(1060) {
        width: 55%;
      }
      @include max(992) {
        width:85%;
        margin: 0 auto;
      }
      @include max(767) {
        width: 100%;
      }
      
      figure {
        max-width: max-content;
        margin-left: auto;
        @include max(1060) {
          margin-left: 0;
        }
        @include max(767) {
          transform: initial !important;
        }
        &:nth-child(2){
          margin-left: -100px;
          @include max(1550) {
            margin-left: -80px;
          }
          @include max(1060) {
            width:35%;
            margin-left:0;
          }
          
        }
        @include max(1500) {
          width: 55%;
        }
        @include max(767) {
          width: 100% !important;
        }

      }
    }
  }
}

:global(body.rtl){
  .peri_tree{
     .tree_wrap{
        .images{
          figure{
             margin-left: 0;
             margin-right: auto;
          }
        }
     }
  }
}