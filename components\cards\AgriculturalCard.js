import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

const AgriculturalCard = ({ image, title, desc }) => {
  const textRef = useRef(null);
  const imageRef = useRef(null);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    if (textRef.current) {
      const titleElement = textRef.current.querySelector("h3");
      const paragraphElements = textRef.current.querySelectorAll("p");

      // Title: Smooth Text Reveal (Mask Effect)
      const titleAnimation = gsap.fromTo(
        titleElement,
        { opacity: 1, y: "100%", clipPath: "inset(0 100% 0 0)" }, // Start Hidden
        {
          y: "0%",
          clipPath: "inset(0 0% 0 0)", // Reveal smoothly
          duration: 1.5,
          ease: "power4.out",
          scrollTrigger: {
            trigger: titleElement,
            start: "top 85%",
            toggleActions: "play none none reset", // Repeats when scrolling back
          },
        }
      );

      // Paragraphs: Fade-In Effect (Only after title animation completes)
      gsap.fromTo(
        paragraphElements,
        { opacity: 1, y: "100%", clipPath: "inset(0 100% 0 0)" }, // Start Hidden
        {
          y: "0%",
          clipPath: "inset(0 0% 0 0)", // Reveal smoothly
          duration: 3,
          ease: "power4.out",
          scrollTrigger: {
            trigger: titleElement,
            start: "top 100%",
            toggleActions: "play none none reset", // Repeats when scrolling back
          },
        }
      );
       if (imageRef.current) {
            gsap.fromTo(
              imageRef.current,
              {  scale: 1.1 }, // Start Position (Moves Down)
              {
  
                scale: 1, // Slight Zoom Out
                ease: "none",
                scrollTrigger: {
                  trigger: imageRef.current,
                  start: "top bottom", // When image enters viewport
                  end: "bottom top", // Until it exits
                  scrub: 1.5, // Smooth movement
                },
              }
            );
          }
    }
  }, []);
  return (
    <div className={styles.agricultural_card}>
      <div className={styles.agri_desc} ref={textRef}>
        <h3 className='main_title value_text'>{title}</h3>
        <div className='value_text'>
          <p>{desc}</p>
        </div>

      </div>
      <div className={styles.agri_pic} ref={imageRef}>
        <Image src={image} width={626} height={584} alt='image' />
      </div>
    </div>
  )
}

export default AgriculturalCard