@import "@/public/styles/mixins/mixins";
.invitation_center{
    border-radius: 20px;
    min-height: 675px;
    display: flex;
    align-items: flex-end;
    padding: 50px 60px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    @include max(1600) {
        padding: 50px 60px 70px 60px;
        min-height: 615px;
    }
    @include max(1200) {
        min-height: 540px;
        padding: 50px;
    }
    @include max(1060) {
        min-height: 480px;
        padding: 35px;
    }
    @include max(992) {
        min-height: auto;
        padding-top: 115px;
    }
    @include max(767) {
        padding: 0;
        flex-wrap: wrap;
        border-radius: 12px;
    }
    h6{
        font-size: size(28px);
        color: #fff;
        font-weight: 600;
        @include max(1600) {
            font-size: size(25px);
        }
        @include max(992) {
            font-size: size(22px);
            margin-bottom: 10px;
        }
        @include max(767) {
            font-size: size(18px);
        }
    }
    h2{
        margin-bottom:10px;
    }
    p{
        color: #fff;
        max-width: 590px;
        font-size: size(18px);
        line-height: 160%;
        // text-shadow: 0px 2px 9px rgb(0 0 0);
        @include max(1600) {
            max-width: 500px;
        }
        @include max(767) {
            font-size: size(15px);
            line-height: size(25px);
        }
    }
    a{
        margin-top:25px;

    }
    .invite_text{
        position: relative;
        z-index: 1;
        @include max(767) {
            margin-top:20px;
        }
    }
    .invite_img{
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 20px;
        overflow: hidden;
        @include max(767) {
            position: initial;
            border-radius: 12px;
        }
        &:after{
            content: "";
            display: block;
            width: 100%;
            height: 55%;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
            position: absolute;
            left: 0;
            bottom: 0;
        }
    }
}