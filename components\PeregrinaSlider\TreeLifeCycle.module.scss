@import "@/public/styles/mixins/mixins";
 
.wrapper {
  display: flex;
  height: 100vh;
  scroll-snap-type: y mandatory;
  overflow-y: scroll;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.left {
  flex: 1;
  position: sticky;
  top: 0;
  height: 100vh;
  background: #111;
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  transition: opacity 0.5s ease-in-out;
  img {
    object-fit: cover;
  }
}

.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  scroll-snap-align: start;
  scroll-padding-top: 20px;
}

.section {
  padding: 80px 40px;
  min-height: 100vh;
  border-bottom: 1px solid #333;
  scroll-snap-align: start;
  transition: background 0.3s ease;
  background: #f8f1e6;

  h3 {
    font-size: 2rem;
    margin: 0;
  }

  p {
    margin: 10px 0;
  }

  &.active {
    background: #fefaf5;
    h3 {
      color: #7b3f00;
    }
  }
}
.stack_Section{
  .tree_cycle_block_container{
    width: 100%;
    // height: 100vh;
    // min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    transition: justify-content 0.3s cubic-bezier(0.4,0,0.2,1);

    &.sticky {
      justify-content: center;
    }
    h2 {
      margin-bottom: 30px;
    }
  }
  // &.sticky{
  //   .tree_cycle_block_container{
  //     margin-top: 110px !important;
  //     @include max(767)  {
  //       margin-top: 150px !important;
  //     }
  //   }
  // }
}

.stack_block{ display: flex; flex-wrap: wrap; justify-content: space-around;width:100%;
  .stack_block_left{ width: 64%; height: 100%;
    @include max(1550){width: 62%;}
    .stack_block_left_inner{ width: 100%; position: relative; border-radius: 20px; overflow: hidden; height: 100%;
      @include max(820)  {border-radius: 10px;}
      .txt_block{
        position: absolute;  height: 100%; padding:3% 5%; width: 100%; right: 0; left: 0;
        @include max(820){padding: 5%;}
        transition: opacity 0.3s ease-in-out;
      }
       img{ width: 100%; height: 100%; display: block; object-fit: cover; transition: opacity 0.3s ease-in-out;}
    }

    @include max(820)  { width: 100%;} 
    @include max(767)  { height: auto;} 
  }
  .stack_block_right{ width: 33.9%; background: #E9E2D9; border-radius: 20px; padding: 34px 15px;
    @include max(1550){ padding: 25px 15px;width: 36%;}
    display: flex;align-items: center;justify-content: center;
    ul{ width: 100%;
      li{ list-style: none; width: 100%; display: flex; flex-wrap: wrap; align-items: center; border-radius: 3px;
        justify-content: space-between; padding: 17px 33px;
        transition: background-color 0.3s ease-in-out;
        &.active{ background: rgba(252, 225, 203,.48);}
        @include max(1550){
          padding: 10px 15px;
          margin-bottom: 10px;
        }
        @include max(767){
          padding: 10px 10px;
        }
      }
      // background: #FCE1CB; 
    }

    @include max(820) {
      width: 100%;
      margin-top: 20px;
      border-radius: 10px;
    }
  
  }

  .stack_icn_block{ width: 78px; height: 78px; border-radius: 100%; background: #34241D;display: flex;align-items: center;justify-content: center;
    @include max(1550) {width: 70px; height: 70px;}
    img{
      @include max(1550) {width:50%;}
    }
  }
  .stack_txt_block{  width: calc(100% - 90px);padding-inline-start: 30px;
    h4{ font-size: size(22px); color: #34241d; font-weight: 700; margin-bottom: 5px;
      @include max(1550) {font-size:size(20px) ;}
      @include max(1300) {font-size:size(18px) ;}
      @include max(767) {font-size:size(16px) ;}
    }
    p{ color: #34241d; font-weight: 400;font-size: size(20px);line-height:size(34px);
      @include max(1550) {font-size: size(18px);line-height:size(25px)}
      @include max(1300) {font-size: size(15px);line-height:size(20px)}
    }
    @include max(1550){
      padding-inline-start:15px;
    }
    @include max(1300){
      width: calc(100% - 70px)
    }
  }
  .txt_block{ align-items: flex-end; display: flex; flex-wrap: wrap;
    h3{ margin-bottom: 10px; font-size: size(24px); font-weight: 600;
      @include max(767) {
        font-size: size(20px);
      }
    }
    h4{ font-size: size(24px); font-weight: 300;
      @include max(1550) {
        font-size: size(22px);
      }
      @include max(767) {
        font-size: size(20px);
      }
    }
    p{ margin-top: 45px; max-width: 70%;font-size: size(18px);line-height: 160%;
      @include max(1550) {
        font-size: size(16px);
      }
      @include max(1300) {
        font-size: size(15px);
      }
      @include max(820) {
        max-width:100%;
      }
      @include max(767) {
        font-size: size(14px);
        line-height:size(22px);
        margin-top: 35px;
      }
    }
  
  }



  @include max(820) {
    height: auto;
  }



}