"use client";
import React, { useEffect, useRef } from "react";
import style from "./peregrina.module.scss";
import Image from "next/image";
import Link from "next/link";
import Icons from "@/public/Icons";

// Dynamically import GSAP to avoid server-side execution
const gsapPromise = import("gsap").then((mod) => mod.default);
const ScrollTriggerPromise = import("gsap/ScrollTrigger").then((mod) => mod.ScrollTrigger);

const Page = () => {
  const sectionRef = useRef(null);
  const textRef = useRef(null);
  const text2Ref = useRef(null);
  const pRef = useRef(null);
  const p2Ref = useRef(null);
  const figure1ref = useRef(null);
  const figure2ref = useRef(null);

  // Function to split text into spans for animation
  const splitText = ({ smallText, bigText }) => {
    return (
      <span>
        <span className={style.smallText}>
          {smallText.split("").map((char, i) => (
            <span key={`small-${i}`} className="char">{char}</span>
          ))}
        </span>
        <span className={style.bigText}>
          {bigText.split("").map((char, i) => (
            <span key={`big-${i}`} className="char">{char}</span>
          ))}
        </span>
      </span>
    );
  };

  useEffect(() => {
    let ctx;

    Promise.all([gsapPromise, ScrollTriggerPromise]).then(([gsap, ScrollTrigger]) => {
      gsap.registerPlugin(ScrollTrigger);

      ctx = gsap.context(() => {
        const chars1 = textRef.current.querySelectorAll(".char");
        const chars2 = text2Ref.current.querySelectorAll(".char");

        // Initial GSAP setup
        gsap.set([text2Ref.current, pRef.current, p2Ref.current], { opacity: 0 });
        gsap.set([textRef.current, figure1ref.current], { scale: 0.5 });
        gsap.set(figure1ref.current, { xPercent: 70 });
        gsap.set(figure2ref.current, { opacity: 0, scale: 0.5 });

        // Title animation and reveal
        gsap.to([textRef.current, figure1ref.current], {
          scale: 1,
          duration: 0.8,
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            end: "top 50%",
            scrub: 1,
          },
          onComplete: () => {
            // Fade in paragraph after title animation
            gsap.to(pRef.current, { opacity: 1, duration: 0.8 });
          },
        });

        // Letter by letter reveal for first text
        gsap.from(chars1, {
          opacity: 0,
          y: 50,
          stagger: 0.02,
          duration: 0.6,
          scrollTrigger: {
            trigger: textRef.current,
            start: "top 80%",
            end: "top 50%",
            scrub: 1,
          },
        });

        // Pinning effect & transition to second text and image
        const tl = gsap.timeline({
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 10%",
            end: "+=800px",
            scrub: 1,
            pin: true,
          },
        });

        tl.to(textRef.current, { scale: 2, opacity: 0, duration: 0.5 })
          .from(chars2, {
            opacity: 0,
            y: 50,
            stagger: 0.02,
            duration: 0.6,
          }, "<")
          .to(text2Ref.current, { scale: 1, opacity: 1, duration: 0.6 }, "<")
          .to(figure1ref.current, { xPercent: 30, duration: 0.6 }, 0)
          .to(figure2ref.current, { scale: 1, opacity: 1, duration: 0.8 }, "<")
          .add(() => {
            // Fade in second paragraph after second title appears
            gsap.to(p2Ref.current, { opacity: 1, duration: 0.8 });
          }, "-=0.5"); // Delayed slightly before paragraph appears
      });
    });

    return () => ctx?.revert();
  }, []);

  return (
    <>
      {/* <section className={style.banner}></section> */}

      <section className={style.peri_tree} ref={sectionRef}>
        <div className={style.tree_wrap}>
          <div className={style.content}>
            <div className={style.text} ref={textRef}>
              <h2 className={style.title}>
                {splitText({ smallText: "Uncover the secret of ", bigText: "The Peregrina Tree" })}
              </h2>
              <p ref={pRef}>
                In AlUla, Peregrina is already cultivated sustainably by local farmers, including many women. Peregrina farming has become a landmark of the local ecology, with low water requirements and a positive environmental impact through a no waste and clean agriculture with a minimal footprint. Peregrina seeds are carefully harvested at maturity, then hand-sorted and dried in the shade. Producers decant the oil naturally for sustainable and superior quality.
              </p>
               <Link href="/about" className="main_btn">
                <span className='button_text'>Learn More</span>
                <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
              </Link>
            </div>

            <div className={style.text2} ref={text2Ref}>
              <h2 className={style.title}>
                {splitText({ smallText: "The Peregrina Tree and ", bigText: "The Local Community" })}
              </h2>
              <p ref={p2Ref} className={style.para_two}>
                At Alula Peregrina, we believe in pushing boundaries through research and innovation. Our dedicated team explores the latest advancements in sustainable technology to develop eco-friendly products that don’t compromise on quality or style. From experimenting with renewable materials to creating energy-efficient production techniques, innovation drives our sustainability journey.
              </p>
              <Link href="/about" className="main_btn">
                <span className='button_text'>Learn More</span>
                <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
              </Link>
            </div>
          </div>

          <div className={style.images}>
            <figure ref={figure1ref}>
              <Image src="/images/tree.png" width={538} height={698} alt="tree" loading="lazy" />
            </figure>
            <figure ref={figure2ref}>
              <Image src="/images/people.png" width={286} height={297} alt="tree" loading="lazy" />
            </figure>
          </div>
        </div>
      </section>
    </>
  );
};

export default Page;
