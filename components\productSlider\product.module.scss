
@import "@/public/styles/mixins/mixins";
.sliderContainer {
    position: relative;
  }
  
  .slider {
    position: relative;
    width: 100%;
    height: 500px;
    @include max(1060) {
      height: 420px;
    }
    @include max(767) {
      height: 295px;
    }
  }
  
  .slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    aspect-ratio: 16 / 9;
    object-fit: cover;
    transition: transform 0.5s ease-in-out, z-index 0.2s ease-in-out;

  }
  

  .sliderControls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
    max-width: 480px;
    @include max(1060) {
      gap: 15px;
      margin-top: 0px;
      max-width: 100%;
      justify-content: flex-end;
      padding-right: 55px;
    }
    .controlButton{
        width: 64px;
        height: 64px;
        background-color: rgba(0, 0, 0,.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all .3s ease-in-out;
        &:hover{
            background-color: #7F4930;
        }
        @include max(1060) {
          width: 50px;
          height: 50px;
        }
        svg{
          @include max(1060) {
            width: 20px !important;
            height: 20px !important;
          }
        }
    }
    .prev{
        transform: rotate(180deg);
    }
    .pagination{
        position: absolute;
        right: 0;
        bottom: 0;
        color: #FCE1CB;
        font-size: size(16px);
        font-family: var(--font-obsolete);
        @include max(1060) {
          bottom: 15px;
        }
    }
      
}
:global(body.rtl) {
  .sliderControls {
    .prev{
      order: 2;
   }
   .pagination{
     right: auto;
     left: 0;
   }
  }
  
}
