@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.logo_list {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    position: relative;
    z-index: 1;

    @include max(1060) {
        grid-template-columns: repeat(4, 1fr);
    }

    @include max(992) {
        grid-template-columns: repeat(3, 1fr);
    }

    @include max(767) {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    li {
        margin: 0;
        height: 223px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 16px;
        overflow: hidden;

        @include max(1550) {
            height: 200px;
        }

        @include max(1060) {
            height: 180px;
        }

        @include max(767) {
            height: 140px;
            border-radius: 13px;
        }

        .logo_img {
            img {
                height: fit-content;
                width: fit-content;
                max-height: 220px;

                @include max(1550) {
                    max-height: 200px;
                }
                @include max(1300) {
                    max-height: 95px;
                }
                @include max(1060) {
                    max-height: 160px;
                }

            }
        }
    }
}

.acc_sec {
    position: relative;

    .leaf {
        position: absolute;
        right: 15px;
        top: -135px;

        @include max(1060) {
            right: 0;
            top: -105px;
            width: 25%;
        }

        @include max(992) {
            right: -40px;
            top: -100px;
            width: 35%;
        }

        @include max(767) {
            right: -25px;
            top: -73px;
            width: 62%;
        }

    }
}

:global(body.rtl) {
    .acc_sec {
        .leaf {
            right: auto;
            left: 15px;
        }
    }
}