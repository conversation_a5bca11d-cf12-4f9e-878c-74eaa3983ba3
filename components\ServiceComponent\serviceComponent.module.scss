@import "@/public/styles/mixins/mixins";

.service_link_block {
  margin: 0;
  padding: 0;
}

// .service_link_block_ul {
//   display: flex;
//   li {
//     height: 300px;
//     flex: 1 1 50%;
//     transition: flex 0.3s ease-in-out, background-size 0.5s ease-in-out;
//     transition: all 0.3s ease;
//     &:hover {
//       background-size: auto 120% !important;
//       flex: 1 1 25%;
//     }
//   }
// }

.service_ul_new {
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: wrap;

  li {
    list-style: none;
    position: relative;
    align-items: flex-end;
    padding: 2%;
    display: flex;
    flex-wrap: wrap;
    height: 670px;
    flex: 1 1 15%;
    /* Default width as 16.6% */
    position: relative;
    z-index: 5;
    background-position: center center !important;
    background-size: auto 100% !important;
    transition: flex .8s ease-in-out, background-size .8s ease-in-out,background-position .8s ease-in-out;
    cursor: pointer;
    // background-size: auto 100% !important;
    background-position: center left;
    overflow: hidden;
    margin-bottom: 0;
    // @media #{$media-1600} {
    //   height: 400px;
    // }
    @include max(1550){
      height: 600px;
    }
    &:nth-child(1){
      background-position: 10% center !important;
      @include max(1600){
        background-position: 25% center !important;
      }
     
      @include max(767){
        background-position:32% center !important;
       }
    }
    &:nth-child(2){
       background-position: 90% center !important;
       @include max(1600){
        background-position:75% center !important;
       }
       @include max(767){
        background-position:center !important;
       }
    }
    .text {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding-bottom: 30px;

      h3 {
        font-size: 1.5rem;
        color: #fff;
        text-align: center;
        margin: 0;
      }
    }

    img {
      width: 100%;
    }

    @include max(700){
        flex: 1 1 100%;
        height: 400px
    }


    @include max(1024){
         padding: 5%;
    }


    &::after{ display: block; content: ""; z-index: 10; position: absolute; bottom: 0; left: 0; height: 100%; width: 100%; background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.6) 100%
      );}
      &:hover{
        &:nth-child(1),&:nth-child(2){
          background-position: center center !important;
        }
      }
  }

  /* On hover, increase the hovered li's width to 25% */
  li:hover {

   .hover_cnt{  opacity: 1;visibility: visible;height: auto;
  }

    @include min(1366){
  
  
    flex: 1 1 60%;  
    @include max(1600){
      flex: 1 1 65%;
    }
  }
    .service_text_block_main{height:235px;  
      @include max(767) {
        height: auto;
       
      }
    }
    // background-size: auto 120% !important;
  }

  /* Adjust the non-hovered li to share the remaining space */
  &:hover li:not(:hover) {

    @include min(1366){
    flex: 1 1 calc((100% - 15%) / 4);
    /* Adjust width for remaining items */
    }
  }

  h4{   font-size: size(54px); color: #fff; font-weight: 300; margin-bottom:25px;
    @include max(1800){
      font-size: size(39px);
  }
    @include max(1024){
        font-size: size(35px); margin-bottom: 15px;
    }
    @include max(767){
      font-size: size(30px);
  }
}
p{margin-bottom: 25px;  }
  .hover_cnt{ opacity: 0; transition: all .8s ease-in-out;visibility: hidden;
    @include max(767) {
      height: auto;
      opacity: 1;
      visibility: visible;
    }
  }
}


.service_text_block_main{ width: 100%; z-index: 20;height:150px;transition: all .8s ease-in-out;
  @include max(767){
    height: auto;
    padding-bottom: 25px;
  }
}