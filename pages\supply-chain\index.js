import React, { useRef, useEffect, useState } from "react";
import styles from "./standards.module.scss";
import InnerBanner from "@/components/innerBanner/InnerBanner";
import BoxDesc from "@/components/boxDescription/BoxDesc";
import AOS from "aos";
import "aos/dist/aos.css";
import Image from "next/image";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import TextOverImg from "@/components/cards/TextOverImg";
import ImageBg from "@/components/imageBgSet/imageBg";
import Parallax from "@/components/Paralax";
import SingleTitleBanner from "@/components/single_title_banner";


import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getSupplyChain } from "@/utils/lib/server/publicServices";  

gsap.registerPlugin(ScrollTrigger);

const Counter = ({ end, suffix = "", duration = 1500 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);
            let start = 0;
            const increment = end / (duration / 16);
            let frame;
            function animate() {
              start += increment;
              if (start < end) {
                setCount(Math.floor(start));
                frame = requestAnimationFrame(animate);
              } else {
                setCount(end);
              }
            }
            animate();
            return () => cancelAnimationFrame(frame);
          }
        });
      },
      { threshold: 0.5 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [end, duration, hasAnimated]);

  // Always show suffix on the right side
  return (
    <span ref={counterRef}>
      {count}
      {suffix}
    </span>
  );
};

const OurStandards = (props) => {

  const leafHtRef = useRef(null);
  useEffect(() => {
    // Animate .leaf_ht with a float effect
    gsap.fromTo(
      leafHtRef.current,
      { opacity: 0, y: 50, rotate: -20 },
      {
        opacity: 1,
        y: 0,
        rotate: 0,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: leafHtRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play reverse play reverse",
          markers: false,
        },
      }
    );

    return () => {
      if (window.ScrollTrigger) {
        window.ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      }
      gsap.globalTimeline.clear();
    };
  }, []);

  useEffect(() => {
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });
    return () => {
      if (AOS.refreshHard) AOS.refreshHard();
    };
  }, []);

 const router = useRouter();
  const yoastData = props?.pageData?.yoast_head_json;
  if (!props?.pageData) {
        return null;
    }

  return (
    <>
      {yoastData && <Yoast meta={yoastData} />}
         {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_image) && (
            <section id="our-standards">           
                      <InnerBanner
                          bannerBg={props?.pageData?.acf?.banner_image?.url}
                          title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                          content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                      />           
            </section>
        )}
       
      {props &&
          props?.pageData &&
          props?.pageData?.acf && (
          props?.pageData?.acf?.psc_tilte ||                
          props?.pageData?.acf?.supply_chain_listing?.length > 0) && (
      <div
        className={`${styles.container} ${styles.pb_120} ${styles.pt_120}`}
        data-aos="fade-up"
        data-aos-duration="1000"
      >
        <div className={`container`}>
            <div className={`${styles.w_100}`}> 
              {props?.pageData?.acf?.psc_tilte &&
                <div className={`${styles.w_100} ${styles.mb_40}`}>
                  <h2 className="main_title">
                    {parse(props?.pageData?.acf?.psc_tilte)}
                  </h2>
                </div>
              }
          {props?.pageData?.acf?.supply_chain_listing?.length > 0 && ( 
            <ul className={`${styles.two_cl_layout}`}>
              {props?.pageData?.acf?.supply_chain_listing.map((item, idx) => (
                <li
                  data-aos="fade-up"
                  data-aos-duration="1000"
                  key={idx}
                  className={`${styles.mb_50}`}
                >
                  <TextOverImg
                    image={item.list_image?.url}
                    title={item.list_title}
                    description={item.list_content}
                  />
                </li>
              ))}
            </ul>
            )}
          </div>
        </div>
      </div>
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
            props?.pageData?.acf?.fs_title ||
            props?.pageData?.acf?.fs_image ||
            props?.pageData?.acf?.fs_button ||
            props?.pageData?.acf?.fs_pdf_file ||
            props?.pageData?.acf?.fs_content) && (
      <section className={` ${styles.pb_120}`} id="the-farmer-community">
        <ImageBg
            title={props?.pageData?.acf?.fs_title}
            description={props?.pageData?.acf?.fs_content}
            imageSrc={props?.pageData?.acf?.fs_image?.url}
            imageAlt={props?.pageData?.acf?.fs_image?.alt || 'image'}
            downloadLink={props?.pageData?.acf?.fs_pdf_file}
            downloadText={props?.pageData?.acf?.fs_button || 'Download PDF'}
        />       
          </section>
        )}

       {props &&
        props?.pageData &&
        props?.pageData?.acf && (props?.pageData?.acf?.section_title || props?.pageData?.acf?.section_image) && (               
            <SingleTitleBanner
                imageSrc={props?.pageData?.acf?.section_image?.url}
                title={props?.pageData?.acf?.section_title}                    
            />               
        )}
     
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        props?.pageData?.acf?.counter_section &&
        props?.pageData?.acf?.counter_section?.length > 0 && (
      <section className={`${styles.pb_120} ${styles.pt_120}`}> 
        <div className="container"> 
          <ul className={styles.statsList}> 
            {props?.pageData?.acf?.counter_section.map((counter, index) => (
            <li className={styles.statsItem} key={index}> 
              <h3> 
                <Counter end={counter?.counter_number} suffix={counter?.suffix} /> 
              </h3> 
              <p>{counter?.counter_text && parse(counter?.counter_text)}</p> 
            </li> 
              ))}            
          </ul> 
        </div> 
      </section> 
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        props?.pageData?.acf?.left_right_content &&
        props?.pageData?.acf?.left_right_content?.length > 0 && (
      <section> 
        {props?.pageData?.acf?.left_right_content.map((left_right, index) => (
          <div className={`${styles.pb_120} ${styles.pt_120} ${index % 2 === 0 ? "bg_black" : " "}`} key={index}> 
            <div className="container"> 
              <BoxDesc
                imageSrc={left_right?.lr_image?.url}
                title={left_right?.lr_title}
                position={index % 2 === 0 ? "right" : "left"}
                content={left_right?.lr_content}
              />
            </div> 
          </div> 
        ))}        
      </section> 
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.jfc_title ||
          props?.pageData?.acf?.jfc_content ||
          props?.pageData?.acf?.jfc_image ||
          props?.pageData?.acf?.jfc_button) && (
            <section className={`${styles.pb_120}`}> 
              <ImageBg
                title={props?.pageData?.acf?.jfc_title}
                description={props?.pageData?.acf?.jfc_content}
                imageSrc={props?.pageData?.acf?.jfc_image?.url}
                imageAlt={props?.pageData?.acf?.jfc_image?.alt || 'image'}
                buttonLink={props?.pageData?.acf?.jfc_button?.url}
                buttonText={props?.pageData?.acf?.jfc_button?.title}
                fullWidth="true"
              />
            </section> 
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.apc_section_title ||
          props?.pageData?.acf?.apc_section_image) && (
            <section id="peregrina-center"> 
              <SingleTitleBanner
                imageSrc={props?.pageData?.acf?.apc_section_image?.url}
                title={props?.pageData?.acf?.apc_section_title}                    
              /> 
            </section> 
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.iso_title ||
          props?.pageData?.acf?.iso_content ||
          props?.pageData?.acf?.iso_logo ||
          props?.pageData?.acf?.image_1 ||
          props?.pageData?.acf?.image_2 ||
          props?.pageData?.acf?.image_3) && (
          <section className={`${styles.top_sec} ${styles.pt_120}  `}>
            <div
              className={` ${styles.d_flex_wrap} ${styles.standard_set} container`}
            >
              <div className={styles.top_left}>
                <div className={styles.desc}>
                  {props?.pageData?.acf?.iso_logo && (
                    <div className={`${styles.mb_10} ${styles.top_left_img}`}>
                      <Image
                        data-aos="fade-up"
                        data-aos-duration="3000"
                        src={props?.pageData?.acf?.iso_logo?.url}
                        alt="Peregrina Center"
                        width={160}
                        height={170}
                      />
                    </div>
                  )}
                  {props?.pageData?.acf?.iso_title && (
                    <h2
                      className="main_title"
                      data-aos="fade-up"
                      data-aos-duration="3000"
                    >
                      {parse(props?.pageData?.acf?.iso_title)}
                    </h2>
                  )}
                  {props?.pageData?.acf?.iso_content && (
                    <div data-aos="fade-up" data-aos-duration="3000">
                      {parse(props?.pageData?.acf?.iso_content)}
                    </div>
                  )}
              
                </div>
                {props?.pageData?.acf?.image_1 && (
                  <div className={styles.left_image}>
                    <Parallax mediaSrc={props?.pageData?.acf?.image_1?.url} altText="image" />
                  </div>
                )}
              </div>
              <div className={styles.top_right}>
                {props?.pageData?.acf?.image_2 && (
                  <div className={styles.right_big_img}>
                    <Parallax mediaSrc={props?.pageData?.acf?.image_2?.url} altText="image" />
                  </div>
                )}
                {props?.pageData?.acf?.image_3 && (
                  <div className={styles.right_small_img}>
                    <Parallax mediaSrc={props?.pageData?.acf?.image_3?.url} altText="image" />
                  </div>
                )}
              </div>
            </div>
          </section>
        )}

      {props?.pageData?.acf?.card_listing?.length > 0 &&
        props?.pageData?.acf?.card_listing.map((carditeam, index) => (
        <section className={`${styles.pt_120} ${styles.pb_120}`} key={index}>
          <ImageBg
            title={carditeam?.card_title}
            subtitle={carditeam?.card_sub_title}
            description={carditeam?.card_content}
            imageSrc={carditeam?.card_image?.url}
            imageAlt="Contact Us"
          />
        </section>
      ))}      
    </>
  );
};

export default OurStandards;


export async function getStaticProps(locale) {
    const PageData = await getSupplyChain(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}
