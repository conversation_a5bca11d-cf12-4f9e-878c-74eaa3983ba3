import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
    console.log('Track post API called:', req.method, req.body);
    
    if (req.method === 'POST') {
        const { postId, action } = req.body;
        const clientIP = req.headers['x-forwarded-for']?.split(',')[0] || 
                        req.headers['x-real-ip'] || 
                        req.connection.remoteAddress || 
                        '127.0.0.1';
        
        console.log('Tracking:', { postId, action, clientIP });
        
        try {
            const dataPath = path.join(process.cwd(), 'data', 'tracking.json');
            
            // Ensure data directory exists
            const dataDir = path.dirname(dataPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }
            
            // Read existing data
            let trackingData = {};
            if (fs.existsSync(dataPath)) {
                trackingData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            }
            
            // Initialize post data if it doesn't exist
            if (!trackingData[postId]) {
                trackingData[postId] = { views: [], likes: [] };
            }
            
            // Track views (unique per IP per day)
            if (action === 'view') {
                const today = new Date().toDateString();
                const existingView = trackingData[postId].views.find(
                    view => view.ip === clientIP && view.date === today
                );
                
                if (!existingView) {
                    trackingData[postId].views.push({
                        ip: clientIP,
                        date: today,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            // Track likes (unique per IP)
            if (action === 'like') {
                const existingLike = trackingData[postId].likes.find(
                    like => like.ip === clientIP
                );
                
                if (!existingLike) {
                    trackingData[postId].likes.push({
                        ip: clientIP,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            // Save data
            fs.writeFileSync(dataPath, JSON.stringify(trackingData, null, 2));
            
            res.status(200).json({ success: true });
        } catch (error) {
            console.error('API Error:', error);
            res.status(500).json({ success: false, error: error.message });
        }
    } else {
        res.status(405).json({ error: 'Method not allowed' });
    }
}


