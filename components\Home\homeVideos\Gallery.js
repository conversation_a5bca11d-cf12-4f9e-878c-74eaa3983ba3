"use client";
import React, { useState, useEffect, useRef } from 'react';
import Image from "next/image";
import { useSwipeable } from "react-swipeable";
import { useInView } from "react-intersection-observer";
import style from "./Gallery.module.scss";
import Icons from "@/public/Icons";
import Parallax from "@/components/Paralax";
import splitTextAnimation from "@/components/splitTextAnimation";

const items = [
  { type: "video", src: "/videos/video_1.mp4", poster: "/images/img1.jpg", id: "item1", title: "The Region1", description: "AlUla is an eloquent symbol of this richness. For centuries, its oasis has been a crossing point for caravans on the Incense trade route, which connected Asia, Africa and Europe, transiting spices, myrrh, cotton, ebony and silk." },
  { type: "image", src: "/images/img2.jpg", id: "item2", title: "The Region2", description: "AlUla is an eloquent symbol of this richness. For centuries, its oasis has been a crossing point for caravans on the Incense trade route, which connected Asia, Africa and Europe, transiting spices, myrrh, cotton, ebony and silk." },
  { type: "video", src: "/videos/video_main.mp4", id: "item3", poster: "/images/img3.jpg", title: "The Region3", description: "AlUla is an eloquent symbol of this richness. For centuries, its oasis has been a crossing point for caravans on the Incense trade route, which connected Asia, Africa and Europe, transiting spices, myrrh, cotton, ebony and silk." },
  { type: "image", src: "/images/img4.jpg", id: "item4", title: "The Region4", description: "AlUla is an eloquent symbol of this richness. For centuries, its oasis has been a crossing point for caravans on the Incense trade route, which connected Asia, Africa and Europe, transiting spices, myrrh, cotton, ebony and silk." },
  { type: "image", src: "/images/img5.jpg", id: "item5", title: "The Region5", description: "AlUla is an eloquent symbol of this richness. For centuries, its oasis has been a crossing point for caravans on the Incense trade route, which connected Asia, Africa and Europe, transiting spices, myrrh, cotton, ebony and silk." },
];

const Gallery = () => {
  const [expandedItem, setExpandedItem] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showModalContent, setShowModalContent] = useState(false);
  const sectionRef = useRef(null);
  splitTextAnimation(sectionRef, "trigger_title");
  
  // Detect when the gallery is in view
  const { ref, inView } = useInView({
    triggerOnce: false,  // Keep triggering when entering/leaving
    threshold: 0.2,      // Fires when 20% of the element is visible
  });

  const handleClick = (item, index) => {
    setExpandedItem(item.id);
    setCurrentIndex(index);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setExpandedItem(null);
  };

  const handleNext = () => {
    const nextIndex = (currentIndex + 1) % items.length;
    setExpandedItem(items[nextIndex].id);
    setCurrentIndex(nextIndex);
  };

  const handlePrev = () => {
    const prevIndex = (currentIndex - 1 + items.length) % items.length;
    setExpandedItem(items[prevIndex].id);
    setCurrentIndex(prevIndex);
  };

  const swipeHandlers = useSwipeable({
    onSwipedLeft: handleNext,
    onSwipedRight: handlePrev,
    preventDefaultTouchmoveEvent: true,
    trackMouse: true,
  });
  useEffect(() => {
    if (modalOpen) {
      // Delay the modal content appearance by 500ms
      const timer = setTimeout(() => {
        setShowModalContent(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setShowModalContent(false);
    }
  }, [modalOpen]);
  return (
    <div
      ref={ref}
      className={`${style.grid_parent} ${modalOpen ? style.parent_grid_modal : ""} ${inView ? style.inViewClass : ""}`}
    >
      <section className={`${style.grid_image} ${modalOpen ? style.modalOpen : ""}`}>
        {items.map((item, index) => (
          <div
            key={index}
            className={`${style.item} ${style[item.id]} 
              ${expandedItem === item.id ? style.expanded : expandedItem ? style.moved : ""}`}
            onClick={() => handleClick(item, index)}
          >
            {item.type === "image" ? (
              <Parallax mediaSrc={item.src} altText={`img${index + 1}`} />
            ) : (
              <Parallax mediaSrc={item.src} isVideo={true} />
            )}

            {/* Show title only on the center item */}
            {!modalOpen && item.id === "item3" && (
              <div className={style.centerText} ref={sectionRef}>
                <h2 className={`${style.centerTitle} main_title trigger_title`}>{item.title}</h2>
              </div>
            )}
          </div>
        ))}

        {/* Modal for expanded view */}
        {modalOpen && (
          <div className={style.modal} {...swipeHandlers}>
            <div className={style.close} onClick={closeModal}>
              <Image src='/images/close_btn.png' width={26} height={26} alt="close"/>
            </div>
            <div className={style.prev} onClick={handlePrev}>
              <Icons size={25} color="#fff" icon='Right-arw' />
            </div>
            <div className={`${style.modalContent} ${showModalContent ? style.showContent : ""}`}>
              <h2 className='main_title'>{items[currentIndex].title}</h2>
              <p className={style.modalDescription}>{items[currentIndex].description}</p>
            </div>
            <div className={style.next} onClick={handleNext}>
              <Icons size={25} color="#fff" icon='Right-arw' />
            </div>
          </div>
        )}
      </section>
    </div>
  );
};

export default Gallery;
