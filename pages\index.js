import Head from "next/head";
import Image from "next/image";
import styles from '@/public/styles/Home.module.scss'
import <PERSON>eg<PERSON> from "@/components/Home/PeregrinaTree/Peregrina";
import Instagram from "@/components/Home/Instagram/Insta";
import AboutSec from "@/components/Home/homeAbout/AboutSec";
import HomeServices from "@/components/Home/homeServices/HomeServices";
import HomeNews from "@/components/Home/homeNews/HomeNews";
import Gallery from "@/components/Home/homeVideos/Gallery";
import AnimatedTitle from "@/components/anim";
import HomeBanner from "@/components/Home/homeBanner/HomeBanner";
import ArchaeologicalSlider from "@/components/ArchaeologicalSlider/ArchaeologicalSlider";
import ServiceComponent from "@/components/ServiceComponent/ServiceComponent";
import ImageBg from "@/components/imageBgSet/imageBg";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getHome } from "@/utils/lib/server/publicServices";

const slidesData = [
  {
    id: 1,
    title: "Land of Ancient Civilizations",
    text: 'AlUla, a land of ancient civilizations with over 200,000 years of human history, was home to the influential kingdoms of Dadan and Lihyan, serving as a pivotal crossroads on the incense-trading route connecting southern Arabia to Egypt and beyond',
    image: '/images/civil_pic_01.jpg'
  },
  {
    id: 2,
    title: "Desert of Wonders",
    text: 'From the Harrat canyons to the wadis and sandy desert, AlUla unveils a landscape of golden dunes, red valleys, volcanic plateaus, and natural arches.',
    image: '/images/desert.jpg'
  },
  {
    id: 3,
    title: "The Oasis",
    text: 'Framed by towering cliffs and winding valleys, AlUla Oasis pulses with life, nurturing rich biodiversity and sustaining vibrant ecosystems across generations.',
    image: '/images/oasis.jpg'
  },
];

export default function Home(props) {

  const router = useRouter();
  const yoastData = props?.pageData?.yoast_head_json;
  if (!props?.pageData) {
          return null;
  }

  return (
    <>
      
      {yoastData && <Yoast meta={yoastData} />}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.banner_video ||
          props?.pageData?.acf?.video_poster ||
          props?.pageData?.acf?.banner_title) && (
          <HomeBanner
            banner_video={props?.pageData?.acf?.banner_video?.url}
            video_poster={props?.pageData?.acf?.video_poster?.url}
            banner_title={props?.pageData?.acf?.banner_title}
          />
        )}
            

      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.mainTitle ||
          props?.pageData?.acf?.description ||
          props?.pageData?.acf?.button ||
          props?.pageData?.acf?.background_image ||
          props?.pageData?.acf?.background_image_2) && (
          <div className={`${styles.pt_120} ${styles.preregrina_sec} container`}>
            <Peregrina
              title={props?.pageData?.acf?.mainTitle}
              description={props?.pageData?.acf?.description}
              buttonLink={props?.pageData?.acf?.button}
              image_1={props?.pageData?.acf?.background_image?.url}
              image_2={props?.pageData?.acf?.background_image_2?.url}
            />
          </div>
        )}

      <div className={` `}>
        <div className={`${styles.home_abt_sec} ${styles.pb_120} ${styles.pt_120}`}>
          <div className={styles.abt_leaf_1}>
            <Image src='/images/leaf_1.png' width={386} height={551} alt="image" />
            {/* <Parallax mediaSrc='/images/leaf_1.png' altText='image' /> */}
          </div>

          <div className={`${styles.container}  container`}>
            <AboutSec
              backgroundImage="/images/homeAbt.jpg"
              subtitle="Innovation at"
              mainTitle="The Peregrina Center"
              description="From our ISO 9001:2015 certified laboratory, we craft exceptional ingredients rich in exosomes and ceramides, driven by innovation, advanced R&D, and the expertise of our dedicated local team."
              buttonText="Learn More"
              buttonLink="/supply-chain/#peregrina-center"
              iconName="Right-arw"
            />
          </div>
          <div className={styles.abt_leaf}>
            <Image src='/images/leaf_3.png' width={259} height={306} alt="image" />
            {/* <Parallax mediaSrc='/images/leaf_3.png' altText='image' /> */}
          </div>
        </div>
      </div>

      <ServiceComponent />

      {/* <HomeServices /> */}
      <div className={`${styles.homeVideoSec} ${styles.pt_120} ${styles.pb_120}`}>
        <div className="container">
          <h2 className="main_title">AlUla Region</h2>
          <ArchaeologicalSlider slides={slidesData} />
        </div>
        <div className={styles.video_leaf}>
          {/* <Parallax mediaSrc='/images/hm_pic.png' altText='image' /> */}
          <Image src='/images/hm_pic.png' width={555} height={521} alt="image" />
        </div>
      </div>
      <div className={`${styles.pb_120}`}>

        <ImageBg
          title="Vision 2030 Alignment"
          description={`Contributing to Saudi Arabia’s goals of environmental conservation, sustainable agriculture, and economic diversification.`}
          imageSrc="/images/vision_banner.jpg"
          imageAlt="How it all began"
          logoSrc="/images/vision_logo.png"
        />

      </div>
      <div className={styles.pb_120}>
        <HomeNews />
      </div>
      <Instagram />
    </>
  );
}

export async function getStaticProps(locale) {
  const PageData = await getHome(locale.locale);
  // console.log('PageData:', PageData); // Add this line
  return {
    props: {
      pageData: PageData || null,
    },
    revalidate: 10,
  };
}