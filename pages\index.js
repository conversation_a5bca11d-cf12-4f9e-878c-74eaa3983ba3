import Head from "next/head";
import Image from "next/image";
import styles from '@/public/styles/Home.module.scss'
import Peregrina from "@/components/Home/PeregrinaTree/Peregrina";
import Instagram from "@/components/Home/Instagram/Insta";
import AboutSec from "@/components/Home/homeAbout/AboutSec";
import HomeServices from "@/components/Home/homeServices/HomeServices";
import HomeNews from "@/components/Home/homeNews/HomeNews";
import Gallery from "@/components/Home/homeVideos/Gallery";
import AnimatedTitle from "@/components/anim";
import HomeBanner from "@/components/Home/homeBanner/HomeBanner";
import ArchaeologicalSlider from "@/components/ArchaeologicalSlider/ArchaeologicalSlider";
import ServiceComponent from "@/components/ServiceComponent/ServiceComponent";
import ImageBg from "@/components/imageBgSet/imageBg";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getHome,getInsightsPosts } from "@/utils/lib/server/publicServices";


export default function Home(props) {

  const router = useRouter();
  const yoastData = props?.pageData?.yoast_head_json;
  if (!props?.pageData) {
          return null;
  }

  return (
    <>
      
      {yoastData && <Yoast meta={yoastData} />}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.banner_video ||
          props?.pageData?.acf?.video_poster ||
          props?.pageData?.acf?.banner_title) && (
          <HomeBanner
            banner_video={props?.pageData?.acf?.banner_video?.url}
            video_poster={props?.pageData?.acf?.video_poster?.url}
            banner_title={props?.pageData?.acf?.banner_title}
          />
        )}
            

      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.mainTitle ||
          props?.pageData?.acf?.description ||
          props?.pageData?.acf?.button ||
          props?.pageData?.acf?.background_image ||
          props?.pageData?.acf?.background_image_2) && (
          <div className={`${styles.pt_120} ${styles.preregrina_sec} container`}>
            <Peregrina
              title={props?.pageData?.acf?.mainTitle}
              description={props?.pageData?.acf?.description}
              buttonLink={props?.pageData?.acf?.button}
              image_1={props?.pageData?.acf?.background_image?.url}
              image_2={props?.pageData?.acf?.background_image_2?.url}
            />
          </div>
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.innovation_title ||
          props?.pageData?.acf?.innovation_content ||
          props?.pageData?.acf?.innovation_sub_title ||
          props?.pageData?.acf?.innovation_image ||
          props?.pageData?.acf?.innovation_button) && (
         
          <div className={` `}>
            <div className={`${styles.home_abt_sec} ${styles.pb_120} ${styles.pt_120}`}>
              <div className={styles.abt_leaf_1}>
                <Image src='/images/leaf_1.png' width={386} height={551} alt="image" />                
              </div>
              <div className={`${styles.container}  container`}>
                <AboutSec
                  backgroundImage={props?.pageData?.acf?.innovation_image?.url}
                  subtitle={props?.pageData?.acf?.innovation_sub_title}
                  mainTitle={props?.pageData?.acf?.innovation_title}
                  description={props?.pageData?.acf?.innovation_content}
                  buttonText={props?.pageData?.acf?.innovation_button?.title}
                  buttonLink={props?.pageData?.acf?.innovation_button?.url}
                  iconName="Right-arw"
                />
              </div>
              <div className={styles.abt_leaf}>
                <Image src='/images/leaf_3.png' width={259} height={306} alt="image" />                
              </div>
            </div>
          </div>
        )}

        {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.our_actives_sec ||
          props?.pageData?.acf?.our_actives_sec?.length > 0) && (
          <ServiceComponent links={props?.pageData?.acf?.our_actives_sec} />
        )}
      
       {/* <HomeServices /> */}
{props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.alula_region_title ||
          props?.pageData?.acf?.alula_region?.length > 0) && (
    
      <div className={`${styles.homeVideoSec} ${styles.pt_120} ${styles.pb_120}`}>
        <div className="container">
          {props?.pageData?.acf?.alula_region_title && (
            <h2 className="main_title">
              {parse(props?.pageData?.acf?.alula_region_title)}
            </h2>
          )}
          {props?.pageData?.acf?.alula_region?.length > 0 && (
            <ArchaeologicalSlider slides={props?.pageData?.acf?.alula_region} />
          )}         
        </div>
        <div className={styles.video_leaf}>
          {/* <Parallax mediaSrc='/images/hm_pic.png' altText='image' /> */}
          <Image src='/images/hm_pic.png' width={555} height={521} alt="image" />
        </div>
      </div>
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.vision_title ||
          props?.pageData?.acf?.vision_content ||
          props?.pageData?.acf?.vision_image ||
          props?.pageData?.acf?.vision_logo) && (
          <div className={`${styles.pb_120}`}>
            <ImageBg
              title={props?.pageData?.acf?.vision_title}
              description={props?.pageData?.acf?.vision_content}
              imageSrc={props?.pageData?.acf?.vision_image?.url}
              imageAlt={props?.pageData?.acf?.vision_image?.alt || 'How it all began'}
              logoSrc={props?.pageData?.acf?.vision_logo?.url}
            />
          </div>
        ) }
     
{props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.news_title ||
          props?.pageData?.acf?.news_button ||
          props?.newsData?.length > 0) && (
          <div className={`${styles.pb_120}`}>
            <HomeNews
              news_title={props?.pageData?.acf?.news_title}
              news_button={props?.pageData?.acf?.news_button}
              newsData={props?.newsData} />
          </div>
        ) }
      
      <Instagram
        instagram_text={props?.pageData?.acf?.instagram_text}
        instagram_link={props?.pageData?.acf?.instagram_link}
      />
    </>
  );
}

export async function getStaticProps(locale) {
  const PageData = await getHome(locale.locale);
  const InsightsPostsData = await getInsightsPosts(locale.locale);
  // console.log('PageData:', PageData); // Add this line

  
  let InsightsPosts = [];
  if (PageData && PageData?.acf && Array.isArray(PageData?.acf?.news)) {
    InsightsPosts = PageData?.acf?.news;
  }

  // Format Insights Posts for use in the component, excluding current post
  let InsightsPostsRelation = [];
  if (InsightsPosts?.length > 0) {
    InsightsPostsRelation = InsightsPosts
    .map((id) => InsightsPostsData?.find((post) => post.id === id))
    .filter(Boolean);
    // .filter((post) => post.id !== InsightsData[0]?.id); // <-- REMOVE or COMMENT THIS LINE
  }
 
  
  return {
    props: {
      pageData: PageData || null,
      newsData: InsightsPostsRelation || [],
    },
    revalidate: 10,
  };
}