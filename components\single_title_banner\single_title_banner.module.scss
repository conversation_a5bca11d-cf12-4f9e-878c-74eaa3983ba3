@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";
.banner_container {
  width: 100%;
  height: 880px;
  position: relative;
  display: flex;
  align-items: flex-end;
  padding-bottom: 65px;
  @include max(1550) {
    height: 680px;
  }
  @include max(1300) {
    height: 650px;
    padding-bottom:50px;
  }
  @include max(1060) {
    height: 500px;  
    }
    @include max(767) {
        height: 400px;  
        padding-bottom:20px;

    }
  video{
    height: 100%;
    width: 100%;
    object-fit: cover;
    display: block;
    position: absolute;
    left: 0;
    top: 0;

}

  .container {
    position: relative;
    z-index: 2;
    .main_title {
      color: #fff; 
      text-transform: capitalize;
      margin-bottom: 0;
    }
  }
  &::after{
    content: "";
    display: block;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.0) 100%);
    position: absolute;
    left: 0;
    bottom: 0;
  }
  // .with_logo{
  //   display: flex;
  //   flex-wrap: wrap;
  //   align-items: center;
  //   justify-content: space-between;
  // }
  .logo_wrapper{
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 35px;
    @include max(767) {
      margin-bottom:20px;
      order: 1;
    }
    img{
      border-radius: 5px;
    }
  }
  .title_wrapper{
    @include max(767) {
      order: 2;
      width: 100%;
    }
  }
  p{
    font-size: size(18px);
    margin-top: 15px;

    @include max(1550) {
      font-size: size(17px);
      margin-top: 10px;
    }
    @include max(1300) {
      font-size: size(14px);
    }
  }
} 