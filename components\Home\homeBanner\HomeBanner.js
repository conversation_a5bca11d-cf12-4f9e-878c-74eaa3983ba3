import React, { useState, useEffect } from "react";
import styles from "./homeBanner.module.scss";
import AnimatedTitle from "@/components/anim";
import Image from "next/image";
import gsap from "gsap";

const HomeBanner = () => {
  const [showLoader, setShowLoader] = useState(true); // Show preloader by default
  const [animationEnded, setAnimationEnded] = useState(false);
  const [logoActive, setLogoActive] = useState(false);

  // useEffect(() => {
  //   if (showLoader) {
  //     document.documentElement.classList.add('html_loader');
  //   } else {
  //     document.documentElement.classList.remove('html_loader');
  //   }
  // }, [showLoader]);

  // useEffect(() => {
  //   if (fadeInVideo) {
  //     document.body.classList.add('video_active');
  //   } else {
  //     document.body.classList.remove('video_active');
  //   }
  // }, [fadeInVideo]);

  // 🌐 Add or remove class on <html> during loader
  // useEffect(() => {
  //   if (showLoader) {
  //     document.documentElement.classList.add('html_loader');
  //   } else {
  //     document.documentElement.classList.remove('html_loader');
  //   }
  // }, [showLoader]);

  // useEffect(() => {
  //   if (logoBlockRef.current) {
  //     const timeout = setTimeout(() => {
  //       gsap.fromTo(
  //         logoBlockRef.current,
  //         { scale: 1 },
  //         // { scale: 100, duration: 15, ease: 'power1.inOut' }
  //         { scale: 80, duration:10, ease: 'power3.out' }
  //       );
  //       if (loderMainRef.current) {
  //         gsap.to(loderMainRef.current, {
  //           opacity: 0,
  //           duration: 1,
  //           ease: 'power2.out',
  //           delay: 0,
  //           onComplete: () => setAnimationEnded(true)
  //         });
  //       }
  //     }, 3500);
  //     return () => clearTimeout(timeout);
  //   }
  // }, []);

  // useEffect(() => {
  //   if (logoImgRef.current) {
  //     gsap.to(logoImgRef.current, {
  //       opacity: 0,
  //       duration: 1,
  //       delay: 2,
  //       ease: 'power2.out'
  //     });
  //   }
  // }, []);

  useEffect(() => {
    // Add class to body and html on mount
    document.body.classList.add("loader-active");
    document.documentElement.classList.add("loader-active");
    return () => {
      document.body.classList.remove("loader-active");
      document.documentElement.classList.remove("loader-active");
    };
  }, []);

  useEffect(() => {
    if (animationEnded) {
      document.body.classList.remove("loader-active");
      document.documentElement.classList.remove("loader-active");
    }
  }, [animationEnded]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLogoActive(true);
    }, 4000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      const tl = gsap.timeline();

      tl.to(
        ".loder_mian",
        {
          scale: 100,
          duration: 20,
          ease: "none",
        },
        0
      ) // start scale at 0

        .to(
          ".loder_mian",
          {
            opacity: 0,
            duration: 1.5,
            ease: "power1.out",

            onComplete: () => {
              gsap.set(".loder_mian", { display: "none" });
              document.body.classList.remove("loader-active");
              document.documentElement.classList.remove("loader-active");
              setAnimationEnded(true);
            },
          },
          2
        ); // start opacity animation at 2 seconds from start
    }, 3500); // delay the whole animation by 5 seconds

    return () => clearTimeout(timer);
  }, []);



  return (
    <>
      <div className={`${styles.loder_mian} loder_mian`}>
        <div className={styles.logo_block_main_top}></div>
        <div className={styles.logo_block_main_center}>
          <div className={styles.logo_block_main_center_left}></div>
          <div className={styles.logo_block_main_center_center}>
            <div className={styles.logo_block_mask}>
              <Image
                src="/images/alula-logo_mask.svg"
                alt="Loader SVG"
                width={200}
                height={200}
              />
            </div>
            <div
              className={`logo_block_ani${logoActive ? " active_logo" : ""}`}
            >
              <Image
                src="/images/alula-logo-animation.svg"
                alt="Loader SVG"
                width={200}
                height={200}
              />
            </div>
          </div>
          <div className={styles.logo_block_main_center_right}></div>
        </div>

        <div className={styles.logo_block_main_bottom}></div>

        {/* <div className={styles.logo_block} ref={logoBlockRef}>
        <div className={styles.p_relative}>
         <div ref={logoImgRef} className={`${styles.logo_img} logo_img`}>

        </div>
        </div>
        </div> */}
      </div>

      <div className={styles.wrapper}>
        <div className={styles.main_banner}>
          <div className={`${styles.container} container`}>
            <video
              className={`${styles.bgVideo}`}
              muted
              loop
              playsInline
              // preload="auto"
              autoPlay
              poster="/images/main_banner.jpg"
            >
              <source src="/videos/video_main.mp4" type="video/mp4" />
            </video>
            <div className={styles.banner_title}>
              <AnimatedTitle
                title="Desert of Arabia’s </br>Heritage for </br>Longevity Beauty"
                tag="h1"
                className="main_title"
              />
            </div>
          </div>  
        </div>
      </div>
    </>
  );
};

export default HomeBanner;
