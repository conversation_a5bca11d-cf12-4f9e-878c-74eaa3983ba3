import React, { useState, useEffect } from "react";
import styles from "./homeBanner.module.scss";
import AnimatedTitle from "@/components/anim";
import Image from "next/image";
import gsap from "gsap";
import parse from "html-react-parser";

const HomeBanner = ({ banner_video, video_poster, banner_title }) => {
  const [showLoader, setShowLoader] = useState(true); // Show preloader by default
  const [animationEnded, setAnimationEnded] = useState(false);
  const [logoActive, setLogoActive] = useState(false);


  useEffect(() => {
    // Add class to body and html on mount
    document.body.classList.add("loader-active");
    document.documentElement.classList.add("loader-active");
    return () => {
      document.body.classList.remove("loader-active");
      document.documentElement.classList.remove("loader-active");
    };
  }, []);

  useEffect(() => {
    if (animationEnded) {
      document.body.classList.remove("loader-active");
      document.documentElement.classList.remove("loader-active");
    }
  }, [animationEnded]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLogoActive(true);
    }, 4000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      const tl = gsap.timeline();

      tl.to(
        ".loder_mian",
        {
          scale: 100,
          duration: 20,
          ease: "none",
        },
        0
      ) // start scale at 0

        .to(
          ".loder_mian",
          {
            opacity: 0,
            duration: 1.5,
            ease: "power1.out",

            onComplete: () => {
              gsap.set(".loder_mian", { display: "none" });
              document.body.classList.remove("loader-active");
              document.documentElement.classList.remove("loader-active");
              setAnimationEnded(true);
            },
          },
          2
        ); // start opacity animation at 2 seconds from start
    }, 3500); // delay the whole animation by 5 seconds

    return () => clearTimeout(timer);
  }, []);



  return (
    <>
      <div className={`${styles.loder_mian} loder_mian`}>
        <div className={styles.logo_block_main_top}></div>
        <div className={styles.logo_block_main_center}>
          <div className={styles.logo_block_main_center_left}></div>
          <div className={styles.logo_block_main_center_center}>
            <div className={styles.logo_block_mask}>
              <Image
                src="/images/alula-logo_mask.svg"
                alt="Loader SVG"
                width={200}
                height={200}
              />
            </div>
            <div
              className={`logo_block_ani${logoActive ? " active_logo" : ""}`}
            >
              <Image
                src="/images/alula-logo-animation.svg"
                alt="Loader SVG"
                width={200}
                height={200}
              />
            </div>
          </div>
          <div className={styles.logo_block_main_center_right}></div>
        </div>

        <div className={styles.logo_block_main_bottom}></div>      
      </div>

      <div className={styles.wrapper}>
        <div className={styles.main_banner}>
          <div className={`${styles.container} container`}>
            <video
              className={`${styles.bgVideo}`}
              muted
              loop
              playsInline
              // preload="auto"
              autoPlay
              poster={video_poster}
            >
              <source src={banner_video} type="video/mp4" />
            </video>
            <div className={styles.banner_title}>
              <AnimatedTitle
                title={banner_title}
                tag="h1"
                className="main_title"
              />
            </div>
          </div>  
        </div>
      </div>
    </>
  );
};

export default HomeBanner;
