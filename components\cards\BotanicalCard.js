import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';

const BotanicalCard = ({ icon, title, subTitle, parentClass }) => {
    return (
        <div className={`${styles.botanical_card} ${!icon ? styles.no_icon : ''} ${parentClass ? styles[parentClass] : ''}`}>
            {icon && (
                <div className={styles.icon}>
                    <Image src={icon} width={75} height={75} alt='image' />
                </div>
            )}
            <div className={styles.desc}>
                {title && <h3>{title}</h3>}
                {subTitle && <h4>{subTitle}</h4>}
            </div>
        </div>
    );
};

export default BotanicalCard;
