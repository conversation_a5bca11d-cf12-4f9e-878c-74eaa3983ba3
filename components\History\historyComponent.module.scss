@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.history_top{
    position: relative;
    .leaf{
        position: absolute;
        top: -175px;
        left: 0;
        @include max(992) {
            top: -105px;
            width: 35%;
        }
        @include max(767) {
            top: -60px;
            width: 45%;
        }
    }
    .container{
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    .left_sec{
        width: 49%;
        @include max(1060) {
            width: 100%;
        }
        p{
            margin-bottom:20px;
        }
    }
    .right_sec{
        width: 48%;
        overflow: hidden;
        border-radius: 20px;
        @include max(1060) {
            width: 100%;
            margin-top: 30px;
        }
        img{
            border-radius: 20px;
            height: 100%;
            width: 100%;
            object-fit: cover;
            @include max(992) {
                border-radius:12px;
            }
        }
    }
    .box_list{
        display: grid;
        grid-template-columns: repeat(2,1fr);
        gap: 30px;
        padding-top: 50px;
        @include max(767) {
            gap: 20px;
            padding-top: 30px;
        }
    }
}
.tree_root_sec{
    background-repeat: no-repeat;
    background-size: cover;
}

:global(body.rtl) {
    .history_top{
        .leaf{
            left: auto;
            right: 0;
            transform: scaleX(-1);
        }
    }
}

.left_desc{
    p{ font-size:size(18px);
        @include max(1300) {
            font-size: size(15px);
        }
        @include max(767) {
            font-size: size(15px);
        }
    }
}