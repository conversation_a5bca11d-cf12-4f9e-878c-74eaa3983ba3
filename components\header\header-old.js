import React, { useEffect, useRef, useState } from 'react';
import styles from './header.module.scss';
import Link from 'next/link';
import Image from 'next/image';
import Icons from '@/public/Icons';
import Lenis from '@studio-freight/lenis';
import { useRouter } from 'next/router';

const menuItems = [
    {
        title: 'About Us', submenu: [
            { title: 'Our Story', href: '/about' },
            { title: 'Mission and Values', href: '/about/mission-and-values' },
            { title: 'Leadership team', href: '/about/leadership-team' },
            { title: 'Partners', href: '/about/partners' },
            { title: 'Acceditations and Certification', href: '/about/acceditations-and-certification' },
        ],
        image: "/images/header_img.jpg",
    },
    {
        title: 'The Peregrina Tree',
        href: 'the-peregrina-tree',
        submenu: [
            { title: 'History', href: '/the-peregrina-tree/history' },
            { title: 'Culture & Heritage', href: '/the-peregrina-tree/culture-and-heritage' },
            { title: 'Characteristics', href: '/the-peregrina-tree/characteristics' }
        ],
        image: "/images/header_img_2.jpg",
    },
    {
        title: 'Supply Chain', submenu: [
            { title: 'Our Standards', href: '/supply-chain/our-standards' },
            { title: 'Farms Certifications', href: '/supply-chain/farms-certifications' },
            { title: 'Our Farmers Community', href: '/supply-chain/our-farmers-community' },
            { title: 'Peregrina Center', href: '/supply-chain/peregrina-center' },
            { title: 'R&D', href: '/supply-chain/r-and-d' },
        ],
        image: "/images/header_img_3.jpg",
    },
    {
        title: 'Our Actives',href: '/our-actives', submenu: [
            { title: 'Virgin Peregrina Oil', href: '/virgin-peregrina-oil' },
        ],
        image: "/images/header_img_4.jpg",
    },
    {
        title: 'Hotels & Spas',href:'/hotels-and-spas',
        submenu: [
            { title: 'Hotel Toiletries', href: '/hotels-and-spas/hotel-toiletries' },
            { title: 'Luxury Hotels', href: '#.' },
            { title: 'Wellness Centers', href: '#.' }
        ],
        image: "/images/header_img.jpg",
    },
    {
        title: 'Sustainability', submenu: [
            { title: 'Agricultural Practices', href: '/agricultural-practices' },
        ],
        image: "/images/header_img.jpg",
    },
];

const Header = () => {
    const [activeMenu, setActiveMenu] = useState(null);
    const menuRef = useRef(null);
    const [isSticky, setIsSticky] = useState(false);
    const headerRef = useRef(null);
    const router = useRouter();
    const isHomePage = router.pathname === '/';

    useEffect(() => {
        const lenis = new Lenis({
            duration: 1.5,
            easing: (t) => 1 - Math.pow(1 - t, 4),
            smooth: true,
            smoothTouch: true,
        });

        const raf = (time) => {
            lenis.raf(time);
            requestAnimationFrame(raf);
        };
        requestAnimationFrame(raf);

        return () => {
            lenis.destroy();
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setActiveMenu(null);
                document.body.classList.remove('menu-open');
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);


    const handleMenuClick = (index) => {
        if (activeMenu === index) {
            setActiveMenu(null);
            document.body.classList.remove('menu-open'); 
        } else {
            setActiveMenu(index);
            document.body.classList.add('menu-open'); 
        }

        const submenuLength = menuItems[index].submenu ? menuItems[index].submenu.length : 0;
        document.documentElement.style.setProperty('--submenu-items', submenuLength);
    };
    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 100) { 
                setIsSticky(true);
            } else {
                setIsSticky(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);
    return (
        <header ref={headerRef}
            className={`${styles.header} ${!isHomePage ? styles.fixedHeader : ''} ${!isHomePage && isSticky ? styles.sticky : ''
                }`}>
            <div className={`${styles.container} container`}>
                <div className={styles.top_header}>
                    <ul className={styles.header_social}>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Instagram" /></Link></li>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="twitter" /></Link></li>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Facebook" /></Link></li>
                    </ul>
                    <Link href='/' className={styles.logo}>
                        <Image src='/images/main_logo.png' width={303} height={100} alt='logo' />
                    </Link>
                    <ul className={styles.top_contact}>
                        <li><Link href='/news'>News</Link></li>
                        <li><Link href='/contact'>Contact Us</Link></li>
                        <li><Link href='#.'>عربي</Link></li>
                    </ul>
                </div>
                <div className={styles.main_header}>
                    <div className={styles.toggle_line}>
                        <span></span>
                        <span></span>
                    </div>
                    <div className={styles.main_menu} ref={menuRef}>
                        <ul className={styles.main_ul}>
                            {menuItems.map((item, index) => (
                                <li key={index} className={`${item.submenu ? styles.has_submenu : ''} ${activeMenu === index ? styles.active : ''}`} onClick={() => handleMenuClick(index)}>
                                    {item.href ? (
                                        <>
                                            <Link href={item.href} className={styles.menu_item}>
                                                {item.title}
                                            </Link>
                                            {item.submenu && <span className={styles.submenu_arw}><Icons size={13} color="#FCE1CB" icon="down-arw" /></span>}
                                        </>
                                    ) : (
                                        <>
                                            <span className={styles.menu_item} onClick={() => handleMenuClick(index)}>
                                                {item.title}
                                            </span>
                                            {item.submenu && <span className={styles.submenu_arw}><Icons size={13} color="#FCE1CB" icon="down-arw" /></span>}
                                        </>
                                    )}
                                    {item.submenu && (
                                        <div className={styles.submenu}>
                                            <div className={styles.submenu_contents}>
                                                <div className={styles.submenu_list}>
                                                    <h3>{item.title}</h3>
                                                    <ul>
                                                        {item.submenu.map((subItem, subIndex) => (
                                                            <li key={subIndex}>
                                                                <Link href={subItem.href}>{subItem.title}</Link>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                                {item.image && (
                                                    <div className={styles.submenu_img}>
                                                        <Image src={item.image} width={458} height={229} alt={item.title} />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </li>
                            ))}

                        </ul>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
