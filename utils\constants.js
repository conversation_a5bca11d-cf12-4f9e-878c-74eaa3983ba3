import News from "@/pages/news";

const CONSTANTS = {

  Themeoptions: {
    ThemeoptionsApi: `wp-json/options/v2/alloptions`,
  },
  // Home page
  HomePage: {
    HomeApi: `wp-json/wp/v2/pages/2/`,
  },
  
  HomeArPage: {
    HomeArApi: `wp-json/wp/v2/pages/652/`,
  },

  // About Page
  AboutPage: {
    AboutApi: `wp-json/wp/v2/pages/8/`,
  },
  
  AboutArPage: {
    AboutArApi: `wp-json/wp/v2/pages/652/`,
  },

  // The Peregrina Tree page
  PeregrinaCenterpage: {
    PeregrinaCenterApi: `wp-json/wp/v2/pages/262/`,
  },
  
  PeregrinaCenterArPage: {
    PeregrinaCenterArApi: `wp-json/wp/v2/pages/652/`,
  },

   // Supply Chain page
  SupplyChainpage: {
    SupplyChainApi: `wp-json/wp/v2/pages/314/`,
  },
  
  SupplyChainArPage: {
    SupplyChainArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Our Actives page
  ActivesPage: {
    ActivesApi: `wp-json/wp/v2/pages/458/`,
  },
  
  ActivesArPage: {
    ActivesArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Our Actives page
  ActivesPage: {
    ActivesApi: `wp-json/wp/v2/pages/458/`,
  },
  
  ActivesArPage: {
    ActivesArApi: `wp-json/wp/v2/pages/652/`,
  },

   // Client post
  ActivesPost: {
    ActivesPostApi: `wp-json/wp/v2/our_actives/`,
  },

   // Terms and Conditions page
  TermsPage: {
    TermsApi: `wp-json/wp/v2/pages/822/`,
  },
  
  TermsArPage: {
    TermsArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Privacy Policy page  
  PrivacyPage: {
    PrivacyApi: `wp-json/wp/v2/pages/3/`,
  },
  
  PrivacyArPage: {
    PrivacyArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Hotels and Spas page  
  HotelsPage: {
    HotelsApi: `wp-json/wp/v2/pages/829/`,
  },
  
  HotelsArPage: {
    HotelsArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Sustainability page  
  SustainabilityPage: {
    SustainabilityApi: `wp-json/wp/v2/pages/898/`,
  },
  
  SustainabilityArPage: {
    SustainabilityArApi: `wp-json/wp/v2/pages/652/`,
  },

  // Contact Us page  
  ContactusPage: {
    ContactusApi: `wp-json/wp/v2/pages/988/`,
  },
  
  ContactusArPage: {
    ContactusArApi: `wp-json/wp/v2/pages/652/`,
  },
  // News page  
  NewsPage: {
    NewsApi: `wp-json/wp/v2/pages/1016/`,
  },
  
  NewsArPage: {
    NewsArApi: `wp-json/wp/v2/pages/652/`,
  },
    
  // Insight post
  InsightPost: {
    InsightPostApi: `wp-json/wp/v2/posts/`,
  },





  
  

};

export default CONSTANTS;
