import React from 'react'
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import CardText from "@/widget/CardText";
import BottomProduct from "./BottomCardSlider.module.scss"
import { Autoplay } from 'swiper/modules';
import FragnanceCard from "@/components/cards/FragnanceCard";

const BottomCardSlider = ({ products = [] }) => {
    return (
        <div className={`${BottomProduct.bottom_product_list} slider_container `}>
            <Swiper
                slidesPerView={1}
                spaceBetween={10}
                className="mySwiper product_list_card_swiper"
                loop={true}
                autoplay={{
                    disableOnInteraction: false,
                    delay: 2000,
                    pauseOnMouseEnter: true,
                }}
                speed={3000}
                breakpoints={{
                    700: {
                        spaceBetween: 20,
                        slidesPerView: 2,
                    },

                    1024: {
                        spaceBetween: 30,
                        slidesPerView: 3.25,
                    },
                    1400: {
                        spaceBetween: 30,
                        slidesPerView: 3,
                    },
                    1600: {
                        spaceBetween: 30,
                        slidesPerView: 3.25,
                    },
                }}
                modules={[Autoplay]}
            >
                {products.map((data, index) => (
                    <SwiperSlide key={index}>
                        {/* <div className={BottomProduct.product_list_item}>{data}</div> */}{/* 
                        <FragnanceCard
                            image={data?.product_image?.url}
                            title={data?.product_name}
                            description={data?.product_sub_text}
                            showDescription={!!data?.product_sub_text}
                            //   link={data?.link}
                        /> */}
                        <CardText data={data?.product_name} />
                    </SwiperSlide>
                ))}
            </Swiper>
        </div>
    )
}

export default BottomCardSlider