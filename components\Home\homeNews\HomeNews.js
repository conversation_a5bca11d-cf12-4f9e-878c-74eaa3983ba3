import React, { useState, useEffect, useRef } from 'react';
import styles from './homeNews.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from "swiper/modules";
import 'swiper/css';
import NewsCard from '@/components/cards/NewsCard';
import Icons from '@/public/Icons';
import AOS from "aos";
import "aos/dist/aos.css";
import Parallax from "@/components/Paralax";
import splitTextAnimation from "@/components/splitTextAnimation";
import parse from "html-react-parser";

const newsData = [
    {
        date: "11",
        monthYear: "September 2024",
        title: "We continue to blend tradition with innovation to shape a brighter future.",
        imageUrl: "/images/news_hm_1.jpg",
        description: "Each harvest is sustainably sourced and handpicked with care, ensuring premium quality for your products. ​#AlUlaPeregrina",
        link:"#,"
    },
    {
        date: "15",
        monthYear: "October 2024",
        title: "From sustainable certified practices to traceability and careful handpicking,",
        imageUrl: "/images/news_hm_2.jpg",
        description: "At Alula Peregrina, we transform nature's gifts into beauty rituals that honor the Earth. It's beauty with a purpose.",
        link:"#,"
    },
];

const HomeNews = ({ news_title, news_button, newsData }) => {
    const sectionRef = useRef(null);
    const [isVisible, setIsVisible] = useState(false);
    splitTextAnimation(sectionRef, "trigger_title");


    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                }
            },
            { threshold: 0.3 } // Adjust threshold for when the fade-in should trigger
        );

        if (sectionRef.current) {
            observer.observe(sectionRef.current);
        }

        return () => {
            if (sectionRef.current) {
                observer.unobserve(sectionRef.current);
            }
        };
    }, []);

    

    return (
        <div className={`${styles.hm_news_sec} ${isVisible ? 'fadeIn' : ''}`} ref={sectionRef}>
            <div className='container'>
                <h2 className='main_title trigger_title'>{news_title && parse(news_title)}</h2>
            
                <div className={styles.home_news}>
                    {newsData && newsData.length > 0 && (
                        <Link 
                            href={newsData[0].acf?.external_news_link ? newsData[0]?.acf?.external_news_link?.url : `/news/${newsData[0].slug}`} 
                            className={`${styles.news_main} fade_effect`}
                            target={newsData[0].acf?.external_news_link ? '_blank' : '_self'}
                        >
                            <div className={styles.news_main_img}>
                                <div className={styles.image} style={{ aspectRatio: '675 / 437', width: '100%', position: 'relative' }}>
                                    <Image 
                                        src={newsData[0]?.acf?.listing_image?.url || '/images/no-image.png'} 
                                        width={675} 
                                        height={477} 
                                        alt='image' 
                                    />
                                </div>
                                <div className={styles.news_main_texts}>
                                    <div className={styles.date}>
                                        <span>{new Date(newsData[0].date).getDate()}</span>
                                        {new Date(newsData[0].date).toLocaleDateString('en-US', { month: 'long' })} <br /> 
                                        {new Date(newsData[0].date).getFullYear()}
                                    </div>
                                    <h5>{newsData[0].title?.rendered}</h5>
                                </div>
                            </div>
                            <p>{newsData[0].acf?.description}</p>
                        </Link>
                    )}
                    {newsData && newsData.length > 1 && (
                        <div className={`${styles.news_list} fade_effect`}>
                            <Swiper
                                slidesPerView={2}
                                speed={2000}
                                autoplay={{ delay: 2500, disableOnInteraction: false }}
                                breakpoints={{
                                    0: {
                                        slidesPerView: 1,
                                    },
                                    768: {
                                        slidesPerView: 2,
                                        spaceBetween: 20,
                                    },
                                }}
                                modules={[Autoplay]}
                                className="news_swiper"
                            >
                                {newsData && newsData.slice(1).map((news, index) => (
                                    <SwiperSlide key={index}>
                                        <NewsCard
                                            date={new Date(news.date).getDate().toString()}
                                            monthYear={`${new Date(news.date).toLocaleDateString('en-US', { month: 'long' })} ${new Date(news.date).getFullYear()}`}
                                            title={news.title?.rendered}
                                            imageUrl={news?.acf?.listing_image?.url || '/images/no-image.png'}
                                            description={news.acf?.description}
                                            link={news.acf?.external_news_link ? news?.acf?.external_news_link?.url : `/news/${news.slug}`}
                                            target={news.acf?.external_news_link ? '_blank' : '_self'}
                                        />
                                    </SwiperSlide>
                                ))}
                            </Swiper>
                        </div>
                    )}
                </div>
                {news_button && (
                    <div className={styles.btn_sec}>
                        <Link href={news_button?.url} className={`${styles.main_btn} main_btn`}>
                            <span className='button_text'>{news_button?.title && parse(news_button?.title)}</span>
                            <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                        </Link>
                    </div>
                )}
               
            </div>
        </div>

    );
}

export default HomeNews;
