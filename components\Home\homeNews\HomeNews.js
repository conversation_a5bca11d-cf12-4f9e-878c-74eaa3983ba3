import React, { useState, useEffect, useRef } from 'react';
import styles from './homeNews.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from "swiper/modules";
import 'swiper/css';
import NewsCard from '@/components/cards/NewsCard';
import Icons from '@/public/Icons';
import AOS from "aos";
import "aos/dist/aos.css";
import Parallax from "@/components/Paralax";
import splitTextAnimation from "@/components/splitTextAnimation";

const newsData = [
    {
        date: "11",
        monthYear: "September 2024",
        title: "We continue to blend tradition with innovation to shape a brighter future.",
        imageUrl: "/images/news_hm_1.jpg",
        description: "Each harvest is sustainably sourced and handpicked with care, ensuring premium quality for your products. ​#AlUlaPeregrina",
        link:"#,"
    },
    {
        date: "15",
        monthYear: "October 2024",
        title: "From sustainable certified practices to traceability and careful handpicking,",
        imageUrl: "/images/news_hm_2.jpg",
        description: "At Alula Peregrina, we transform nature's gifts into beauty rituals that honor the Earth. It's beauty with a purpose.",
        link:"#,"
    },
];

const HomeNews = () => {
    const sectionRef = useRef(null);
    const [isVisible, setIsVisible] = useState(false);
    splitTextAnimation(sectionRef, "trigger_title");


    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                }
            },
            { threshold: 0.3 } // Adjust threshold for when the fade-in should trigger
        );

        if (sectionRef.current) {
            observer.observe(sectionRef.current);
        }

        return () => {
            if (sectionRef.current) {
                observer.unobserve(sectionRef.current);
            }
        };
    }, []);

    

    return (
        <div className={`${styles.hm_news_sec} ${isVisible ? 'fadeIn' : ''}`} ref={sectionRef}>
            <div className='container'>
                <h2 className='main_title trigger_title'>News</h2>
            
                <div className={styles.home_news}>
                    <Link href='#.' className={`${styles.news_main} fade_effect`}>
                        <div className={styles.news_main_img}>
                            <div className={styles.image} style={{ aspectRatio: '675 / 437', width: '100%', position: 'relative' }}>
                                <Image src='/images/civil_pic_0_news.jpg' width={675} height={477} alt='image' />
                                {/* <Parallax mediaSrc='/images/civil_pic_01.jpg' altText='image' /> */}
                            </div>
                            <div className={styles.news_main_texts}>
                                <div className={styles.date}>
                                    <span>28</span>
                                    September <br /> 2024
                                </div>
                                <h5>Sustainability is at the heart of AlUla Peregrina.</h5>
                            </div>
                        </div>
                        <p>With responsible harvesting and eco-friendly practices, we protect AlUla's natural beauty...</p>
                    </Link>
                    <div className={`${styles.news_list} fade_effect`}>
                        <Swiper
                            slidesPerView={2}
                            speed={2000}
                            autoplay={{ delay: 2500, disableOnInteraction: false }}
                            breakpoints={{
                                0: {
                                    slidesPerView: 1,
                                },
                                768: {
                                    slidesPerView: 2,
                                    spaceBetween: 20,
                                },
                            }}
                            modules={[Autoplay]}
                            className="news_swiper"
                        >
                            {newsData.map((news, index) => (
                                <SwiperSlide key={index}>
                                    <NewsCard
                                        date={news.date}
                                        monthYear={news.monthYear}
                                        title={news.title}
                                        imageUrl={news.imageUrl}
                                        description={news.description}
                                        link={news.link}
                                    />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
                <div className={styles.btn_sec}>
                    <Link href='/news' className={`${styles.main_btn} main_btn`}>
                        <span className='button_text'>View all News</span>
                        <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                    </Link>
                </div>
            </div>
        </div>

    );
}

export default HomeNews;
