import React from "react";
import styles from "./commonLink.module.scss";
import Link from "next/link";
import { useRouter } from "next/router";

const CommonLink = ({ links }) => {
  const router = useRouter();

  return (
    <div className={styles.common_link}>
      <ul>
        {links.map((item, index) => (
          <li key={index} className={router.pathname === item.link ? styles.active : ""}>
            <Link href={item.link}>{item.link_text}</Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default CommonLink;
