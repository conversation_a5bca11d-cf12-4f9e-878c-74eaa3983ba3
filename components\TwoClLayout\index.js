// components/ImageBg.js
import React from 'react';
import styles from './twocol.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";


const TwoClCard = ({ title, description, imageSrc, imageAlt = '' }) => {
    return (
        <div className={`${styles.two_cl_card_main}`}>
            <div className={`${styles.two_cl_img_block} p_relative`}>
                {imageSrc && (
                    <Image
                        src={imageSrc}
                        alt={imageAlt}
                        width={618}
                        height={508}
                        style={{ objectFit: 'cover' }}
                    />
                )}
            </div>
            <div className={`${styles.two_cl_txt_block}`}>
               {title && <h3>{parse(title)}</h3>}
                {description && parse(description)}
            </div>
        </div>
    );
};

export default TwoClCard;
