{"name": "alula-peregrina", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.1", "@lottiefiles/react-lottie-player": "^3.6.0", "@studio-freight/lenis": "^1.0.42", "animejs": "^3.2.2", "aos": "^2.3.4", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^12.5.0", "gsap": "^3.12.7", "html-react-parser": "^5.2.5", "htmlparser2": "^10.0.0", "lenis": "^1.1.21", "lottie-react": "^2.4.1", "next": "^15.2.4", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icomoon": "^2.6.1", "react-intersection-observer": "^9.16.0", "react-swipeable": "^7.0.2", "sass": "^1.85.0", "split-text": "^1.0.0", "split-type": "^0.3.4", "swiper": "^11.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7"}}