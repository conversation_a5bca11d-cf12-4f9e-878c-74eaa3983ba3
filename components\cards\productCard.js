import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import Icons from '@/public/Icons';
import Link from 'next/link';


const ProductCard = ({image,title,buttonText,buttonLink}) => {
    return (
        <div className={styles.product_card}>
            <div className={styles.pro_image}>
                <Image src={image} width={456} height={458} alt='image' />
            </div>
            <div className={styles.pro_desc}>
                <h3>{title}</h3>
                <Link href={buttonLink} className="main_btn">
                    <span className='button_text'>{buttonText}</span>
                    <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                </Link>
            </div>
        </div>
    )
}

export default ProductCard