@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.news_sec{
    position: relative;
    .leaf{
        position: absolute;
        left: 0;
        top: -185px;
        @include max(1600) {
            top: -145px;
            width: 23%;
        }
        @include max(767) {
            top: -65px;
            width: 30%;
        }
    }
}
.news_list{
    display: grid;
    grid-template-columns: repeat(3,1fr);
    gap:85px 40px;
    @include max(992) {
        grid-template-columns: repeat(2,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
    }
}
.btn_sec{
    text-align: center;
    .main_btn{
        overflow: hidden;
        justify-content: center;
        margin-top: 120px;
        @include max(992) {
            margin-top: 60px;
        }
        &::after{
            right: -48px;
        }
        &:hover{
            &::after{
                right: 2px;
            }
        }
    }
}
.news_details{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
    z-index: 5;
    h2{
        margin-bottom: 40px;
        font-weight: 300;
        @include max(1600) {
            margin-bottom: 25px;
            font-size: 2.85rem;
        }
        @include max(1200) {
            font-size: 2.5rem;
            line-height: 2.8rem;
        }
        @include max(1060) {
            font-size: 2.2rem;
            line-height: 2.5rem;
        }
        @include max(767) {
            font-size: 1.5rem;
            line-height: 2rem;
        }
    }
    h3{
        font-size: size(28px);
        line-height: size(39px);
        padding-right: 100px;
        font-weight: 400;
        @include max(1600) {
            font-size: size(25px);
            line-height: size(36px);
            padding-right: 50px;
        }
        @include max(1060) {
            font-size: size(22px);
            line-height: size(32px);
            padding-right: 0px;
        }
        @include max(767) {
            font-size: size(16px);
            line-height: size(26px);
        }
    }
    p{
        line-height: size(28px);
        margin-bottom: 30px;
        @include max(1600) {
            margin-bottom: 25px;
        }
        @include max(767) {
            line-height: size(25px);
            margin-bottom: 15px;
        }
    }
    .detail_left{
        width: 76%;
        @include max(1600) {
            width: 71%;
        }
        @include max(1060) {
            width:100%;
            margin-bottom: 50px;
        }
        img{
            border-radius: 5px;
            overflow: hidden;
            margin: 40px 0 30px 0;
            width: 100%;
            @include max(767) {
                margin: 30px 0 25px 0;
            }
        }
        h1,h2,h3,h4,h5,h6{
            margin-bottom: 20px;
        }
    }
    .detail_left_content{
        max-width: 1000px;
    }
    .detail_right{
        width: 23%;
        position: sticky;
        top: 108px;
        height: max-content;
        @include max(1600) {
            width: 24%;
        }
        @include max(1060) {
            width: 100%;
           
        }
        p{
            font-size: size(14px);
            line-height: size(20px);
            font-style: italic;
            margin-top: 20px;
            @include max(1600) {
                font-size: size(13px);
                line-height: size(19px);
            }
        }
    }
    .top_desc{
        display: flex;
        align-items: center;
        gap: 30px;
        margin-bottom: 60px;
        @include max(1600) {
            margin-bottom: 40px;
        }
        @include max(1060) {
            margin-bottom:30px;
            padding-top: 20px;
        }
        @include max(767) {
            margin-bottom:20px;
            gap: 20px;
        }
        li{
            font-size: size(18px);
            color: #fff;
            font-weight: 400;
            @include max(1600) {
                font-size: size(16px);
            }
            @include max(767) {
                font-size: size(14px);
            }
            span{
                font-weight: 600;
            }
            &:nth-child(1){
                font-size: size(13px);
                color: #fff;
                text-transform: uppercase;
                border: 1px solid #fff;
                border-radius: 30px;
                padding: 5px 15px;
                font-weight: 600;
                @include max(1600) {
                    font-size: size(11px);
                    padding: 4px 13px;
                }
            }
        }
    }
    .news_date_desc{
        li{
            img{
                margin: 0;
                border-radius: 0;
            }
        }
    }
}
.news_date_desc{
    display: flex;
    gap: 50px;
    margin-top:45px;
    @include max(1600) {
        margin-top:35px;
    }
    @include max(767) {
       flex-wrap: wrap;
       gap: 0;
       column-gap: 35px;
       margin-top:25px;
    }
    li{
        position: relative;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        color: #fff;
        font-size: size(18px);
        @include max(1600) {
            font-size: size(16px);
        }
        @include max(767) {
            font-size: size(14px);
        }
        &::after{
            content: "";
            display: block;
            width: 7px;
            height: 7px;
            border-radius: 50%;
            background-color: #fff;
            position: absolute;
            right: -25px;
            @include max(767) {
                right: -20px;
            }
        }
        &:last-child{
            &::after{
                display: none;
            }
            span{
                width: 15px;
            }
        }
        span{
            margin-right: 10px;
            width: 19px;
        }

    }
}
.social{
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: flex-end;
    @include max(1600) {
        gap: 12px;
    }
    li{
        margin: 0;
        color: #fff;
        font-size: size(18px);
        font-weight: 400;
        @include max(1600) {
            font-size: size(16px);
        }
        a{
            width: 40px;
            height: 40px;
            display: block;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #fff;
            transition: all .3s ease-in-out;
            @include max(1600) {
                width: 35px;
                height: 35px;
            }
        }
        svg{
            transition: all .3s ease-in-out;
            @include max(1600) {
                width: 17px !important;
                height: 17px !important;
            }
        }
        &:hover{
            a{
                border-color: #FCE1CB;
                background-color: #FCE1CB;
            }
            svg{
                fill: #7F4930 !important;
            }
        }
    }
}
.news_subscription_form{
    margin-top: 80px;
    background-color: rgba(0, 0, 0,.3);
    padding: 30px 15px;
    border-radius: 20px;
    width: 100%;
    @include max(1060) {
        margin-top: 40px;
    }
    h4{
        font-size: size(22px);
        font-family: var(--font-obsolete);
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 40px;
        &::after{
            content: "";
            display: block;
            width: 90px;
            height: 1px;
            background-color: #fff;
            margin-left: 10px;
        }
        @include max(1600) {
            font-size: size(19px);
        }
    }
    .inpField{
        width: 100%;
        height: 45px;
        background-color: #fff;
        width: 100%;
        border: none;
        font-family: var(--font-open-sans);
        font-size: size(14px);
        padding: 10px;
        color: #757575;
        &::placeholder{
            color: #757575;
        }
        @include max(1600) {
            font-size: size(13px);
        }
    }
    .submit{
        background-color: #7F4930;
        height: 49px;
        font-size: size(18px);
        text-transform: uppercase;
        width: 100%;
        color: #fff;
        border: none;
        font-family: var(--font-open-sans);
        margin-top: 15px;
        position: relative;
        cursor: pointer;
        transition: all .3s ease-in-out;
        @include max(1600) {
            font-size: size(16px);
        }
    }
    ul{
        li{
            position: relative;
            margin: 0;
            transition: all .3s ease-in-out;
            span{
                display: block;
                width: 10px;
                transition: all .3s ease-in-out;
                &::before{
                    content: "";
                    width: 6px;
                    height: 6px;
                    background-color: #fff;
                    display: block;
                    position: absolute;
                    left: 15px;
                    z-index: 1;
                    top: 36px;
                    transform: rotate(45deg);
                    transition: all .3s ease-in-out;
                }
                &::after{
                    content: "";
                    width: 6px;
                    height: 6px;
                    background-color: #fff;
                    display: block;
                    position: absolute;
                    right: 15px;
                    z-index: 1;
                    top: 36px;
                    transform: rotate(45deg);
                    transition: all .3s ease-in-out;
                }
            }
            &:hover{
                .submit{
                    &:hover{
                        background-color: #FCE1CB;
                        color: #7F4930;
                    }
                }
                span{
                    &::before,&::after{
                        background-color:#7F4930;
                    }
                }
            }
        }
    }
}
.related_news{
    h2{
        margin-bottom: 45px;
        @include max(992) {
            margin-bottom:15px;
        }
    }
}
:global(body.rtl) {
    .news_details {
        h3{
            padding-right: 0;
            padding-left: 100px;
        }
    }
    .news_date_desc {
        li{
            &::after{
                right: auto;
                left: -25px;
            }
            span{
                margin-right: 0;
                margin-left: 10px;
            }
        }
    }
    .news_subscription_form{
        h4{
            &::after{
                margin-left: 0;
                margin-right: 10px;
            }
        }
    }
}