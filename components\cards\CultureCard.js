import React, { useEffect, useRef, useState } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import Parallax from "@/components/Paralax";
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import AnimatedTitle from '../anim';

// Register ScrollTrigger plugin
if (typeof window !== "undefined") {
    gsap.registerPlugin(ScrollTrigger);
}

const CultureCard = ({ data }) => {
    const { imageSrc, title, desc } = data;
    const cardRef = useRef(null);
    const textRef = useRef(null);
    const [textVisible, setTextVisible] = useState(false);

    useEffect(() => {
        const card = cardRef.current;
        const text = textRef.current;

        // Check if the screen width is greater than 768px (Tablet & Desktop)
        const isDesktop = window.innerWidth > 1061;

        if (isDesktop) {
            // Animation for Image Expansion and Shrinking
            gsap.fromTo(
                card,
                { width: "10%", opacity: 0 },
                {
                    width: "100%",
                    opacity: 1,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: card,
                        start: "top 70%",
                        end: "top 30%",
                        scrub: 1,
                        onUpdate: (self) => {
                            // Show text only after the card width reaches 100%
                            if (self.progress >= 1) {
                                setTextVisible(true);
                            }
                            // Hide text before shrinking the image
                            else if (self.progress <= 0.8) {
                                setTextVisible(false);
                            }
                        }
                    }
                }
            );

            // Text Fade-In and Fade-Out Effect
            gsap.fromTo(
                text,
                { opacity: 0, y: 20 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: card,
                        start: "top 50%",
                        end: "top 40%",
                        toggleActions: "play none reverse"
                    }
                }
            );
        }
    }, []);

    return (
        <div ref={cardRef} className={styles.culture_card}>
            <div className={styles.culture_card_img}>
                <Parallax mediaSrc={imageSrc} altText={title} />
            </div>
            <div
                
                className={styles.cultural_content}
                
            >
                {/* <h3 className="main_title">{title}</h3> */}
                <AnimatedTitle
                    title={title}
                    tag="h4"
                    className="main_title"
                />
                <div ref={textRef} style={{ opacity: textVisible ? 1 : 0, transition: "opacity 0.5s ease-in-out" }}>
                    {desc.map((para, index) => (
                        <p key={index}>{para}</p>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CultureCard;
