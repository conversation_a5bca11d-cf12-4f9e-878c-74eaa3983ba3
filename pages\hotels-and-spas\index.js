import React, { useRef, useEffect } from "react";
import InnerBanner from '@/components/innerBanner/InnerBanner';
import BoxDesc from '@/components/boxDescription/BoxDesc';
import styles from './hotels.module.scss';
import Image from 'next/image';
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import FragnanceCard from "@/components/cards/FragnanceCard";
import JoinCard from "@/components/cards/JoinCard";
import AOS from "aos";
import "aos/dist/aos.css";
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import AnimatedTitle from "@/components/anim";
import ImageBg from "@/components/imageBgSet/imageBg";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getHotelsAndSpas } from "@/utils/lib/server/publicServices"; 

const HotelSpas = (props) => {
  useEffect(() => {
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });
  }, []);

  const router = useRouter();
  const yoastData = props?.pageData?.yoast_head_json;
  if (!props?.pageData) {
            return null;
  }
  
  return (
    <>
         {yoastData && <Yoast meta={yoastData} />}
         {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_logo ||
                props?.pageData?.acf?.banner_image) && (                
                      <InnerBanner
                          bannerBg={props?.pageData?.acf?.banner_image?.url}
                          title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                          content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                          subImageUrl={props?.pageData?.acf?.banner_logo?.url}
                          isSmallText="true" 
                      />           
           
        )}
 
      
      { props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.ht_title ||
          props?.pageData?.acf?.ht_content ||        
          props?.pageData?.acf?.ht_image) && (
          <section className={`${styles.hotels_sec}  ${styles.pt_120}`} id="hotel-toiletries">
            <div className={styles.graphics}>
              <Image src='/images/spa_graphics.png' width={276} height={579} alt='image' />
            </div>
            <ImageBg
              title={props?.pageData?.acf?.ht_title}
              description={props?.pageData?.acf?.ht_content}
              imageSrc={props?.pageData?.acf?.ht_image?.url}
              imageAlt={props?.pageData?.acf?.ht_image?.alt || 'image'}
              fullWidth="true"
              isBigText={true}
            />
          </section>
        )}

      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.product_range_title ||          
          props?.pageData?.acf?.product_range_list?.length > 0) && (
          <section className={`${styles.pt_120} p_relative  ${styles.swipper_overflow}`}>
            <div className={`${styles.right_bg_img}`}>
              <Image src="/images/ic-04.png" width={261} height={361} alt="image" />
            </div>
          <div className="container">
            {props?.pageData?.acf?.product_range_title && (
              <div className={`${styles.pb_20}`}>
                <h2 className="main_title">{parse(props?.pageData?.acf?.product_range_title)}</h2>
              </div>
            )}
              
            {props?.pageData?.acf?.product_range_list &&
              props?.pageData?.acf?.product_range_list.length > 0 && (
                <Swiper
                modules={[Autoplay]}
                autoplay={{
                  delay: 2500,  // Adjust delay as needed
                  disableOnInteraction: false,
                }}
                speed={2000}
                loop={true}
                breakpoints={{
                  0: {
                    slidesPerView: 1,
                    spaceBetween: 20,
                  },
                  768: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                  },
                  1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                  },
                }}
                className="frangment_slider">
                {props?.pageData?.acf?.product_range_list.map((rangeItem, index) => (
                  <SwiperSlide key={index}>
                    <FragnanceCard
                      image={rangeItem?.product_image?.url}
                      title={rangeItem?.product_name}
                      link={rangeItem?.link}
                      description={rangeItem?.product_sub_text}
                      showDescription={!!rangeItem?.product_sub_text}
                    />
                  </SwiperSlide>
                ))}
                </Swiper>
              )}
            </div>
          </section>
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.spa_solutions_title ||          
          props?.pageData?.acf?.spa_solutions_content ||
          props?.pageData?.acf?.spa_solutions_image) && (
                <section className={`${styles.pt_120} ${styles.pb_120}`} id="spa-solutions">
                  <ImageBg
                      title={props?.pageData?.acf?.spa_solutions_title}                      
                      description={props?.pageData?.acf?.spa_solutions_content}
                      imageSrc={props?.pageData?.acf?.spa_solutions_image?.url}
                      imageAlt={props?.pageData?.acf?.spa_solutions_image?.alt || 'How it all began'}
                      isBigText={true}
                      fullWidth="true"
                    />        
                </section>
      )}
{props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.ancient_desert_title ||          
          props?.pageData?.acf?.ancient_desert_content ||
          props?.pageData?.acf?.ancient_desert_image ||
          props?.pageData?.acf?.ancient_desert_product?.length > 0) && (        
            <section className={`  ${styles.pb_120}`}>
              <ImageBg
                title={props?.pageData?.acf?.ancient_desert_title}                      
                description={props?.pageData?.acf?.ancient_desert_content}
                imageSrc={props?.pageData?.acf?.ancient_desert_image?.url}
                imageAlt={props?.pageData?.acf?.ancient_desert_image?.alt || 'How it all began'}
                showProductsList={true}
                products={props?.pageData?.acf?.ancient_desert_product}
              />
            </section>
        )}
      
{props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.vibrant_oasis_title ||          
          props?.pageData?.acf?.vibrant_oasis_content ||
          props?.pageData?.acf?.vibrant_oasis_image ||
          props?.pageData?.acf?.vibrant_oasis_product?.length > 0) && (
      <section className={`${styles.pb_120} ${styles.pt_120} ${styles.hotel_sec} bg_black`}>
        <div className='container'>
          <BoxDesc
            title={props?.pageData?.acf?.vibrant_oasis_title} 
            content={props?.pageData?.acf?.vibrant_oasis_content}
            imageSrc={props?.pageData?.acf?.vibrant_oasis_image?.url}
            position="right"
            buttonLink="/#"
            buttonText="View Products"
            BottomSliderData={true}
             bottomSliderDataList={props?.pageData?.acf?.vibrant_oasis_product}
           
          />
        </div>
      </section>
        )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.oil_hairitage_title ||
          props?.pageData?.acf?.oil_hairitage_content ||
          props?.pageData?.acf?.oil_hairitage_image) && (          
      <section className={`${styles.pt_120} ${styles.pb_120}`}>
        <ImageBg
          title={props?.pageData?.acf?.oil_hairitage_title}
          // subtitle="Where Perfume And History Intertwin."
          description={props?.pageData?.acf?.oil_hairitage_content}
          imageSrc={props?.pageData?.acf?.oil_hairitage_image?.url}
          imageAlt={props?.pageData?.acf?.oil_hairitage_image?.alt || 'How it all began'}
          buttonLink="/#"
          showProductsList={true}
          products={props?.pageData?.acf?.oil_hairitage_product}
        />
      </section>
        )}
      
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.contact_us_title ||
          props?.pageData?.acf?.contact_us_email ||
          props?.pageData?.acf?.contact_us_image) && (
          <section className={`  ${styles.pb_120}`}>
            <ImageBg
              title={props?.pageData?.acf?.contact_us_title}
              imageSrc={props?.pageData?.acf?.contact_us_image?.url}
              imageAlt={props?.pageData?.acf?.contact_us_image?.alt || "How it all began"}
              email={props?.pageData?.acf?.contact_us_email}
              emailText={props?.pageData?.acf?.contact_us_email}
              showEmail={true}

            />
          </section>
        )}

    </>
  )
}

export default HotelSpas


export async function getStaticProps(locale) {
    const PageData = await getHotelsAndSpas(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}