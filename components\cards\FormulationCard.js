import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const FormulationCard = ({ data, position = 'left' }) => {
    const { content, title, image } = data;
    const sectionRef = useRef(null);
    const descRef = useRef(null);
    const imageRef = useRef(null);

    useEffect(() => {
        const ctx = gsap.context(() => {
            gsap.fromTo(
                descRef.current,
                { opacity: 0, y: 50 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: sectionRef.current,
                        start: 'top 80%',
                        toggleActions: 'play reverse play reverse',
                    },
                }
            );

            gsap.fromTo(
                imageRef.current,
                { opacity: 0, scale: 0.9 },
                {
                    opacity: 1,
                    scale: 1,
                    duration: 1.2,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: sectionRef.current,
                        start: 'top 85%',
                        toggleActions: 'play reverse play reverse',
                    },
                }
            );
        }, sectionRef);

        return () => ctx.revert();
    }, []);

    return (
        <div ref={sectionRef} className={`${styles.formulation_card} ${position === 'right' ? styles.right : styles.left}`}>
            <div className={styles.formulation_desc} ref={descRef}>
                <h3 className="main_title">{title}</h3>
                {content.map((para, index) => (
                    <p key={index}>{para}</p>
                ))}
            </div>
            <div className={styles.formulation_image} ref={imageRef}>
                <Image src={image} alt="image" width={624} height={354} quality={100} />
            </div>
        </div>
    );
};

export default FormulationCard;
