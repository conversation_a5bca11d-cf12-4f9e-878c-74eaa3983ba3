@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.virgin_sec{
    position: relative;
    h2{
        margin-bottom: 0;
    }
    .leaf{
        position: absolute;
        bottom: -180px;
        left: 0;
        @include max(1060) {
            width: 20%;
        }
    }
    .leaf_2{
        position: absolute;
        right: 0;
        top: 0;
        @include max(1060) {
            width: 20%;
        }
    }
    .leaf_3{
        position: absolute;
        left: 0;
        top: -120px;
        @include max(1060) {
            width: 20%;
            top: -40px;

        }
    }
}
.botanical_list{
    
    h5{
        color: #FCE1CB;
        font-size: size(28px);
        font-weight: 500;
        margin: 40px 0;
        @include max(992) {
            font-size: size(25px);
        }
        @include max(767) {
            font-size: size(20px);
            line-height: size(25px);
            margin: 30px 0 25px 0;
        }
    }
    .bt_list{
        display: grid;
        grid-template-columns: repeat(4,1fr);
        gap: 33px;
        @include max(1060) {
            gap: 20px;
        }
        @include max(992) {
            grid-template-columns: repeat(2,1fr);
        }
    }
}
.research_sec{
    margin: 0 30px;
    background-color: rgb(0, 0, 0,.3);
    border-radius: 20px;
    @include max(767) {
        margin: 0 15px;
        padding: 40px 0;
    }
    h5{
        font-size: size(28px);
        line-height: size(38px);
        font-weight: 600;
        color: #fff;
        @include max(1060) {
            font-size: size(22px);
            line-height: size(32px);
            margin-top: 10px;
        }
        @include max(767) {
            font-size: size(18px);
            line-height: size(25px);
        }
    }
}
.research_list{
    margin-top: 45px;
    display: grid;
    grid-template-columns: repeat(3,1fr);
    gap: 34px;
    @include max(1060) {
        gap: 25px;
    }
    @include max(992) {
        grid-template-columns: repeat(2,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
    }
}
:global(body.rtl) {
    .virgin_sec{
        .leaf{
            left:auto;
            right: 0; 
            transform: scaleX(-1);
        }
        .leaf_2{
            right:auto;
            left: 0;
            transform: scaleX(-1);
        }
        .leaf_3{
            left: auto;
            right: 0;
            transform: scaleX(-1);
        }
    }
}