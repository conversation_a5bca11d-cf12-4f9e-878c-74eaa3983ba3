import React, { useState, useEffect, useRef } from "react";
import styles from './news.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import Image from 'next/image';
import NewsCard from '@/components/cards/NewsCard';
import AOS from "aos";
import "aos/dist/aos.css";
import Icons from '@/public/Icons';
import Link from "next/link";


import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getNewspage, getInsightsPosts } from "@/utils/lib/server/publicServices"; 

const newsData = [
    {
        date: '11',
        monthYear: 'September 2024',
        title: "February 22nd marks more than a moment in history",
        description: "it’s the foundation of a legacy. A day that shaped a nation, rooted in strength, vision, and unity. From 1727 to today, the spirit of Saudi Arabia continues to rise. ",
        imageUrl: "/images/news_1.jpg",
        id: 1
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "This Peregrina Season, we celebrated the heart of AlUla",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_2.jpg",
        id: 2
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "Ten farmers received the Gold Farmers Excellence Award.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_3.jpg",
        id: 3
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "Sustainability is at the heart of AlUla Peregrina.",
        description: "With responsible harvesting and eco-friendly practices, we protect AlUla’s natural beauty, support local communities, and ensure a greener future. ",
        imageUrl: "/images/news_4.jpg",
        id: 4
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "AlUla Peregrina Oil harnesses nature with sustainable methods.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_5.jpg",
        id: 5
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "AlUla Peregrina Oil is lightweight, non-comedogenic.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_6.jpg",
        id: 6
    },
]

const News = (props) => {
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);
    const router = useRouter();
    const yoastData = props?.pageData?.yoast_head_json;
    if (!props?.pageData) {
        return null;
    }

    return (
        <>
            {yoastData && <Yoast meta={yoastData} />}
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_image) && (
                <InnerBanner
                    bannerBg={props?.pageData?.acf?.banner_image?.url}
                    title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                    content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                />
            )}
            
            
            <div className={`${styles.pb_120} ${styles.pt_120} ${styles.news_sec}`}>
                <div className={styles.leaf}>
                    <Image src='/images/active_leaf.png' width={466} height={465} alt='image' />
                </div>
                <div className="container">
                    <div className={styles.news_list}>
                        {pro.map((news, index) => (
                            <div className={styles.news_set} key={index} data-aos="fade-up" data-aos-duration="1000">
                                <NewsCard
                                    date={news.date}
                                    monthYear={news.monthYear}
                                    title={news.title}
                                    imageUrl={news.imageUrl}
                                    description={news.description}
                                    link={`/news/${news.id}`}
                                    extraClass="news_page_card"
                                />
                            </div>
                        ))}
                    </div>
                    <div className={styles.btn_sec}>
                        <Link href='#' className={`${styles.main_btn} main_btn`}>
                            <span className='button_text'>Show More</span>
                        </Link>
                    </div>
                </div>
            </div>
        </>
    )
}

export default News

export async function getStaticProps(locale) {
    const PageData = await getNewspage(locale.locale); 
    const InsightsData = await getInsightsPosts(locale.locale); 
    
    return {
        props: {
            pageData: PageData || null,
            insightsData: InsightsData || null,
        },
        revalidate: 10,
      };
}