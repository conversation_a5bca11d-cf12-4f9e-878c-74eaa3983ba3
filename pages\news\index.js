import React, { useState, useEffect, useRef } from "react";
import styles from './news.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import Image from 'next/image';
import NewsCard from '@/components/cards/NewsCard';
import AOS from "aos";
import "aos/dist/aos.css";
import Icons from '@/public/Icons';
import Link from "next/link";


import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getNewspage, getInsightsPosts } from "@/utils/lib/server/publicServices"; 

const News = (props) => {
    const [visibleCount, setVisibleCount] = useState(6);

    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    const router = useRouter();
    const yoastData = props?.pageData?.yoast_head_json;
    if (!props?.pageData) {
        return null;
    }

    const insights = Array.isArray(props?.insightsData) ? props.insightsData : [];
    const visibleInsights = insights.slice(0, visibleCount);

    const handleShowMore = () => {
        setVisibleCount((prev) => prev + 6);
    };

    return (
        <>
            {yoastData && <Yoast meta={yoastData} />}
            {props?.pageData?.acf && (
                (props.pageData.acf.banner_text ||
                    props.pageData.acf.sub_text ||
                    props.pageData.acf.banner_image) && (
                    <InnerBanner
                        bannerBg={props.pageData.acf.banner_image?.url}
                        title={props.pageData.acf.banner_text && parse(props.pageData.acf.banner_text)}
                        content={props.pageData.acf.sub_text && parse(props.pageData.acf.sub_text)}
                    />
                )
            )}

            <div className={`${styles.pb_120} ${styles.pt_120} ${styles.news_sec}`}>
                <div className={styles.leaf}>
                    <Image src='/images/active_leaf.png' width={466} height={465} alt='image' />
                </div>
                <div className="container">
                    {visibleInsights?.length > 0 && (
                        <div className={styles.news_list}>
                            {visibleInsights.map((news, index) => (
                                <div className={styles.news_set} key={index} data-aos="fade-up" data-aos-duration="1000">
                                    <NewsCard
                                        date={new Date(news.date).getDate().toString()}
                                        monthYear={`${new Date(news.date).toLocaleDateString('en-US', { month: 'long' })} ${new Date(news.date).getFullYear()}`}
                                        title={news.title?.rendered}
                                        imageUrl={news?.acf?.listing_image?.url || '/images/no-image.png'}
                                        description={news.acf?.description}
                                        link={news.acf?.external_news_link ? news?.acf?.external_news_link?.url : `/news/${news.slug}`}
                                        target={news.acf?.external_news_link ? '_blank' : '_self'}
                                        extraClass="news_page_card"
                                    />
                                </div>
                            ))  }
                        </div>
                    )}
                    {visibleInsights?.length === 0 && (
                        <p>No insights found.</p>
                    )}                    
                    {visibleCount < insights.length && (
                        <div className={styles.btn_sec}>
                            <button onClick={handleShowMore} className={`${styles.main_btn} main_btn`}>
                                <span className='button_text'>{router.locale === 'ar' ? 'عرض المزيد' : 'Show More'}</span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </>
    )
}

export default News

export async function getStaticProps(locale) {
    const PageData = await getNewspage(locale.locale); 
    const InsightsData = await getInsightsPosts(locale.locale); 
    
    return {
        props: {
            pageData: PageData || null,
            insightsData: InsightsData || null,
        },
        revalidate: 10,
      };
}
