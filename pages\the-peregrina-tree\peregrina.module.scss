@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.map{
    max-width: calc(100% - 60px);
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    margin-top: 155px;
    @include max(1600) {
        margin-top: 134px;
    }
    @include max(1060) {
        border-radius: 12px;
    }
    @include max(992) {
        margin-top: 115px;
    }
    iframe{
        display: block;
        @include max(1600) {
            height: 550px;
        }
        @include max(1060) {
            height: 400px;
        }
    }
}
.contact_sec{
    max-width: calc(100% - 60px);
    margin: 0 auto;
    border-radius: 20px;
}
.contact_sec_content{
    height: 820px;
    background-color: #F2EDE7;
    display: flex;
    flex-wrap: wrap;
    border-radius: 20px;
    overflow: hidden;
    @include max(1600) {
        height: 800px;
    }
    @include max(1060) {
        height: auto;
        border-radius:12px;
    }
    .contact_address{
        background-repeat: no-repeat;
        background-size: cover;
        height: 100%;
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 70px;
        position: relative;
        &::after{
            content: "";
            display: block;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0,.4);
            position: absolute;
            left: 0;
            top: 0;
        }
        @include max(1060) {
            width: 100%;
            padding: 50px;
        }
        @include max(992) {
            padding: 40px;
        }
        @include max(767) {
            padding: 15px;
        }
    }
    .address_box{
        height: 680px;
        background-color: #F8F5F1;
        border-radius: 20px;
        width: 100%;
        padding: 65px 45px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        z-index: 1;
        @include max(1600) {
            padding: 65px 45px 35px 45px;
            height: 620px;
            border-radius: 12px;
        }
        @include max(1060) {
            padding: 40px 30px 20px 30px;
            height: 370px;
        }
        @include max(767) {
            padding: 25px 15px 20px 15px;
            height: 320px;
        }
    }
    h2{
        color: #34241D;
    }
    .address_top{
        ul{
            li{
                margin-bottom: 5px;
                a{
                    color: #7F4930;
                    font-size: size(20px);
                    direction: ltr;
                    display: block;
                    @include max(1600) {
                        font-size: size(18px);
                    }
                    @include max(767) {
                        font-size: size(15px);
                    }
                    &:hover{
                        color: #d19270;
                    }
                }
            }
        }
    }
    .address_bottom{
        p{
            color: #7F4930;
            font-size: size(20px);
            line-height: size(30px);
            font-weight: 400;
            max-width: 450px;
            @include max(1600) {
                font-size: size(18px);
                line-height: size(28px);
            }
            @include max(767) {
                font-size: size(15px);
                line-height: size(25px);
            }
        }
    }
    .address_bottom{
        ul{
            display: flex;
            gap: 20px;
            margin-top: 25px;
            @include max(992) {
                gap: 15px;
            }
            @include max(767) {
                margin-top: 20px;
            }
            li{
                &:first-child{
                    margin-right: 15px;
                    color: #7F4930;
                    font-size: size(15px);
                    font-weight: 500;
                    @include max(992) {
                        font-size: size(14px)
                    }
                }
                svg{
                    transition: all .3s ease-in-out;
                    @include max(1600) {
                        width: 19px !important;
                        height: 19px !important;
                    }
                }
                &:last-child{
                    svg{
                        @include max(1600) {
                            width: 16px !important;
                            height: 16px !important;
                        }
                    }
                }
                &:hover{
                    a{
                        svg{
                            fill: #d19270 !important;
                        }
                    }
                }
            }
        }
    }
}
.form_sec{
    width: 50%;
    @include max(1060) {
        width: 100%;
    }
}
 
 .two_cl_img_block{ width: 100%; border-radius: 20px; overflow: hidden;
    img{ display: block;}
}


.two_cl_layout{
    margin: 0; padding: 0; display: flex; flex-wrap: wrap; justify-content: space-between;
    li{ list-style: none; width: 48.2%;
    
    @include max(600) {
        width: 100%; margin-bottom: 35px;
    }
    }

    // @include max(600) { padding-left: 3%; padding-right: 3%;}

    & > :last-child{
        margin-bottom: 0;
    }
}
.tree_cycle{
    background-color: rgba(0, 0, 0, 0.3);
}