import React, { useState, useEffect, useRef } from "react";
import InnerBanner from '@/components/innerBanner/InnerBanner';
import gsap from 'gsap';
import AOS from "aos";
import styles from '@/pages/the-peregrina-tree/peregrina.module.scss'
import TwoClCard from '@/components/TwoClLayout';
import PeregrinaSecondSection from "@/components/Innovation/Innovation";
import PeregrinaIntroSection from "@/components/History";
import PeregrinaSlider from "@/components/PeregrinaSlider";
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getPeregrinaCenter } from "@/utils/lib/server/publicServices";  


const PeregrinaTree = (props) => {
    const IntitleRef = useRef(null);
    const paragraphsRef = useRef([]);
    const imgRef = useRef(null);

    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
        return () => {
            if (AOS.refreshHard) AOS.refreshHard();
        };
    }, []);

    useEffect(() => {
        // Title animation
        if (IntitleRef.current) {
            gsap.from(IntitleRef.current, {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: IntitleRef.current,
                    start: "top 80%",
                    toggleActions: "play reverse play reverse",
                },
            });
        }

        // Paragraphs animation
        paragraphsRef.current.forEach((el, i) => {
            if (el) {
                gsap.from(el, {
                    y: 30,
                    opacity: 0,
                    duration: 0.8,
                    delay: i * 0.2,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: el,
                        start: "top 85%",
                        toggleActions: "play reverse play reverse",
                    },
                });
            }
        });

        // Parallax image animation 
        if (imgRef.current) {
            gsap.fromTo(
                imgRef.current,
                { opacity: 0, scale: 0.9 },
                {
                    opacity: 1,
                    scale: 1,
                    duration: 1.2,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: imgRef.current,
                        start: "top+=80px top",
                        end: "+=1000",
                        pin: true,
                        scrub: 0.1,
                        anticipatePin: 1,
                    },
                }
            );
        }

        return () => {
            // Clean up all ScrollTriggers and animations
            if (ScrollTrigger) {
                ScrollTrigger.getAll().forEach(trigger => trigger.kill());
            }
            gsap.globalTimeline.clear();
        };
    }, []);

    const router = useRouter();
    const yoastData = props?.pageData?.yoast_head_json;

    const scrollingText = props?.pageData?.acf?.scrolling_text;
    const hasScrollingText =
        scrollingText &&
      (
        scrollingText.from_title ||
        scrollingText.from_sub_title ||
        scrollingText.from_image ||
        scrollingText.to_title ||
        scrollingText.to_sub_title ||
        scrollingText.to_image
      );

    if (!props?.pageData) {
        return null;
    }
    return (
        <>
            {yoastData && <Yoast meta={yoastData} />}

            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
                props?.pageData?.acf?.banner_image) && (
                <InnerBanner
                    title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                    bannerBg={props?.pageData?.acf?.banner_image?.url}
                    content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                />
            )}             
            {hasScrollingText && (
              <PeregrinaSecondSection scrollingText={scrollingText} />              
            )}
           

            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.tree_rooted_title ||                
                props?.pageData?.acf?.tree_rooted_listing) && (
            <section className={`${styles.pt_120} ${styles.pb_120}`} style={{ backgroundImage: `url("/images/history_bg.jpg")` }}>
                <div className="container">
                    {props?.pageData?.acf?.tree_rooted_title && (
                        <h2 className="main_title title_color_02" data-aos="fade-up" data-aos-duration="1000">{parse(props?.pageData?.acf?.tree_rooted_title)}</h2>
                    )}
                    {props?.pageData?.acf?.tree_rooted_listing && props?.pageData?.acf?.tree_rooted_listing.length > 0 && (
                   <ul className={`${styles.two_cl_layout}`}>
                        {props?.pageData?.acf?.tree_rooted_listing.map((item, index) => (
                            <li key={index} data-aos="fade-up" data-aos-duration="1000" data-aos-delay={index * 100}>
                                <TwoClCard
                                    title={item?.list_title}
                                    description={item?.list_content}
                                    imageSrc={item?.list_image?.url}
                                    imageAlt={item?.list_image?.alt || "Peregrina Tree Image"}
                                />
                            </li>
                        ))}
                    </ul>
                    )}
                </div>
            </section>
            )}
           {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.section_title ||                
                props?.pageData?.acf?.tree_image ||                
                props?.pageData?.acf?.family_box.length > 0 ||                
                props?.pageData?.acf?.section_subtitle) && ( 
            <section>
                <PeregrinaIntroSection
                    title={props?.pageData?.acf?.section_title}
                    paragraphs={props?.pageData?.acf?.section_subtitle}
                    botanicalData={
                        props?.pageData?.acf?.family_box.length > 0 &&
                        props?.pageData?.acf?.family_box?.map((item) => ({
                        icon: item?.icon?.url || '',
                        title: item?.label || '',
                        subTitle: item?.name || '',
                        })) || []
                    }
                    mainImage={{
                        src: props?.pageData?.acf?.tree_image?.url || '',
                        width: 626,
                        height: 860,
                        alt: props?.pageData?.acf?.tree_image?.alt || 'Peregrina tree',
                    }}
                    leafImage={{
                        src: '/images/active_leaf.png',
                        width: 466,
                        height: 465,
                        alt: 'Decorative leaf',
                    }}
                    />
                

            </section>
                )}
             {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.life_cycle_title ||                
                props?.pageData?.acf?.tree_life_cycle?.length > 0) && (
            <section className={` ${styles.tree_cycle}`} >
                <PeregrinaSlider title={props?.pageData?.acf?.life_cycle_title} contentData={props?.pageData?.acf?.tree_life_cycle} />
            </section>
            )}
        </>
    );
};

export default PeregrinaTree;


export async function getStaticProps(locale) {
    const PageData = await getPeregrinaCenter(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}