import React, { useRef, useEffect } from "react";
import Image from "next/image";
import styles from './sustainability.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import gsap from "gsap";
import AOS from "aos";
import "aos/dist/aos.css";
import TextOverImg from "@/components/cards/TextOverImg";
import ImageBg from "@/components/imageBgSet/imageBg";
import SustainabilityCard from "@/components/cards/SustainabilityCard";
import SingleTitleBanner from "@/components/single_title_banner";



import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getSustainability } from "@/utils/lib/server/publicServices"; 


const Sdgs = (props) => {
  useEffect(() => {
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });
  }, []);
  const leafHtRef = useRef(null);
  useEffect(() => {
    // Animate .leaf_ht with a float effect
    gsap.fromTo(
      leafHtRef.current,
      { opacity: 0, y: 50, rotate: -20 },
      {
        opacity: 1,
        y: 0,
        rotate: 0,
        duration: 1.2,
        ease: "power3.out",
        scrollTrigger: {
          trigger: leafHtRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play reverse play reverse",
          markers: false,
        },
      }
    );


  }, []);

   const router = useRouter();
      const yoastData = props?.pageData?.yoast_head_json;
      if (!props?.pageData) {
            return null;
        }
    
    
  return (
    <>
       {yoastData && <Yoast meta={yoastData} />}
         {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.banner_text ||
                props?.pageData?.acf?.sub_text ||
          props?.pageData?.acf?.banner_image) && (
          <section id="environmental-responsibility">
              <InnerBanner
                  bannerBg={props?.pageData?.acf?.banner_image?.url}
                  title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                  content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
              />   
            </section>           
        )}
          {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.sustainability_title ||
                props?.pageData?.acf?.sustainability?.length > 0) && (    
      <section className={`${styles.pb_100} ${styles.pt_100} p_relative`}>
        <div className="container">
          {props?.pageData?.acf?.sustainability_title && (
            <div className={`${styles.pb_10}`}>
              <h2 className={`main_title  ${styles.f_24}`}>{parse(props?.pageData?.acf?.sustainability_title)}</h2>
            </div>
          )}
          {props?.pageData?.acf?.sustainability &&
            props?.pageData?.acf?.sustainability.length > 0 && (
            <ul className={`${styles.two_cl_layout}`}>
              {props?.pageData?.acf?.sustainability.map((item, idx) => (
                <li data-aos="fade-up" data-aos-duration="1000" key={idx} className={`${styles.mb_50}`}>
                  <TextOverImg
                    image={item.ss_image?.url}
                    title={item.ss_title}
                    description={item.ss_content}
                  />
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className={`${styles.icon_block}`}>
          <Image src="/images/ic-06.png" alt="sustainability" width={606} height={903} />
        </div>
      </section>
        )}
        {props &&
          props?.pageData &&
          props?.pageData?.acf && (
          props?.pageData?.acf?.social_impact_title ||
          props?.pageData?.acf?.social_impact_image) && (
            <section className={`${styles.pb_120} `} id="social-impact">
              <SingleTitleBanner
                imageSrc={props?.pageData?.acf?.social_impact_image?.url}
                title={props?.pageData?.acf?.social_impact_title}
              />
            </section>
          )}
{props?.pageData?.acf?.social_impact_cards?.length > 0 && 
    props?.pageData?.acf?.social_impact_cards.map((card, idx) => (
      <section className={`${styles.pb_120}`} key={idx}>
        <ImageBg
          title={card?.sim_title}
          description={card?.sim_content}
          imageSrc={card?.sim_image?.url}
          imageAlt={card?.sim_image?.alt || 'How it all began'}
        />
      </section>
    ))}

      {props &&
          props?.pageData &&
          props?.pageData?.acf && (
        props?.pageData?.acf?.our_commitment_title ||
        props?.pageData?.acf?.our_commitment_content ||
        props?.pageData?.acf?.our_commitment_logo ||
          props?.pageData?.acf?.our_commitment_image) && (
            <section id="our-commitment">
              <SingleTitleBanner
                imageSrc={props?.pageData?.acf?.our_commitment_image?.url}
                title={props?.pageData?.acf?.our_commitment_title}
                logoSrc={props?.pageData?.acf?.our_commitment_logo?.url}
                description={props?.pageData?.acf?.our_commitment_content}
              />
            </section>
          )}
      {props?.pageData?.acf?.sustainability_card &&
        props?.pageData?.acf?.sustainability_card?.length > 0 && (        
        <section className={`${styles.pb_120} ${styles.pt_120}`}>
          <div className="container">
            <ul className={`${styles.two_cl_layout}`}>
              {props?.pageData?.acf?.sustainability_card.map((card, idx) => (
                <li className={`${styles.mb_50}`} key={idx}>
                  <SustainabilityCard
                    imageSrc={card.image?.url}
                    imageAlt={card.image?.alt || 'image'}
                    leftBgColor={card.left_bg_color}
                    description={card.description}
                  />
                </li>
              ))}
            </ul>
          </div>
        </section>
      )}
      {props &&
        props?.pageData &&
        props?.pageData?.acf && (
          props?.pageData?.acf?.building_international_title ||
          props?.pageData?.acf?.building_international_content ||
          props?.pageData?.acf?.building_international_image) && (            
          <section className={`${styles.pb_120}`}>
              <ImageBg
                title={props?.pageData?.acf?.building_international_title}
                description={props?.pageData?.acf?.building_international_content}
                imageSrc={props?.pageData?.acf?.building_international_image?.url}
                imageAlt={props?.pageData?.acf?.building_international_image?.alt || 'How it all began'}
              />
          </section>
        )}
    </>
  )
}

export default Sdgs


export async function getStaticProps(locale) {
    const PageData = await getSustainability(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}
