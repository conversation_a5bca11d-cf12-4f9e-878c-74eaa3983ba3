import axios from "@/utils/axios";
import CONSTANTS from "@/utils/constants";
import { errorHandler } from "@/utils/utils";
import { getToken } from "@/utils/getToken";

const token = getToken(); // Get the token using the utility function

//Themeoptions

export const getThemeoptions = async (locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    const response = await axios.get(
      `${CONSTANTS.Themeoptions.ThemeoptionsApi}?lang=${locale}`,
      {
        headers,
      }
    );
    return response.data;
  } catch (error) {
    return errorHandler(error);
  }
};


// Home page
export const getHome = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.HomePage.HomeApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.HomeArPage.HomeArApi}?acf_format=standard`, {      
        headers,
      });
    }
//console.log('response', response)
    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// About us page
export const getAbout = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.AboutPage.AboutApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.AboutArPage.AboutArApi}?acf_format=standard`, {         
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Peregrina Center page

export const getPeregrinaCenter = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.PeregrinaCenterpage.PeregrinaCenterApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.PeregrinaCenterArPage.PeregrinaCenterArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Supply Chain page

export const getSupplyChain = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.SupplyChainpage.SupplyChainApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.SupplyChainArPage.SupplyChainArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Our Actives page

export const getOurActives = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.ActivesPage.ActivesApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.ActivesArPage.ActivesArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Actives Post Api 

export const getOurActivesPosts = async (locale, query) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.ActivesPost.ActivesPostApi}?acf_format=standard&per_page=100&_embed&lang=${locale}&${query}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Actives Post  Details Api 

export const getOurActivespostSlug = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.ActivesPost.ActivesPostApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Terms & Conditions page

export const getTermsConditions = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.TermsPage.TermsApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.TermsArPage.TermsArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};

// Privacy Policy page

export const getPrivacyPolicy = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.PrivacyPage.PrivacyApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.PrivacyArPage.PrivacyArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Hotels and Spas page

export const getHotelsAndSpas = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.HotelsPage.HotelsApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.HotelsArPage.HotelsArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Sustainability page

export const getSustainability = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.SustainabilityPage.SustainabilityApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.SustainabilityArPage.SustainabilityArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// Contact Us page

export const getContactus = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.ContactusPage.ContactusApi}?acf_format=standard`, {
        headers,
      });
    } else {
      response = await axios.get(`${CONSTANTS.ContactusArPage.ContactusArApi}?acf_format=standard`, {
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// News page
export const getNewspage = async (locale) => {
  try {
    let response;
    const headers = {
      Authorization: `Bearer ${token}`,
    };
    if (locale == "en") {
      response = await axios.get(`${CONSTANTS.NewsPage.NewsApi}?acf_format=standard`, { 
        headers,
      });
      } else {
      response = await axios.get(`${CONSTANTS.NewsArPage.NewsArApi}?acf_format=standard`, {              
        headers,
      });
    }

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


// News Api 
export const getInsightsPosts = async (locale, query) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&_embed&lang=${locale}&${query}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};


//Insights post details

export const getInsightspostSlug = async (slug, locale) => {
  try {
    const headers = {
      Authorization: `Bearer ${token}`,
    };   
    const response = await axios.get(
      `${
        CONSTANTS.InsightPost.InsightPostApi}?acf_format=standard&per_page=100&lang=${locale}&slug=${slug ? slug : ""}`,
      {
        headers,
      }
    );

    return response.data;
  } catch (error) {
    console.log("error", error);
    return errorHandler(error);
  }
};



