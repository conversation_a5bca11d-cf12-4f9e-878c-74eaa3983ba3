@import "@/public/styles/mixins/mixins";
 

.centerWrapper {
   width: 100vw;
   height: 100vh;
   overflow-x: hidden;
   background: #231915;
 
   .header_container {
     width: 1200px;
     margin: 0 auto;
     display: flex;
 
     h1 {
       color: white;
     }
   }
 
   .lifeCycleContainer {
     width: 1200px;
     height: 600px;
     display: flex;
     gap: 10px;
     border-radius: 24px;
     overflow: hidden;
     box-shadow: 0 4px 32px rgba(0, 0, 0, 0.12);
     background: none;
     margin: 0 auto;
 
     .imageSection {
       position: relative;
       width: 65%;
       height: 100%;
       overflow: hidden;
       border-radius: 24px 0 0 24px;
 
       .imageFadeContainer {
         position: relative;
         width: 100%;
         height: 100%;
         opacity: 0;
         animation: fadeIn 0.8s ease-in-out forwards;
       }
 
       @keyframes fadeIn {
         to {
           opacity: 1;
         }
       }
 
       img {
         width: 100%;
         height: 100%;
         object-fit: cover;
         border-radius: 24px 0 0 24px;
       }
 
       .imageOverlay {
         position: absolute;
         left: 0;
         bottom: 0;
         width: 100%;
         padding: 32px;
         color: #fff;
         border-radius: 0 0 0 24px;
 
         h2 {
           margin: 0 0 8px 0;
           font-size: 2rem;
           font-weight: 700;
         }
         h3 {
           margin: 0 0 16px 0;
           font-size: 1.25rem;
           font-weight: 400;
         }
         p {
           margin: 0;
           font-size: 1.1rem;
           font-weight: 300;
         }
       }
     }
 
     .listSection {
       width: 35%;
       height: 100%;
       background: #f7f1ea;
       display: flex;
       flex-direction: column;
       align-items: flex-start;
       padding: 40px 0 40px 0;
       border-radius: 24px;
       box-sizing: border-box;
 
       .list {
         list-style: none;
         padding-left: 40px;
         // margin: 0 0 0 40px;
         // background: yellow;
         margin: auto 0;
         width: 320px;
         display: flex;
         flex-direction: column;
         // gap: 24px;
         height: 100%;
         // background: yellow;
 
         li {
           display: flex;
           align-items: center;
           gap: 24px;
           padding-left: 20px;
           height: 25%;
           border-radius: 16px;
           cursor: pointer;
           background: transparent;
           transition: all 0.2s;
 
           &:hover,
           &.active {
             background: #f3e3d2;
           }
 
           .iconWrapper {
             width: 64px;
             height: 64px;
             display: flex;
             align-items: center;
             justify-content: center;
             background: #231915;
             border-radius: 50%;
 
             // Active or hover state for icon
             li.active &,
             li:hover & {
               background: #e7d3c0;
             }
           }
 
           span {
             strong {
               font-weight: bold;
               font-size: 1.5rem;
             }
             .period {
               color: #6b5c4a;
               font-size: 1.1rem;
               font-weight: 400;
             }
           }
         }
       }
     }
   }
 }
 
 .dummy_div_hight_for_scroll {
   width: 100vw;
   height: 200px;
   background: yellow;
 }
 