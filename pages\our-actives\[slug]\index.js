import React, { useState, useEffect, useRef } from "react";
import styles from "./virgin.module.scss";
import InnerBanner from "@/components/innerBanner/InnerBanner";
import BoxDesc from "@/components/boxDescription/BoxDesc";
import AOS from "aos";
import "aos/dist/aos.css";
import ImageBg from "@/components/imageBgSet/imageBg";


import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getOurActivespostSlug, getOurActivesPosts } from "@/utils/lib/server/publicServices"; 

const Index = (props) => {
  useEffect(() => {
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });
  }, []);

   const router = useRouter();
   const yoastData = props?.pageData?.yoast_head_json;
   if (!props?.pageData) {
        return null;
   }
  
  return (
    <>
      
      {yoastData && <Yoast meta={yoastData} />}
     
       {props &&
            props?.pageData &&
        props?.pageData?.acf && (            
            props?.pageData?.acf?.banner_text ||
            props?.pageData?.acf?.sub_text ||
            props?.pageData?.acf?.banner_image) && (   
              <div id="banner">
                  <InnerBanner
                      bannerBg={props?.pageData?.acf?.banner_image?.url}
                      title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text || props?.pageData?.title?.rendered)}
                      content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                  />  
               </div>
          )}
      
        {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.jfc_title || 
        props?.pageData?.acf?.jfc_content ||
        props?.pageData?.acf?.jfc_image ||
        props?.pageData?.acf?.jfc_button ||
        props?.pageData?.acf?.jfc_patent_text ||
        props?.pageData?.acf?.jfc_sub_title) && (
            
      <section
        className={`${styles.pb_120} ${styles.pt_120} ${styles.active_sec}`}
      >
        <div className="container">
          <BoxDesc
            imageSrc={props?.pageData?.acf?.jfc_image?.url}
            shortTtile={props?.pageData?.acf?.jfc_patent_text}
            title={props?.pageData?.acf?.jfc_title}
            position="right"
            subTitle={props?.pageData?.acf?.jfc_sub_title}
            buttonLink={props?.pageData?.acf?.jfc_button?.url}
            buttonText={props?.pageData?.acf?.download_pdf_text}
            buttonType="download"
            content={props?.pageData?.acf?.jfc_content}
          />
        </div>
          </section>
        )}

      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.enquire_now_title || 
        props?.pageData?.acf?.enquire_now_content ||
        props?.pageData?.acf?.enquire_now_image ||
        props?.pageData?.acf?.enquire_now_button) && (    
      <section className={`${styles.pb_120}`}>       
        <ImageBg
          title={props?.pageData?.acf?.enquire_now_title}           
          description={props?.pageData?.acf?.enquire_now_content}
          imageSrc={props?.pageData?.acf?.enquire_now_image?.url}
          imageAlt={props?.pageData?.acf?.enquire_now_image?.alt || 'image'}
          buttonLink={props?.pageData?.acf?.enquire_now_button?.url}
          buttonText={props?.pageData?.acf?.enquire_now_button?.title}
        />
      </section>
      )}
      {/* <section className={`${styles.pb_120} `}>
        <ImageBg
          title="Contact us for any commercial inquires"
          description={`For enquiries of any kind please fill out the form below and a member of the team will be in touch.`}
          imageSrc="/images/enquire_banner.jpg"
          imageAlt="How it all began"
          buttonLink="/contact"
          buttonText="Enquire Now"
        />
      </section> */}
    </>
  );
};

export default Index;



export async function getStaticPaths(locale) {  
   
const OurActivesPostslug = await getOurActivesPosts(locale.locales); // Fetch all advisory posts
    const paths = OurActivesPostslug.map((post) => ({
        params: { slug: post.slug },
    }));   

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}

export const getStaticProps = async ({params, locale}) => {
  const slug = params.slug; 
  const PageData = await getOurActivespostSlug(slug, locale);  
   console.log("Generated:", PageData[0]);

  return {
    props: {      
      pageData: PageData[0] || null, // use lowercase 'p'
    },
    revalidate: 10,
  };
};



