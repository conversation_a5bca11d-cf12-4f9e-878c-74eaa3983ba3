import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const CosmeticCard = ({ data }) => {
    const { bg_img, title } = data;
    const cardRef = useRef(null);

    useEffect(() => {
        let ctx = gsap.context(() => {
            gsap.fromTo(
                cardRef.current,
                { opacity: 0, y: 50, scale: 0.9 },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 0.8, // Faster animation
                    ease: "power3.out", // Snappier motion
                    scrollTrigger: {
                        trigger: cardRef.current,
                        start: "top 85%",
                        end: "top 40%",
                        scrub: 0.5, // Slight scroll effect
                        toggleActions: "play reverse play reverse",
                    }
                }
            );
        });

        return () => ctx.revert(); // Cleanup on unmount
    }, []);

    return (
        <div className={styles.cosmetic_card} ref={cardRef}>
            <div className={styles.cos_card_content}>
                <div className={styles.cos_image}>
                    <Image src={bg_img} width={309} height={319} alt="image" quality={100} />
                </div>
                <h3 dangerouslySetInnerHTML={{ __html: title }} />
            </div>
        </div>
    );
}

export default CosmeticCard;
