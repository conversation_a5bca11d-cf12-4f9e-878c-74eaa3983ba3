@import "@/public/styles/mixins/mixins";

:global(body.mob_menu) {
    .main_banner{
        z-index: -1;
        position: relative;
      }
}
.main_banner{
    height: 100%;
    img{
      width: 100%;
      // border-radius: 20px;
    }
    .container{
     max-width: 100%;
     height: 100%;
     position: relative;
    }
    video{
     width: 100%;
     height:100%;
     object-fit: cover;
    //  border-radius: 20px;
    //  @include max(1550) {
    //     height: 810px;
    //  }
    //  @media (min-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
    //     height: 640px;
    // }
    // @media (min-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
    //     height: 640px;
    // }
    //  @include max(1060) {
    //    height: 550px;
    //  }
    //  @include max(992) {
    //    height: 650px;
    //    border-radius:12px;
    //  }
    //  @include max(767) {
    //    height: 400px;
    //  }
    }
    .banner_title{
        position: absolute;
        bottom: 190px;
        left: 130px;
        max-width: 745px;
        z-index: 9;
        h1{
            margin-bottom: 0;
            @include max(1060) {
              font-size: 2.1rem;
            }
            @include max(767) {
                font-size: 1.6rem;
            }
        }
        @include max(1600) {
            max-width: 710px;
        }
        @include max(1060) {
            bottom: 25px;
            left: 0;
            padding: 30px;
            width: 100%;
            text-align: center;
            right: 0;
            margin: 0 auto;
        }
        @include max(992) {
          bottom: 50%;
          transform: translateY(50%);
        }
        @include max(767) {
            padding: 15px 20px 15px 20px;
            text-align: center;
            width: 100%;
        }
    }
 }
 :global(body.rtl) {
  .main_banner{
     .banner_title{
       right: 130px;
       left: auto;
     }
  }
}
.loaderWrapper {
  position: fixed;
  inset: 0;
  background: #34241D;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.lottieWrapper {
  transition: transform 1.6s ease-in-out, opacity 1.6s ease-in-out;
  transform: scale(1);
  opacity: 1;
  transform-origin: center center;
  z-index: 10;
}

.zoomOut {
  transform: scale(6); // Bigger zoom like your video
  opacity: 0;
}

.wrapper {
  position: relative;
  width: 100%;
  height: 900px;
  overflow: hidden;
  background: #34241D;
  // &::after{
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   background: linear-gradient(rgba(0, 0, 0, 0.7), rgb(0 0 0 / 70%)), linear-gradient(rgba(0, 0, 0, 0.2), rgb(26 26 26 / 20%));
  //   height: 100%;
  //   width: 100%;
  // }
  @include max(1550) {
    height: 810px;
  }
  @include max(1060) {
    height:550px;
  }
  @include max(992) {
    height: 100vh;
  }
}

.bgVideo {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  // opacity: 0;
  // transform: scale(0);
  // transition: opacity 2s ease-in-out, transform 2s ease-in-out;
  z-index: 0;
}

.bgVideo.show {
  opacity: 1;
  transform: scale(1);
}



.loder_mian{ position: absolute; width: 100%; height: 100%; left: 0; top: 0; z-index: 1000; display: flex; flex-wrap: wrap; align-items: center; justify-content: center; }
// background: #34241d;

.logo_block{ width: 100%; margin-left: auto; margin-right: auto;  width: 10%; 

  &:after{ content: ""; display: block; width: 100%; height: 100%; position: absolute; }

}

.animation_end{ display: none;}

.logo_mask_img{position: absolute; left: 0; top: 0;}


.p_relative{ position: relative;
  &::after{ content: ""; display: block; width: 100%; height: 100%; background: #34241D; position: absolute;}
}
// background: #34241d; 


 .logo_block_main_top{ background: #34241D; width: 100%; height: 35.75%;


  @include max(1440) { height: 26.9%; }

  @include max(600) { height: 36.49%;}




}
 .logo_block_main_center{ height: 28.5%; width: 100%;  display: flex; flex-wrap: wrap;


  @include max(1440) { 
    height: 46.2%;
  }

}
 .logo_block_main_bottom{ height: 35.75%; width: 100%; background: #34241D; position: relative;

  @include max(1440) { height: 26.9%; }

  @include max(600) { height: 36.49%;}



}
 .logo_block_main_center_left{ width: 44.9%;  height: 100%;  background: #34241D;  position: relative;


  @include max(1440) {
    width: 37.5%;
  }
  @include max(600) {
    width:26%;
  }
  &::after{ display: block; width: 100%; height: 100px; background:#34241D; position: absolute; content: ""; z-index: -1; top: -1%;}
  &::before{ display: block; width: 100%; height: 100px; background:#34241D; position: absolute; content: ""; z-index: -1; bottom: -1%;}


}
.logo_block_main_center_center{ width:10.2%; height: 100%; position: relative; transform: scale(1.01);
 
  :global{
    .logo_block_ani{ background: #34241D;  opacity: 1;  transition: transform 0.3s ease, opacity 0.3s ease; }
    .active_logo{ opacity: 0;  transition: transform 0.3s ease, opacity 0.3s ease;}
    .logo_block_ani{ background: #34241D; width: 100%; height: 100%;}
  }

  @include max(1440) {
    width: 25%;
  }

  @include max(600) {
    width:48%;
  }


  img{ width: 100%;
  
    @include max(1480) {
      width: 75%;
      margin: 0 auto;
    }
  }

}
 .logo_block_main_center_right{ width: 44.9%;  height: 100%;  background: #34241D; position: relative;

  @include max(1440) {
    width: 37.5%;
  }

  @include max(600) {
    width:26%;
  }

    &::after{ display: block; width: 100%; height: 100px; background:#34241D; position: absolute; content: ""; z-index: -1; top: -1%;}
    &::before{ display: block; width: 100%; height: 100px; background:#34241D; position: absolute; content: ""; z-index: -1; bottom: -1%;}

} 

 .logo_block_mask{ position: absolute; left: 0; top: 0; width: 100%; height: 100%;
 
  &::after{     display: block;
    content: "";
    width: 100%;
    height: 100%;
    /* top: 0; */
    background: #34241D;
    left: 0;
    position: absolute;}
  // img{
  //    height: 100%;
  //    object-fit: contain;
  // }
}


 .active_logo { opacity: 0;}