import React, { useRef, useEffect } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const IngredientsCard = ({ image, title, content }) => {
  const cardRef = useRef(null);
  const imageRef = useRef(null);
  const contentRef = useRef(null);

  useEffect(() => {
    const card = cardRef.current;
    const img = imageRef.current;
    const contentText = contentRef.current;

    // Image Animation
    gsap.fromTo(
      img,
      { opacity: 0, x: -50 },
      {
        opacity: 1,
        x: 0,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: card,
          start: 'top 80%',
          toggleActions: 'play reverse play reverse',
        },
      }
    );

    // Text Animation (Title & Paragraph)
    gsap.fromTo(
      contentText,
      { opacity: 0, x: 50 },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        ease: 'power3.out',
        delay: 0.2,
        scrollTrigger: {
          trigger: card,
          start: 'top 80%',
          toggleActions: 'play reverse play reverse',
        },
      }
    );
  }, []);

  return (
    <div className={styles.ingredient_card} ref={cardRef}>
      <div className={styles.ing_image} ref={imageRef}>
        <Image src={image} width={228} height={289} alt="image" />
      </div>
      <div className={styles.ing_content} ref={contentRef}>
        <h4>{title}</h4>
        <div className={styles.desc}>
            <p>{content}</p>
        </div>
      </div>
    </div>
  );
};

export default IngredientsCard;
