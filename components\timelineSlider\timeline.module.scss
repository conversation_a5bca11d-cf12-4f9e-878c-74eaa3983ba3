@import "@/public/styles/mixins/mixins";

.timelineContainer {
    position: relative;
    background-image: url(/images/timeline_bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    // padding: 120px 0;
    // display: flex;
    // flex-wrap: wrap;
    align-items: center;
    height:920px;
    padding-top: 60px;
    .main_title{
        text-align: center;
        color: #34241D;

        @include max(767) {
            text-align: left;
        }
    }
    @include max(1600) {
        height:800px;
    }
    @include max(1550) {
        height:760px;
    }
    @include max(1300) {
        height:690px;
    }
    @include max(992) {
        height: 650px;
        height:auto;
        padding: 50px 0;
    }
    @include max(767) {
        height:auto;
        padding: 40px 15px;
    }

    .timelineTop {
        display: flex;
        align-items: center;
        gap: 105px;
        min-height: 550px;
        @include max(1600) {
            gap: 60px;
            min-height: 440px;
        }
        @include max(1300) {
            min-height: 390px;
        }
        @include max(1060) {
            gap: 30px;
            min-height: 440px;
        }
        @include max(992) {
            flex-wrap: wrap;
            gap: 50px;
        }
        @include max(767) {
            gap: 30px;
        }
    }

    .content {
        padding-left: calc((100% - 1400px) / 2);
        width:50%;

        h3 {
            font-size: size(54px);
            margin-bottom: 20px;
            font-weight: 300;
            color: #34241D;
            max-width: 75%;
            @include max(1600) {
                font-size: size(48px);
            }
            @include max(1300) {
                font-size: size(42px);
            }
            @include max(992) {
                font-size: size(40px);
            }
            @include max(767) {
                font-size: size(21px);
                line-height: size(32px);
                max-width: 100%;
            }
            
        }

        p {
            color: #34241D;
            font-weight: 400;
            font-size: size(18px);
            line-height: size(30px);
            margin-bottom: 30px;
            strong{
                font-weight: 600;
            }
            @include max(1550) {
                font-size: size(16px);
                line-height: size(27px);
            }
            @include max(1300) {
                font-size: size(15px);
                line-height: size(26px);
            }
            @include max(767) {
                font-size: size(15px);
                line-height: size(25px);
            }
        }
        @include max(1600) {
            padding-left: calc((100% - 1200px) / 2);
            width: 45%;
        }
        @include max(1300) {
            padding-left: calc((100% - 1025px) / 2);
            width: 45%;
        }
        @include max(1250) {
            padding-left: 4%;
        }
        @include max(1060) {
            width:40%;
        }
        @include max(992) {
            width: 100%;
            padding-right: 4%;
        }
        @include max(767) {
            width: 100%;
            padding-left: 0%;
            padding-right: 0%;
        }
    }
    .timeline_set{
        width: 100%;
    }

    .images {
        display: flex;
        align-items: center;
        width: 60%;
        justify-content: flex-end;
        margin-left: auto;

        @include min(1601) {
            padding-right: calc((100% - 1400px) / 2);
        }
        @include max(1550) {
            width: 42%;
            justify-content:flex-start;
            margin-left: 45px;
            gap: 15px;
        }
        @include max(992) {
            width: 100%;
            // flex-wrap: wrap;
            // justify-content:center;
            margin-left: 0;
        }
        @include max(767) {
            // flex-wrap: wrap;
            gap: 10px;
            min-height: unset;
            // min-height: 620px;
        }
        .imageBox {
            text-align: center;
            width: 100% !important;
            @include max(1550) {
                width: 83% !important;
                margin: 0 auto;
            }
            img{
                // box-shadow: 0px 0px 30px rgb(0 0 0 / 17%);
                display: block;
                @include max(1060) {
                    width: 100%;
                }
            }
            &:first-child {
                position: relative;
                right:0;
                // transform: rotate(355deg);
                z-index: 9;
                width: 50%;
                @include max(1060) {
                    width: auto;
                    right: initial;
                }
                @include max(767) {
                    right: 0;
                }
                p{
                    transform: translateY(15px);
                    @include max(1600) {
                        // transform: translate(-30px, 10px);
                    }
                    @include max(1250) {
                        transform: initial
                    }
                }

            }
            &:nth-child(2){
                // transform: rotate(9deg);
                
                // @include max(767) {
                //     width: 80% !important;
                //     margin: 0 auto;
                // }
                p{
                    transform:translate(110px, 15px);
                    @include max(1600) {
                        transform:translate(105px, 10px);
                    }
                    @include max(1060) {
                        transform: translate(40px, 5px);
                    }
                    @include max(767) {
                        transform: translate(0, 5px);
                    }
                }
            }

            p {
                font-size: size(16px);
                margin-top: 5px;
                color: #7F4930;
                font-family: var(--font-obsolete);
                max-width: 270px;
                @include max(1600) {
                    font-size: size(15px);
                }
                @include max(1250) {
                    font-size: size(14px);
                    line-height: size(23px);
                }
                @include max(767) {
                    font-size: size(13px);
                    line-height: size(18px);
                }
            }
            @include max(820) {
                width: 47% !important;
                padding-left: 2%;
                padding-right: 2%;
            }


            @include max(767) {
                width: 100% !important;
            }
            
        }
        &.singleImage{
            // justify-content: flex-start;
            // margin: 0 auto;
            .imageBox{
                right: 0;
                width: auto;
                @include max(1060) {
                    width: 70%;
                    margin-left: auto;
                }
                @include max(992) {
                    width: 50%;
                    margin:0 auto;
                }
            }

            > div{ width: 100%;}

            .imageBox{
                img{ width: 100%;}
            }
        }
    }

    .timelineSwiper {
       margin: 0;
        text-align: center;

        .swiper-slide {
            button {
                background: none;
                border: none;
                font-size: 14px;
                cursor: pointer;
                color: #777;
                padding: 5px 10px;
                transition: color 0.3s;
            }
        }

        .activeYear button {
            font-weight: bold;
            color: #000;
            border-bottom: 2px solid #000;
        }
    }

    .timelineSwiper {
        justify-content: center;
        overflow: visible;
        width:100%;
        @include max(1600) {
            width: 100%;
            text-align: left;
        }
        @include max(1060) {
            padding-top:100px;
        }
        @include max(767) {
            // width: 320px;
            padding-top:40px;
            overflow: hidden;
        }
        :global {
            .swiper-wrapper {
                justify-content: center;
                max-width: 890px;
                // max-width: 1000px;
                margin: 0 auto;
                position: relative;
                gap: 80px;
                // gap: 40px;
                padding-top: 15px;
                transition: all 1s ease-in-out;
                @include max(1550) {
                    max-width: 750px;
                    gap: 65px;
                }
                @include max(1300) {
                    max-width: 675px;
                }
                @include max(1060) {
                    max-width:max-content;
                    width: max-content;
                    justify-content: flex-start;
                }
                @include max(767) {
                    gap: 30px;
                }
                &::after {
                    content: "";
                    display: block;
                    width: 100%;
                    height: 1px;
                    background-color: #7F4930;
                    position: absolute;
                    left: 0;
                    top: 0;
                    z-index: -1;
                }
            }

            .swiper-slide {
                width: auto;
                transition: all 1s ease-in-out;
                &.activeYear{
                    .year_item{
                        transform: translateY(-70px);
                        font-size: size(32px);
                        font-weight: 300;
                        z-index: 5;
                        @include max(1060) {
                           font-size: size(30px);
                           line-height: size(40px);
                        }
                        @include max(767) {
                            font-size: size(28px);
                            line-height: size(30px);
                            min-height: 35px;
                            transform: translateY(-60px);
                         }
                        &:after{
                            top: 48px;
                            background-color: #FCE1CB;
                            border: 1px solid #7F4930;
                            @include max(1550) {
                                top: 50px;
                            }
                            @include max(767) {
                                top: 40px;
                            }
                        }
                        span{
                            br{
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }
}

.year_item {
    color: #34241D;
    font-size: size(16px);
    font-weight: bold;
    position: relative;
    cursor: pointer;
    transition: all 1s ease-in-out;
    min-height: 46px;
    &::after {
        content: "";
        width: 11px;
        height: 11px;
        background-color: #34241D;
        border: 2px solid #FCE1CB;
        border-radius: 50%;
        display: block;
        position: absolute;
        top: -23px;
        left: 0;
        // left: 50%;
        // transform: translateX(-50%);
        z-index: 1;
        transition: all 1s ease-in-out;
        @include max(1550) {
            width: 9px;
            height: 9px;
            top: -21px;
        }
        
        @include max(1060) {
            left: 20px;
        }
    }
    @include max(1300) {
        font-size: size(14px);
    }
}
.timelineBottom{
   display: flex;
   margin-top: 100px;
   padding: 0 8%;
//    align-items: center;
    @include max(1600) {
        margin-top: 70px;
    }
    @include max(1060) {
        flex-wrap: wrap;
        margin-top: 30px;
        padding: 0;
    }
}
.custom_nav{
    width: 10%;
    font-family: var(--font-obsolete);
    display: flex;
    gap: 30px;
    position: relative;
    top: -30px;
    left: 0;
    align-items: center;
    z-index: 9;
    display: none;
    @include max(1600) {
        left: -40px;
    }
    @include max(992) {
        left: 0;
        top: 20px;
    }
    @include max(767) {
        // display: flex;
        top: 100px;
    }
    :global{

        .swiper_button_prev{
            position: relative;
            color: #34241D;
            opacity: 1;
            &:hover{
                color: var(--hover-color);
            }
           &::before{
              position: absolute;
              content: "";
              right: -15.5px;
              width: 1.2px;
              height: 100%;
              background-color: #34241D;
              
           }
        }
        .swiper_button_next{
            color: #34241D;
            opacity: 1;
            &:hover{
                color: var(--hover-color);
            }
        }
    }
    // padding-left: calc((100% - 1400px) / 2);
    // display: flex;
}
:global(body.rtl){
    .timelineContainer{
        .content{
            padding-left: 0;
            padding-right: calc((100% - 1400px) / 2);
        }
        .images{
            @include min(1601) {
                padding-right: 0;
                padding-left: calc((100% - 1400px) / 2);
            }

            > div{ width: 100%;}
        }
    }
    .custom_nav{
        :global{
            .swiper_button_prev{
               &::before{
                  right: auto;
                  left: -15.5px;
               }
            }
        }
    }
}


.singleWrapper_two{ width: 100%;}