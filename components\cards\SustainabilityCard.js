import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";

const SustainabilityCard = ({
  imageSrc,
  imageAlt = '',
 
  description,
  bgColor = '#FDE5D2', // default right block color
  leftBgColor = '#EF4423', // default left block color
}) => {
  return (
    <div className={styles.card_sustainability} style={{ background: bgColor }}>
      <div className={styles.left} style={{ background: leftBgColor }}>
       {imageSrc && (
          <Image src={imageSrc} alt={imageAlt} width={196.59} height={196.6} quality={100} />
        )}  
      </div>
      <div className={styles.right}>
        <p>{description && parse(description)}</p>
      </div>
    </div>
  );
};

export default SustainabilityCard;
