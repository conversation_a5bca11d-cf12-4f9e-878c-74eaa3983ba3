import React, { useState, useEffect, useRef } from 'react';
import styles from './aboutHm.module.scss';
import Link from 'next/link';
import Icons from '@/public/Icons';
import Parallax from "@/components/Paralax";
import AOS from "aos";
import "aos/dist/aos.css";
import { useInView } from "react-intersection-observer";
import Image from 'next/image';

const AboutSec = ({ 
  backgroundImage, 
  subtitle, 
  mainTitle, 
  description, 
  buttonText, 
  buttonLink, 
  iconName 
}) => {
  const sectionRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 } // Adjust threshold for when the fade-in should trigger
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);
  useEffect(() => {
    AOS.init({
      easing: "ease-out-cubic",
      once: false,
      offset: 50,
    });
  }, []);

  const { ref, inView } = useInView({
    triggerOnce: false,  // Keep triggering when entering/leaving
    threshold: 0.2,      // Fires when 20% of the element is visible
  });

  return (
    <div 
      ref={ref}
      className={`${styles.invitation_center}  ${inView ? 'fadeIn' : ""}`} 
    > 
        <div className={`${styles.invite_img} fade_effect`}>
            {/* <Parallax mediaSrc={backgroundImage} altText='image' /> */}
            <Image src={backgroundImage} alt='image' width={1300} height={675} quality={100} />
        </div>
      <div className={`${styles.invite_text} text_effect`}>
        {subtitle && <h6>{subtitle}</h6>}
        {mainTitle && <h2 className="main_title">{mainTitle}</h2>}
        {description && <p>{description}</p>}
        {buttonText && buttonLink && (
          <Link href={buttonLink} className="main_btn">
            <span className='button_text'>{buttonText}</span> 
            {iconName && <span className='arrow'><Icons size={25} color="#fff" icon={iconName} /></span>}
          </Link>
        )}
      </div>
    </div>
  );
};

export default AboutSec;
