import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';

const GraphCard = ({data}) => {
  const {title,image} = data
  return (
    <div className={styles.graph_card}>
        <div className={styles.graph_img}>
            <Image src={image} width={286} height={166} alt='image' quality={100}/>
        </div>
        <h4>{title}</h4>
    </div>
  )
}

export default GraphCard