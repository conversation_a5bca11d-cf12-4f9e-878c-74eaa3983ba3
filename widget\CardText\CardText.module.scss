@import "@/public/styles/mixins/mixins";



.product_list_item {
    font-size: size(26px);
    line-height: size(34px);
    background-color: #fce1cb;
    height: 285px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    font-weight: 600;
    background-image: url(/images/product_bg.png);
    background-repeat: no-repeat;
    background-position: left bottom;
    margin: 0;
    padding: 0 85px;
    text-align: center;
    transition: all 0.5s ease;
    @include max(1550) {
        font-size: size(24px);
        line-height: size(30px);
        background-size: 60%;
        height: 270px;
        padding: 0 70px;
    }
    @include max(1440) {
        height: 260px;
    }

    @include max(1024) {
        padding: 0 30px;
        //   height: 210px;
        font-size: size(20px);
        line-height: size(30px);
    }

    @include max(767) {
        font-size: size(16px);
        line-height: size(22px);
        //   width: 200px;
        height: unset;
        min-height: 200px;
        padding: 0 20px;
        background-size: 60%;
    }
}