import React, { useState, useEffect, useRef } from "react";
import styles from './acceditations.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import CommonLink from '@/components/commonLinks/CommonLink';
import Breadcumbs from "@/components/breadcrumbs/breadcumb";
import Image from 'next/image';
import AOS from "aos";
import "aos/dist/aos.css";
import SingleTitleBanner from "../single_title_banner";

const Acceditations = ({ title, imageSrc, data }) => {
      useEffect(() => {
        AOS.init({
          easing: "ease-out-cubic",
          once: false,
          offset: 50,
        });
      }, []);
    
    return (
        <>
           {imageSrc && title && (
            <SingleTitleBanner
                imageSrc={imageSrc}
                title={title}
            />
            )}
            {data && data.length > 0 && (
                <div className={`${styles.acc_sec} ${styles.pt_120} ${styles.pb_120}`}>
                    <div className={styles.leaf}>
                        <Image src='/images/story_leaf.png' width={337} height={556} />
                    </div>
                    <div className={`${styles.container} container`}>
                        <ul className={styles.logo_list}>
                            {data.map((logoList, index) => (
                                <li key={index} data-aos="fade-up" data-aos-duration="1000">
                                    <div className={styles.logo_img}>
                                        <Image src={logoList?.url} width={245} height={224} alt="image" quality={100} />
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            )}

        </>
    )
}

export default Acceditations