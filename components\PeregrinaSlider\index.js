
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import style from "./TreeLifeCycle.module.scss";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import parse from "html-react-parser";

const Stacksection = ({ title, contentData }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const sectionRef = useRef(null);
  const containerRef = useRef(null);
  const [isSticky, setIsSticky] = useState(false);

  const getCurrentContent = () => {
    return contentData[activeIndex] || contentData[0];
  };

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    const container = containerRef.current;
    const cards = gsap.utils.toArray(".card");

    if (!container || cards.length === 0) return;

    // Clear any existing active classes
    cards.forEach(card => card.classList.remove(style.active));

    // Single ScrollTrigger to handle everything
    const mainScrollTrigger = ScrollTrigger.create({
      trigger: container,
      start: "top top",
      end: "+=1000",
      pin: true,
      // markers: true,
      scrub: 0.1,
      anticipatePin: 1,
      onUpdate: (self) => {
        const progress = self.progress;
        const index = Math.floor(progress * contentData.length);
        const clampedIndex = Math.min(Math.max(index, 0), contentData.length - 1);

        // Update active index
        setActiveIndex(clampedIndex);

        // Update card active states
        cards.forEach((card, cardIndex) => {
          if (cardIndex === clampedIndex) {
            card.classList.add(style.active);
          } else {
            card.classList.remove(style.active);
          }
        });
      },
      onEnter: () => {
        setIsSticky(true);
        setActiveIndex(0);
        // Ensure first card is active
        cards.forEach((card, index) => {
          if (index === 0) {
            card.classList.add(style.active);
          } else {
            card.classList.remove(style.active);
          }
        });
      },
      onLeave: () => {
        setIsSticky(false);
        // Keep the last item active when leaving forward
        const lastIndex = contentData.length - 1;
        setActiveIndex(lastIndex);
        cards.forEach((card, index) => {
          if (index === lastIndex) {
            card.classList.add(style.active);
          } else {
            card.classList.remove(style.active);
          }
        });
      },
      onEnterBack: () => {
        setIsSticky(true);
        // Keep the last item active when re-entering from below
        const lastIndex = contentData.length - 1;
        setActiveIndex(lastIndex);
        cards.forEach((card, index) => {
          if (index === lastIndex) {
            card.classList.add(style.active);
          } else {
            card.classList.remove(style.active);
          }
        });
      },
      onLeaveBack: () => {
        setIsSticky(false);
        // Keep the first item active when leaving backward
        setActiveIndex(0);
        cards.forEach((card, index) => {
          if (index === 0) {
            card.classList.add(style.active);
          } else {
            card.classList.remove(style.active);
          }
        });
      },
    });

    return () => {
      mainScrollTrigger.kill();
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  const currentContent = getCurrentContent();

  return (
    <div
      ref={sectionRef}
      className={`${style.stack_Section} ${isSticky ? style.sticky : ''}`}
    >
      <div className={`${style.container} container`}>
        <div ref={containerRef} className={style.tree_cycle_block_container}>
          {title && (
            <h2 className="main_title" data-aos="fade-up" data-aos-duration="1000">{parse(title)}
            </h2>
          )}
          <div className={`${style.stack_block}`}>
            <div className={`${style.stack_block_left}`}>
              <div className={`${style.stack_block_left_inner}`}>
                <div className={`${style.txt_block}`}>
                  <div className={`${style.w_100}`}>
                    {currentContent.cycle_name && <h3>{parse(currentContent.cycle_name)}</h3>}
                    {currentContent.cycle_label && <h4>{parse(currentContent.cycle_label)}</h4>}
                    {currentContent.cycle_description && (parse(currentContent.cycle_description))}
                  </div>
                </div>

                <Image
                  src={currentContent.cycle_image?.url}
                  alt={currentContent.cycle_image?.alt || "Cycle Image"}
                  width={816}
                  height={546}
                  priority
                />
              </div>
            </div>
            <div className={`${style.stack_block_right}`}>
              <ul>
                {contentData && contentData.map((item, index) => (
                  <li key={index} className={`card ${activeIndex === index ? style.active : ''}`}>
                    {item.cycle_icon && (
                      <div className={`${style.stack_icn_block}`}>
                        <Image
                          src={item.cycle_icon?.url}
                          alt={item.cycle_icon?.alt || "Cycle Icon"}
                          width={43}
                          height={56}
                        />
                      </div>
                    )}
                    <div className={`${style.stack_txt_block}`}>
                      {item.cycle_name && <h4>{parse(item.cycle_name)}</h4>}
                      {item.cycle_label && <p>{parse(item.cycle_label)}</p>}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default Stacksection;