import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const ResilienceCard = ({ pic, title, content }) => {
  const cardRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);

  useEffect(() => {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: cardRef.current,
        start: "top 80%", 
        end: "bottom 20%",
        toggleActions: "play reverse play reverse", // Ensures animation plays again when re-entering
        markers: false,
      }
    });

    // Image Fade In
    tl.fromTo(
      cardRef.current,
      { opacity: 0, y: 50 },
      { opacity: 1, y: 0, duration: 1, ease: "power3.out" }
    );

    // Title Animation
    tl.fromTo(
      titleRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" },
      "-=0.5" // Overlaps animation
    );

    // Content Animation
    tl.fromTo(
      contentRef.current,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" },
      "-=0.4" // Overlaps animation
    );
    
  }, []);

  return (
    <div className={styles.resilience_card} ref={cardRef}>
      {pic && (
        <div className={styles.rs_image}>
          <Image src={pic} width={376} height={285} alt="image" />
        </div>
      )}
      <div className={styles.rs_desc}>
        {title && <h3 ref={titleRef}>{title}</h3>}
        {content && <p ref={contentRef}>{content}</p>}
      </div>
    </div>
  );
};

export default ResilienceCard;
