// Add custom REST API endpoints for tracking
add_action('rest_api_init', function () {
    register_rest_route('custom/v1', '/track', array(
        'methods' => 'POST',
        'callback' => 'handle_post_tracking',
        'permission_callback' => '__return_true'
    ));
    
    register_rest_route('custom/v1', '/stats/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_post_stats',
        'permission_callback' => '__return_true'
    ));
});

function handle_post_tracking($request) {
    global $wpdb;
    
    $post_id = $request->get_param('post_id');
    $ip_address = $request->get_param('ip_address');
    $action = $request->get_param('action');
    
    $table_name = $wpdb->prefix . 'post_tracking';
    
    // Create table if it doesn't exist
    $wpdb->query("CREATE TABLE IF NOT EXISTS $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        post_id int(11) NOT NULL,
        ip_address varchar(45) NOT NULL,
        action varchar(10) NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )");
    
    // For views, check if IP already viewed this post today
    if ($action === 'view') {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE post_id = %d AND ip_address = %s AND action = 'view' AND DATE(timestamp) = CURDATE()",
            $post_id, $ip_address
        ));
        
        if ($existing == 0) {
            $wpdb->insert($table_name, array(
                'post_id' => $post_id,
                'ip_address' => $ip_address,
                'action' => $action
            ));
        }
    }
    
    // For likes, check if IP already liked this post
    if ($action === 'like') {
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE post_id = %d AND ip_address = %s AND action = 'like'",
            $post_id, $ip_address
        ));
        
        if ($existing == 0) {
            $wpdb->insert($table_name, array(
                'post_id' => $post_id,
                'ip_address' => $ip_address,
                'action' => $action
            ));
        }
    }
    
    return new WP_REST_Response(array('success' => true), 200);
}

function get_post_stats($request) {
    global $wpdb;
    
    $post_id = $request->get_param('id');
    $table_name = $wpdb->prefix . 'post_tracking';
    
    $views = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(DISTINCT ip_address) FROM $table_name WHERE post_id = %d AND action = 'view'",
        $post_id
    ));
    
    $likes = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE post_id = %d AND action = 'like'",
        $post_id
    ));
    
    return new WP_REST_Response(array(
        'views' => intval($views),
        'likes' => intval($likes)
    ), 200);
}