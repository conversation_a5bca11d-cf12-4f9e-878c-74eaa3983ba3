@import "@/public/styles/mixins/mixins";

.footer {
    padding: 80px 0 0 0;
    background-color: #2E1C18;
    @include max(767) {
        padding: 40px 0 0 0;
    }
    .top_footer {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        @include max(992) {
            flex-wrap: wrap;
            row-gap: 15px;
            align-items: center;
        }
        @include max(767) {
            justify-content: center;
        }
    }
    .footer_logo{
        @include max(992) {
            order: 1;
            width: 100%;
            text-align: center;
            display: flex;
            justify-content: center;
        }
    }
    .foot_social {
        display: flex;
        align-items: center;
        gap: 23px;
        width: 18%;
        justify-content: flex-end;
        li{
            margin-bottom: 0;
            a{
                display: block;
            }
        }
        @include max(992) {
            order: 3;
            width: 50%;
        }
        @include max(767) {
            width: 100%;
            justify-content: center;
        }
        a {
            &:hover {
                svg {
                    fill: var(--hover-color) !important;
                }

            }
        }
    }
    .footer_logo{
        @include max(767) {
            text-align: center;
        }
    }
    .left_content {
        width: 20%;
        @include max(1300) {
            width: 25%;
        }
        @include max(1200) {
            width: 30%;
        }
        @include max(992) {
            width: 50%;
            order: 2;
        }
        @include max(767) {
            width: 100%;
            text-align: center;
            order: 3;
            padding: 10px 0;
        }
        p,
        a {
            color: var(--primery_color);
            font-size: size(15px);
            display: block;
            font-weight: 400;
            @include max(1600) {
                font-size: size(14px);
            }
        }

        a {
            margin-top: 5px;

            &:hover {
                color: var(--hover-color);
            }
        }
    }

    .container {
        max-width: 1400px;
        @include max(1600) {
            max-width: 1250px;
        }
        
        @include max(1300) {
            max-width: 87%;
        }
    }

    .footer_main_links {
        border-top: 1px solid rgba(127, 73, 48, .6);
        border-bottom: 1px solid rgba(127, 73, 48, .6);
        margin-top: 25px;
        padding: 25px 0;

        ul {
            display: flex;
            justify-content: center;
            column-gap: 30px;
            @include max(992) {
                flex-wrap: wrap;
            }
            @include max(767) {
                gap: 5px 20px;
            }
            li {
                margin: 0;

                a {
                    font-size: size(15px);
                    color: #FFFFFF;
                    font-family: var(--font-obsolete);
                    &:hover {
                        color: var(--primery_color);
                    }
                    @include max(1600) {
                        font-size: size(13px);
                    }
                }
                &.active{
                    a{
                        color: var(--primery_color);
                    }
                }
            }
        }
    }

    .footer_logos {
        padding: 25px 0;
        border-bottom: 1px solid rgba(127, 73, 48, .6);

        ul {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 25px;
            @include max(767) {
                gap: 10px;
            }
            li {
                margin-bottom: 0;
            }
        }
    }

    .copyright {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
        @include max(767) {
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        h5 {
            font-size: size(14px);
            color: #fff;
            font-weight: 400;
            @include max(1600) {
                font-size: size(13px);  
            }
        }

        ul {
            display: flex;
            align-items: center;
            li {
                margin-bottom: 0;
                line-height:14px;
                a {
                    font-size: size(14px);  
                    color: #fff;
                    font-weight: 400;
                    &:hover{
                        color: var( --hover-color);
                        text-shadow: none;
                        -webkit-text-fill-color:initial;
                    }
                    @include max(1600) {
                        font-size: size(13px);  
                    }
                    @include max(1550) {
                        font-size: size(12.5px);  
                        line-height:size(18px);
                    }
                }

                // border-right: 1px solid #fff;
                margin-right: 25px;
                padding-right: 25px;
                position: relative;
                @include max(1600) {
                    margin-right: 20px;
                    padding-right: 20px;
                }

                &:last-child {
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    &::after{
                        display: none;
                    }
                }
                &::after{
                    content: "";
                    width: 1px;
                    height: 15px;
                    background-color: #fff;
                    display: block;
                    position: absolute;
                    right: 0;
                    top: 0;
                    @include max(1550) {
                        width: 1px;
                        height: 13px;
                        top: 2px;
                    }
                }
            }
        }
    }
}
:global(body.rtl){
    footer{
        .copyright{
            ul li{
                border-right:none;
                margin-right: 0;
                padding-right:0;
                border-left: 1px solid #fff;
                margin-left: 25px;
                padding-left: 25px;
            }
        }
    }
}