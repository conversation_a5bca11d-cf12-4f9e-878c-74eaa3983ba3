import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';

const TeamCard = ({ data, size = 'small' }) => {
    const { board_image, board_name, board_position } = data;
    const defaultImage = '/images/dummy_img.jpg'; 

    return (
        <div className={`${styles.team_card} ${size === 'big' ? styles.big : styles.small}`}>
            <div className={styles.team_img}>
                <figure>
                    <Image src={board_image?.url || defaultImage} fill alt="team member image" quality={100} />
                </figure>
                <div className={styles.teamDetails}>                    
                    {board_name && (
                        <h3>{board_name}</h3>
                    )}
                    {board_position && (
                        <p>{board_position}</p>
                    )}
                </div>
            </div>
        </div>
    );
}

export default TeamCard;
