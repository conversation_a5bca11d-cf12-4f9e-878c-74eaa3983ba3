@import "@/public/styles/mixins/mixins";
.breadcrumbs{
    margin-top: 35px;
    display: flex;
    align-items: center;
    gap: 15px;
    overflow: scroll;
    &::-webkit-scrollbar{
        display: none;
    }
    @include max(767) {
        gap: 10px;
    }

    li{
        position: relative;
        display: flex;
        align-items: center;
        margin: 0;
        span{
            margin-left: 15px;
            svg{
                @include max(767) {
                    width: 10px !important;
                    height: 10px !important;
                }
            }
        }
        &:last-child{
            span{
                display: none;
            }
        }
        a,p{
            font-size: size(16px);
            line-height: size(23px);
            color: #FCE1CB;
            font-weight: 400;
            white-space: nowrap;
            @include max(767) {
                font-size: size(14px);
                line-height: size(20px);
            }
        }
        a{
            svg{
                width: 20px !important;
                height: 20px !important;
            }
        }
    }
}