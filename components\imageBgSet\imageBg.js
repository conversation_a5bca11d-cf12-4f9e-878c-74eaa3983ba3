// components/ImageBg.js
import React, { useState, useEffect } from "react";
import styles from "./imageBg.module.scss";
import Image from "next/image";
import Icons from "@/public/Icons";
import Link from "next/link";
import AOS from "aos";


import { motion } from "framer-motion";
import parse from "html-react-parser";
import { parseDocument } from "htmlparser2";
import BottomCardSlider from "../BottomCardSlider";
const ImageBg = ({
  title,
  subtitle,
  description,
  imageSrc,
  imageAlt = "",
  buttonLink,
  buttonText,
  downloadLink,
  downloadText,
  email,
  showEmail = true,
  emailText = "Contact Us",
  showLogo = true,
  logoSrc,
  fullWidth = false,
  isBigText = false,
  showProductListButton = false,
  showProductsList = false,
  products = [],
}) => {
  const [isContentActive, setIsContentActive] = useState(false);
  const [showProducts, setShowProducts] = useState(
    showProductListButton || showProductsList
  );
  const [isMobile, setIsMobile] = useState(false);

  // Calculate if we need the show more logic Start
  const getParagraphCount = (html) => {
    const dom = parseDocument(html || "");
    let count = 0;
  
    const traverse = (nodes) => {
      for (const node of nodes) {
        if (node.name === "p") {
          count++;
        }
        if (node.children?.length) {
          traverse(node.children);
        }
      }
    };
  
    traverse(dom.children || []);
    return count;
  };
  const paragraphCount = description ? getParagraphCount(description) : 0;
  const needsShowMore = isMobile && subtitle && paragraphCount > 1;

  // Set initial showFullContent state based on needsShowMore
  const [showFullContent, setShowFullContent] = useState(!needsShowMore);
  
  // Update showFullContent if needsShowMore changes (e.g., on resize)
  useEffect(() => {
    setShowFullContent(!needsShowMore);
  }, [needsShowMore]);
  
// Calculate if we need the show more logic end

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleContentClick = () => {
    // Only toggle on mobile devices
    if (window.innerWidth <= 768) {
      setIsContentActive(!isContentActive);
    }
  };

  const handleViewProducts = (e) => {
    e.preventDefault();
    setShowProducts((prev) => !prev);
  };
  useEffect(() => {
    AOS.init({
        easing: "ease-out-cubic",
        once: false,
        offset: 50,
    });
}, []);

  return (
    <div
      className={`${styles.about_bgean} ${fullWidth ? styles.full_width : ""}${showProducts ? " " + styles.products_open : ""
        }`}
    >
      <div className={styles.imageContainer}>
        <Image
          src={imageSrc}
          alt={imageAlt}
          width={1540}
          height={753}
          quality={100}
          className={styles.image}
        />
      </div>

      {showLogo && logoSrc && (
        <div className={styles.logo_img_block}>
          <Image
            src={logoSrc}
            alt="logo"
            width={250}
            height={190}
            quality={100}
          />
        </div>
      )}

      <motion.div
        className={`${styles.content} ${isContentActive ? styles.content_active : ""
          }`}
        onClick={handleContentClick}
      >
        <div className={styles.title_sec} data-aos="fade-up" data-aos-duration="1000">
          {title &&
            <h2 className={`${styles.main_title} main_title`}>
              {title && parse(title)}
            </h2>
          }
            
          {subtitle && (
            <h3 className={styles.subtitle}>{parse(subtitle)}</h3>             
          )}
        </div>

        {/* Only show full content if not mobile, or if mobile and showFullContent is true */}
        {(!needsShowMore || showFullContent) && (
          <motion.div
            className={styles.desc}
            initial={isMobile ? { opacity: 0, y: 20 } : false}
            animate={isMobile ? { opacity: 1, y: 0 } : false}
            transition={
              isMobile ? { duration: 0.8, ease: "easeOut" } : undefined
            }
             data-aos="fade-up" data-aos-duration="1000"
          >
            {description && parse(description) }
             

            {showEmail && email && (
              <a
                href={`mailto:${email}`}
                className={`email_btn ${styles.email_btn}`}
              >
                {emailText}
              </a>
            )}

            {/* Show product list button if requested */}
            {showProductListButton && products.length > 0 && (
              <button
                type="button"
                className={`main_btn ${styles.show_products_btn}${showProducts ? " " + styles.active : ""
                  }`}
                onClick={handleViewProducts}
              >
                <span className="button_text">View Products</span>
                <span className="arrow">
                  <Icons size={25} color="#fff" icon="Right-arw" />
                </span>
              </button>
            )}

            {/* Show link button if buttonLink is provided and not a download */}
            {buttonLink && buttonText && (
              <Link href={buttonLink} className={`main_btn ${styles.link_btn}`}>
                <span className="button_text">{buttonText}</span>
                <span className="arrow">
                  <Icons size={25} color="#fff" icon="Right-arw" />
                </span>
              </Link>
            )}

            {/* Show download button if downloadLink is provided */}
            {downloadLink && downloadText && (
              <a
                href={downloadLink}
                className="main_btn download_btn"
                download
                target="_blank"
              >
                <span className="button_text">{downloadText}</span>
                <span className="arrow">
                  <Icons size={25} color="#fff" icon="Right-arw" />
                </span>
              </a>
            )} 

            {showProductsList && showProducts && products.length > 0 && (
              <BottomCardSlider products={products} />
            )}
          </motion.div>
        )}

        {/* Show the Show More button only if needed and not showing full content */}
        {needsShowMore && !showFullContent && (
          <button
            type="button"
            className={styles.show_more_btn}
            onClick={() => setShowFullContent(true)}
          >
            Show more
          </button>
        )}
      </motion.div>
    </div>
  );
};

export default ImageBg;
