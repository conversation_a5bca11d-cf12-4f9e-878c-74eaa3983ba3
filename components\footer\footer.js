import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import styles from './footer.module.scss';
import Icons from '@/public/Icons';
import { useRouter } from 'next/router';

const Footer = () => {
  const { asPath } = useRouter();
  return (
    <footer className={styles.footer}>
      <div className={`${styles.container} container` }>
        <div className={styles.top_footer}>
          <div className={styles.left_content}>
            <p>
              AlUla Peregrina Trading<br />
              Azizia District <br />
              Al Madinah Al Munawarah Street 7239 <br />
              43512 AlUla, Kingdom of Saudi Arabia <br />
              
            </p>
            <Link href='mailto:<EMAIL>' target='_blank'><EMAIL></Link>
          </div>
          <div className={styles.footer_logo}>
            <Image src='/images/footer_logo.png' width={95} height={124} alt='logo' quality={100} />
          </div>
          <ul className={styles.foot_social}>
              <li><Link href='https://www.facebook.com/share/ErmBgUdJCrH4wXPz/?mibextid=LQQJ4d' target="_blank"><Icons size={22} color="#FCE1CB" icon="Facebook" /></Link></li>
              <li><Link href='https://www.instagram.com/alula_peregrina?igsh=Nm5rcjJ6cHJ0ZWNs' target="_blank"><Icons size={22} color="#FCE1CB" icon="Instagram" /></Link></li>
              <li><Link href='https://x.com/alulaperegrina?s=21' target="_blank"><Icons size={18} color="#FCE1CB" icon="twitter" /></Link></li>
              <li><Link href='https://in.linkedin.com/' target="_blank"><Icons size={25} color="#FCE1CB" icon="linkedin2" /></Link></li>
           </ul>
        </div>
        <div className={styles.footer_main_links}>
            <ul>
              <li className={asPath === '/about' ? styles.active : ''}><Link href='/about'>About Us</Link></li>
              <li className={asPath === '/the-peregrina-tree' ? styles.active : ''}><Link href='/the-peregrina-tree'>The Peregrina Tree</Link></li>
              <li className={asPath === '/supply-chain' ? styles.active : ''}><Link href='/supply-chain'>Supply Chain</Link></li>
              <li className={asPath === '/our-actives' ? styles.active : ''}><Link href='/our-actives'>Our Actives</Link></li>
              <li className={asPath === '/hotels-and-spas' ? styles.active : ''}><Link href='/hotels-and-spas'>Hotels & Spas</Link></li>
              <li className={asPath === '/sustainability' ? styles.active : ''}><Link href='/sustainability'>Sustainability</Link></li>
              <li className={asPath === '/news' ? styles.active : ''}><Link href='/news'>News</Link></li>
              <li className={asPath === '/contact' ? styles.active : ''}><Link href='/contact'>Contact Us</Link></li>
           </ul>
        </div>
        <div className={styles.footer_logos}>
          <ul>
             <li><Image src='/images/ft_1.png' width={60} height={60} alt='image' quality={100} /></li>
             <li><Image src='/images/ft_2.png' width={49} height={60} alt='image' quality={100} /></li>
             {/* <li><Image src='/images/ft_3.png' width={29} height={60} alt='image' quality={100} /></li> */}
             <li><Image src='/images/ft_4.png' width={60} height={60} alt='image' quality={100} /></li>
             <li><Image src='/images/ft_5.png' width={60} height={38} alt='image' quality={100} /></li>
          </ul>
        </div>
        <div className={styles.copyright}>
             <h5>Copyrights © 2025 AlUla. All Rights Reserved.</h5>
             <ul>
                <li><Link href='/terms-conditions'>Terms & Conditions</Link></li>
                <li><Link href='/privacy-policy'> Privacy Policy</Link></li>
             </ul>
        </div>
      </div>
    </footer>
  )
}

export default Footer