import React, { useEffect, useState, useRef } from "react";
import Image from 'next/image';
import Link from 'next/link';
import styles from './footer.module.scss';
import Icons from '@/public/Icons';
import { useRouter } from "next/router";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";


const Footer = (props) => {
  const { asPath } = useRouter();
  const router = useRouter();
  const [footeroption, setOptions] = useState(null);

    useEffect(() => {
  
      const fetchMyAcfOptions = async (locale) => {
        try {
          const FooterPostsData = await getThemeoptions(locale);
          //  console.log("header options:", router.locale);
          setOptions(FooterPostsData);
        } catch (error) {
          console.error("Error fetching options:", error);
        }
      };
      fetchMyAcfOptions(router.locale);
  
    }, [router]);
  
    if (!footeroption) {
      return null;
  }
  
  return (
    <footer className={styles.footer}>
      <div className={`${styles.container} container` }>
        <div className={styles.top_footer}>
          {footeroption?.address && (
            <div className={styles.left_content}>
              {parse(footeroption?.address)}
            </div>
          )}
          {footeroption?.footer_logo &&
          footeroption?.footer_logo?.length > 0 &&  (
            <div className={styles.footer_logo}>
              <Image src={footeroption?.footer_logo?.url} width={95} height={124} alt='logo' quality={100} />
            </div>
          )}
          {footeroption?.social_media && 
            footeroption?.social_media.length > 0 && (
             <ul className={styles.foot_social}>            
                  {footeroption?.social_media.map((social_media, sindex) => (
                    <li key={sindex}>
                      <Link href={social_media?.social_link} target="_blank">
                        <Icons size={23} color="#FCE1CB" icon={social_media?.social_icon} />
                      </Link>
                    </li>
                  ))}             
            </ul>
          )}
        </div>
        {footeroption?.footer_menu &&
          footeroption?.footer_menu?.length > 0 && (
            <div className={styles.footer_main_links}>
                <ul>
                  {footeroption?.footer_menu.map((link, liindex) => (
                    <li key={liindex} className={asPath === link?.footer_menu?.url ? styles.active : ''}>
                      <Link href={link?.footer_menu?.url}>{link?.footer_menu?.title && parse(link?.footer_menu?.title)}</Link>
                    </li>
                  ))}
                </ul>
            </div>
          )}            
        {footeroption?.certificate && 
            footeroption?.certificate.length > 0 && (
              <div className={styles.footer_logos}>
                <ul>
                  {footeroption?.certificate.map((media, cindex) => (
                      <li key={cindex}>
                        <Image src={media?.url} width={media?.width} height={media?.height} alt='image' quality={100} />
                      </li>
                    ))}             
                </ul>
              </div>
          )}
        
        <div className={styles.copyright}>
          {footeroption?.copyright && <h5>{parse(footeroption?.copyright)}</h5>}
          {footeroption?.privacy_menu &&
            footeroption?.privacy_menu.length > 0 && (
              <ul>
                {footeroption?.privacy_menu.map((link, lindex) => (
                  <li key={lindex}>
                    <Link href={link?.privacy_sub_menu?.url}>{link?.privacy_sub_menu?.title && parse(link?.privacy_sub_menu?.title)}</Link>
                  </li>
                ))}
              </ul>
          )}             
        </div>
      </div>
    </footer>
  )
}

export default Footer