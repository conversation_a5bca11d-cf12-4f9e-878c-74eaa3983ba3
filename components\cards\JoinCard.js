import React, { useEffect, useRef } from 'react';
import Link from 'next/link';
import styles from './card.module.scss';
import Icons from '@/public/Icons';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import AnimatedTitle from '../anim';

gsap.registerPlugin(ScrollTrigger);

const JoinCard = ({ title, bgImag, description, buttonText, buttonLink }) => {
    const cardRef = useRef(null);
    const titleRef = useRef(null);
    const descRef = useRef(null);
    const btnRef = useRef(null);

    useEffect(() => {
        const tl = gsap.timeline({
            scrollTrigger: {
                trigger: cardRef.current,
                start: 'top 85%',
                end: 'bottom 20%',
                toggleActions: 'play reverse play reverse',
            }
        });

        // Title animation
        tl.fromTo(
            titleRef.current,
            { opacity: 0, y: 80 },
            { opacity: 1, y: 0, duration: 1.2, ease: 'power3.out' }
        );

        // Description animation (AFTER title, but with a small overlap)
        tl.fromTo(
            descRef.current.children,
            { opacity: 0, y: 30 },
            {
                opacity: 1,
                y: 0,
                duration: 2,
                ease: 'power2.out',
                stagger: 0.2,
            },
            '-=0.3' // Starts slightly before the title fully ends
        );

        // Button animation (Starts while paragraph is animating)
        tl.fromTo(
            btnRef.current,
            { opacity: 0, y: 50 },
            { opacity: 1, y: 0, duration: 1, ease: 'power3.out' },
            '-=1' // Starts even earlier
        );

    }, []);

    return (
        <div
            ref={cardRef}
            className={styles.join_card}
            style={{ backgroundImage: `url(${bgImag})` }}
        >
            <div className={styles.join_card_desc}>
                <AnimatedTitle
                    title={title}
                    tag="h2"
                    className="main_title"
                    ref={titleRef}
                />
                <div ref={descRef}>
                    {description.map((para, index) => (
                        <p key={index}>{para}</p>
                    ))}
                </div>
                <Link href={buttonLink} className="main_btn" ref={btnRef}>
                    <span className='button_text'>{buttonText}</span>
                    <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                </Link>
            </div>
        </div>
    );
};

export default JoinCard;
