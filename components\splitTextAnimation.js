import { useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import SplitType from "split-type";

gsap.registerPlugin(ScrollTrigger);

const SplitTextAnimation = (sectionRef, triggerClass = "trigger_title") => {
  useEffect(() => {
    if (typeof window === "undefined" || !sectionRef.current) return;

    const section = sectionRef.current; // Single element
    const heading = section.querySelector(`.${triggerClass}`);
    const paragraph = section.querySelector("p");

    if (!heading) return;

    const split = new SplitType(heading, { types: "lines, chars" });

    gsap.from(split.lines, {
      yPercent: 100,
      opacity: 0,
      duration: 1.5,
      ease: "power4.out",
      stagger: 0.2,
      scrollTrigger: {
        trigger: section,
        start: "top center",
        toggleActions: "play none none reverse",
      },
    });

    gsap.from(split.chars, {
      x: -30,
      opacity: 0,
      duration: 1,
      ease: "power4.out",
      stagger: 0.05,
      scrollTrigger: {
        trigger: section,
        start: "-100 center",
        toggleActions: "play none none reverse",
      },
    });

    if (paragraph) {
      gsap.from(paragraph, {
        y: 20,
        opacity: 0,
        duration: 1.2,
        ease: "power4.out",
        delay: 0.5,
        scrollTrigger: {
          trigger: section,
          start: "-90 center",
          toggleActions: "play none none reverse",
        },
      });
    }

    return () => ScrollTrigger.getAll().forEach(trigger => trigger.kill()); // Cleanup GSAP animations
  }, [sectionRef, triggerClass]);

  return null;
};

export default SplitTextAnimation;
