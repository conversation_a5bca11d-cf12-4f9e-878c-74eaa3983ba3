import React, { useEffect } from 'react';
import styles from './our_actives.module.scss';
import InnerBanner from '@/components/innerBanner/InnerBanner';
import BoxDesc from '@/components/boxDescription/BoxDesc';
import 'swiper/css';
import 'swiper/css/effect-cards';
import AOS from 'aos';
import 'aos/dist/aos.css';
import TextOverImg from '@/components/cards/TextOverImg';
import ImageBg from '@/components/imageBgSet/imageBg';
import Link from 'next/link';


import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getOurActives, getOurActivesPosts } from "@/utils/lib/server/publicServices"; 


const OurActives = (props) => {
  useEffect(() => {
    AOS.init({
      easing: 'ease-out-cubic',
      once: false,
      offset: 50,
    });
  }, []);

  const router = useRouter();
    const yoastData = props?.pageData?.yoast_head_json;
    if (!props?.pageData) {
          return null;
  }
  
  return (
    <>
      {yoastData && <Yoast meta={yoastData} />}
         {props &&
            props?.pageData &&
            props?.pageData?.acf && (
            props?.pageData?.acf?.banner_text ||
            props?.pageData?.acf?.sub_text ||
            props?.pageData?.acf?.banner_image) && (           
                  <InnerBanner
                      bannerBg={props?.pageData?.acf?.banner_image?.url}
                      title={props?.pageData?.acf?.banner_text && parse(props?.pageData?.acf?.banner_text)}
                      content={props?.pageData?.acf?.sub_text && parse(props?.pageData?.acf?.sub_text)}
                  />  
          )}
        {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.jfc_title || 
        props?.pageData?.acf?.jfc_content ||
        props?.pageData?.acf?.jfc_image ||
        props?.pageData?.acf?.jfc_button) && (
            <section className={`${styles.pb_120} ${styles.pt_120} ${styles.active_sec}`}>
              <div className="container">
                <BoxDesc
                  imageSrc={props?.pageData?.acf?.jfc_image?.url}
                  title={props?.pageData?.acf?.jfc_title}
                  position="right"
                  content={props?.pageData?.acf?.jfc_content}
                />
              </div>
            </section>
        )}
        
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.section_title ||
          props?.pageData?.acf?.our_actives_list?.length > 0 ) && (           
          <section className={`${styles.pb_120} ${styles.pt_120} bg_black`} >
            <div className="container">
              {props?.pageData?.acf?.section_title && (
                <h2 className="main_title">
                  {parse(props?.pageData?.acf?.section_title)}
                </h2>
            )}
            {props.ourActives.length > 0 && (
              <ul className={styles.two_cl_layout}>
                {props.ourActives.map((item, idx) => (
                  <li
                    data-aos="fade-up"
                    data-aos-duration="1000"
                    key={idx}
                    className={`${styles.mb_50} ${styles.clickable_list}`}
                  >
                    <Link href={`our-actives/${item.slug}`}>
                      <TextOverImg
                        image={item?.acf?.listing_image}
                        title={item?.title?.rendered}
                        description={item?.acf?.listing_sub_text}
                      />
                    </Link>
                  </li>
                ))}
              </ul>
              )}
            </div>
          </section>
        )}
      
      {props &&
        props?.pageData &&
        props?.pageData?.acf &&
        (props?.pageData?.acf?.enquire_now_title || 
        props?.pageData?.acf?.enquire_now_content ||
        props?.pageData?.acf?.enquire_now_image ||
        props?.pageData?.acf?.enquire_now_button) && (    
      <section className={`${styles.pb_120} ${styles.pt_120}`}>       
        <ImageBg
          title={props?.pageData?.acf?.enquire_now_title}           
          description={props?.pageData?.acf?.enquire_now_content}
          imageSrc={props?.pageData?.acf?.enquire_now_image?.url}
          imageAlt={props?.pageData?.acf?.enquire_now_image?.alt || 'image'}
          buttonLink={props?.pageData?.acf?.enquire_now_button?.url}
          buttonText={props?.pageData?.acf?.enquire_now_button?.title}
        />
      </section>
      )}

    </>
  );
};

export default OurActives;



export async function getStaticProps(locale) {
    const PageData = await getOurActives(locale.locale); 
    const ActivesPostsData = await getOurActivesPosts(locale.locale); 
  
  
  let OurActivesPosts = [];
  if (PageData && PageData?.acf && Array.isArray(PageData?.acf?.our_actives_list)) {
    OurActivesPosts = PageData?.acf?.our_actives_list;
  }

  // Format Investors Resources  for use in the component
  let OurActivesRelation = [];
  if (OurActivesPosts.length > 0) {
    OurActivesRelation = OurActivesPosts.map((id) =>
      ActivesPostsData?.find((post) => post.id === id)
    ).filter(Boolean); // To ensure undefined values (if any) are removed
  }

    return {
        props: {
          pageData: PageData || null,
          ourActives: OurActivesRelation || [],
        },
        revalidate: 10,
      };
}
