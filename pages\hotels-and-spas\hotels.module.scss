@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.hotel_sec{
    position: relative;
    .leaf{
        position: absolute;
        left: 20px;
        top: -245px;
        z-index: -1;
        @include max(1060) {
            top: -100px;
            width: 20%;
        }
        @include max(992) {
            top: -70px;
        }
        @include max(767) {
            width: 25%;
            top: -40px;
            left: 32px;
        }
    }
    .leaf_ht{
        position: absolute;
        left: 20px;
        top: -110px;
        z-index: -1;
        @include max(1600) {
            left: -15px;
            top: -150px;
        }
        @include max(1060) {
            top: -90px;
            width: 20%;
        }
    }
}
.hotels_sec{
    position: relative;
    .graphics{
        position: absolute;
        top: -410px;
        left: 80px;
    }
}
.fragnance_sec{
    margin: 0 30px;
    border-radius: 20px;
    position: relative;
    @include max(767) {
        margin: 0 15px;
        padding: 40px 0;
    }
    h2{
        margin-bottom: 20px;
        @include max(767) {
            font-size: size(22px);
            line-height: size(32px);
        }
        br{
            @include max(767) {
                display: none;
            } 
        }
    }
    p{
        margin-bottom: 50px;
        @include max(1060) {
            margin-bottom: 30px;
        }
        @include max(767) {
            font-size: size(22px);
            line-height: size(32px);
        }
    }
    .leaf{
        position: absolute;
        bottom: -240px;
        left: 0;
        right: 0;
        margin: 0 auto;
        max-width: 480px;
        @include max(1060) {
            max-width: 265px;
            bottom: -100px;
        } 
        @include max(767) {
            max-width: 170px;
            bottom: -50px;
        } 
    }
    .leaf_gst{
        position: absolute;
        top: -70px;
        right: -30px;
        @include max(1060) {
            width: 20%;
        }
        @include max(767) {
            top: -25px;
        }
    }
    
}
.pro_range{
    :global{
        .frangment_slider{
            overflow: initial;
        }
    }
}
.luxuary_sec{
    .luxuary_top{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 50px;
        @include max(992) {
            flex-wrap: wrap;
        }
        h2{
            @include max(1060) {
                width: 30%;
            }
            @include max(992) {
                width: 100%;
            }
        }
        .desc{
            max-width: 625px;
            @include max(992) {
                width: 100%;
            }
            p{
                color: #fff;
            }
        }
    }
}
.ing_sec{
    h2{
        margin-bottom: 40px;
    }
}
.eng_slider{
    :global{
        .swiper{
            overflow: visible;
        }
    }
}
.philosophy_sec{
    margin: 0 30px;
    @include max(767) {
        margin: 0 15px;
    }
}
.formulation_set{
    margin-top: 50px;
}
.pro_line_sec{
    :global{
        .swiper{
            overflow: initial;
        }
        .pro_line_slider{
            margin-top: 45px;
        }
    }
}
:global(body.rtl) {
    .hotel_sec{
        .leaf{
            left: auto;
            right: 20px;
        }
        .leaf_ht{
            left: auto;
            right: 20px;
        }
    }
    .fragnance_sec{
        .leaf_gst{
            right:auto;
            left:  -30px;
            transform: scaleX(-1) !important;
        }
    }

}
.sig_sec{
    h2{
        margin-bottom: 20px;
    }
    p{
        margin-bottom: 50px;
    }
}

.swipper_overflow{
    :global{
        .swiper{
            overflow: visible;
        }
    }
}

.right_bg_img{ position: absolute; right: 0; top:50px; z-index:1;}