import React, { useState, useEffect, useRef } from "react";
import styles from './contact.module.scss';
import Link from 'next/link';
import Icons from '@/public/Icons';
import ContactForm from '@/components/contactForm/contactForm';
import AnimatedTitle from '@/components/anim';
import AOS from "aos";
import "aos/dist/aos.css";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getContactus } from "@/utils/lib/server/publicServices"; 

const Contact = (props) => {
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

     const router = useRouter();
     const yoastData = props?.pageData?.yoast_head_json;
    if (!props?.pageData) {
        return null;
    }
    
    return (
        <>
            {yoastData && <Yoast meta={yoastData} />}
            
            <div className={`${styles.pb_120} ${styles.pt_180} ${styles.contact_sec}`}>
                <div className={styles.contact_sec_content}>
                        <div
                            className={styles.contact_address}
                            style={{
                                backgroundImage: `url('${props?.pageData?.acf?.background_image?.url || "/images/contact_pic.jpg"}')`
                            }}
                        >
                        <div className={styles.address_box}>
                            <div className={styles.address_top}>
                                {props?.pageData?.acf?.contact_information_normal_title && (
                                    <AnimatedTitle
                                        title={props?.pageData?.acf?.contact_information_normal_title}
                                        tag="h2"
                                        className="main_title"
                                    />
                                )}
                                {props?.pageData?.acf?.contact_information_email && (
                                    <ul data-aos="fade-up" data-aos-duration="1000">
                                        <li><Link href={`mailto:${props?.pageData?.acf?.contact_information_email}`}>{props?.pageData?.acf?.contact_information_email}</Link></li>
                                    </ul>
                                )}
                                
                            </div>
                            <div className={styles.address_bottom}>
                                {props?.pageData?.acf?.address_section && (
                                    <p data-aos="fade-up" data-aos-duration="1000">
                                        {parse(props?.pageData?.acf?.address_section)}
                                    </p>
                                )}
                               
                                <ul data-aos="fade-up" data-aos-duration="1000">
                                    {props?.pageData?.acf?.social_media_title && <li>{parse(props?.pageData?.acf?.social_media_title)}</li>}
                                    {props?.pageData?.acf?.social_media?.map((link, index) => (
                                            <li key={index}>
                                                <Link href={link?.social_link} target="_blank">
                                                    <Icons size={23} color="#7F4930" icon={link?.social_icon} />
                                                </Link>
                                            </li>
                                    ))}
                                        
                                    
                                </ul>
                            </div>
                        </div>
                        
                    </div>
                    {props &&
                        props?.pageData &&
                        props?.pageData?.acf && (
                            <div className={styles.form_sec}>
                                <ContactForm Formfeilds={props?.pageData?.acf} />
                            </div>
                        )}
                </div>
            </div>
            {props &&
                props?.pageData &&
                props?.pageData?.acf && (
                props?.pageData?.acf?.map_iframe_link) && (
                <div className={`${styles.map} ${styles.pb_120}`}>
                    <iframe
                        src={props?.pageData?.acf?.map_iframe_link}
                        width="100%"
                        height="600"
                        style={{ border: 0 }}
                        allowFullScreen
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                    ></iframe>
                </div>
            )}
           
        </>
    )
}

export default Contact


export async function getStaticProps(locale) {
    const PageData = await getContactus(locale.locale); 
    return {
        props: {
            pageData: PageData || null,
        },
        revalidate: 10,
      };
}