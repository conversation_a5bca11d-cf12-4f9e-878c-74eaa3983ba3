import React, { useState, useEffect, useRef } from "react";
import styles from "./contactForm.module.scss";
import Icons from '@/public/Icons';
import AOS from "aos";
import "aos/dist/aos.css";
import parse from "html-react-parser";
import IndustrialForm from "@/components/Form/IndustrialForm";
import FarmersRegistration from "@/components/Form/FarmersRegistration";

const ContactForm = ({Formfeilds}) => {
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);
    const [activeTab, setActiveTab] = useState("industrial");
    const [formData, setFormData] = useState({
        industrial: { name: "", company: "", email: "", phone: "", industryType: "", inquiryType: "", message: "" },
        farmers: { name: "", farmName: "", email: "", phone: "", cropType: "", landSize: "", message: "" },
    });
   

    // Add refs for tab buttons
    const tabRefs = useRef([]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [activeTab]: { ...prev[activeTab], [name]: value },
        }));
    };
   

    // Scroll active tab into view
    const handleTabClick = (tab, idx) => {
        setActiveTab(tab);
        if (window.innerWidth <= 767 && tabRefs.current[idx]) {
            tabRefs.current[idx].scrollIntoView({
                behavior: 'smooth',
                inline: 'center',
                block: 'nearest'
            });
        }
    };

    return (
        <div className={styles.formContainer}>
            {/* Tabs */}
            <div className={styles.tabs} data-aos="fade-up" data-aos-duration="1000">
                {Formfeilds?.industrial_commercial_title &&
                    <button
                        ref={el => tabRefs.current[0] = el}
                        className={activeTab === "industrial" ? styles.active : ""}
                        onClick={() => handleTabClick("industrial", 0)}
                    >
                        {Formfeilds?.industrial_commercial_title && parse(Formfeilds.industrial_commercial_title)}
                    </button>
                }
                {Formfeilds?.farmers_registration_title &&
                    <button
                        ref={el => tabRefs.current[1] = el}
                        className={activeTab === "farmers" ? styles.active : ""}
                        onClick={() => handleTabClick("farmers", 1)}
                    >
                        {Formfeilds?.farmers_registration_title && parse(Formfeilds.farmers_registration_title)}
                    </button>
                }
            </div>

            {/* Form */}          
            
                {activeTab === "industrial" ? (
                    <>
                    <IndustrialForm crycform={Formfeilds} />      
                    </>
                ) : (
                    <>
                        <FarmersRegistration crycform={Formfeilds} /> 
                        
                    </>
                )}
                
           
        </div>
    );
};

export default ContactForm;
