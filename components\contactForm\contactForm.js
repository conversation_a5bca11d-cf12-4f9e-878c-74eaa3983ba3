import React, { useState, useEffect, useRef } from "react";
import styles from "./contactForm.module.scss";
import Icons from '@/public/Icons';
import AOS from "aos";
import "aos/dist/aos.css";
import parse from "html-react-parser";

const ContactForm = ({Formfeilds}) => {
    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);
    const [activeTab, setActiveTab] = useState("industrial");
    const [formData, setFormData] = useState({
        industrial: { name: "", company: "", email: "", phone: "", industryType: "", inquiryType: "", message: "" },
        farmers: { name: "", farmName: "", email: "", phone: "", cropType: "", landSize: "", message: "" },
    });
    const [fileName, setFileName] = useState(""); // State for file name

    // Add refs for tab buttons
    const tabRefs = useRef([]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [activeTab]: { ...prev[activeTab], [name]: value },
        }));
    };
    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setFileName(file.name);
        } else {
            setFileName("");
        }
    };

    // Scroll active tab into view
    const handleTabClick = (tab, idx) => {
        setActiveTab(tab);
        if (window.innerWidth <= 767 && tabRefs.current[idx]) {
            tabRefs.current[idx].scrollIntoView({
                behavior: 'smooth',
                inline: 'center',
                block: 'nearest'
            });
        }
    };

    return (
        <div className={styles.formContainer}>
            {/* Tabs */}
            <div className={styles.tabs} data-aos="fade-up" data-aos-duration="1000">
                {Formfeilds?.industrial_commercial_title &&
                    <button
                        ref={el => tabRefs.current[0] = el}
                        className={activeTab === "industrial" ? styles.active : ""}
                        onClick={() => handleTabClick("industrial", 0)}
                    >
                        {Formfeilds?.industrial_commercial_title && parse(Formfeilds.industrial_commercial_title)}
                    </button>
                }
                {Formfeilds?.farmers_registration_title &&
                    <button
                        ref={el => tabRefs.current[1] = el}
                        className={activeTab === "farmers" ? styles.active : ""}
                        onClick={() => handleTabClick("farmers", 1)}
                    >
                        {Formfeilds?.farmers_registration_title && parse(Formfeilds.farmers_registration_title)}
                    </button>
                }
            </div>

            {/* Form */}
            
            <form className={styles.inquiryForm} data-aos="fade-up" data-aos-duration="1000">
                {activeTab === "industrial" ? (
                    <>
                        <ul className={`${styles.formGroup} ${styles.formGroup_1}`}>
                            <li><input type="text" name="name" required placeholder="Full Name*" value={formData.industrial.name} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                            <li><input type="text" name="company"  required placeholder="Company*" value={formData.industrial.company} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                            <li><input type="email" name="email"required placeholder="Email*" value={formData.industrial.email} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                            <li><input type="tel" name="phone" required placeholder="Phone*" value={formData.industrial.phone} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                            <li>
                                <select name="industryType" value={formData.industrial.industryType} onChange={handleChange}>
                                    <option value="all-project">All Project</option>
                                    <option value="all-locations">Project 1</option>
                                    <option>Type 2</option>
                                </select>
                                <span className={styles.inp_line}></span>
                            </li>
                        </ul>
                    </>
                ) : (
                    <>
                        <ul className={styles.formGroup}>
                            <li>
                                <input type="text" name="name" placeholder="Full Name*" required value={formData.farmers.name} onChange={handleChange} />
                                <span className={styles.inp_line}></span>
                            </li>
                            <li><input type="tel" name="phone" required placeholder="Phone*" value={formData.farmers.phone} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                            <li><input type="email" name="email" required placeholder="Email" value={formData.farmers.email} onChange={handleChange} /><span className={styles.inp_line}></span></li>
        
                            <li><input type="text" name="landSize" placeholder="Farm Location" value={formData.farmers.landSize} onChange={handleChange} /><span className={styles.inp_line}></span></li>
                        </ul>
                    </>
                )}
                <div className={styles.t_wrap} data-aos="fade-up" data-aos-duration="1000"> 
                    <textarea name="message" placeholder="Type your message..." value={formData[activeTab].message} onChange={handleChange}></textarea>
                    <span className={styles.inp_line}></span>
                </div>

                {/* File Upload */}
                <div className={styles.fileUpload} data-aos="fade-up" data-aos-duration="1000">
                    <label className={styles.uploadBtn}>
                        {fileName ? "Update File" : "Upload a File"}
                        <input type="file"  onChange={handleFileChange} />
                    </label>
                    <span>{fileName || "File upload (For documents, proposals, or order details)"}</span>
                </div>

                {/* Terms Checkbox */}
                <div className={styles.terms} data-aos="fade-up" data-aos-duration="1000">
                    <input type="checkbox" id="terms"  required/>
                    <label htmlFor="terms">I agree to the <a href="/terms-conditions">terms and conditions</a> and <a href="/privacy-policy">privacy policy</a>.</label>
                </div>

                <button className={styles.submitBtn} data-aos="fade-up" data-aos-duration="1000">
                    <span className={styles.btn_text}>Submit</span>
                    <span className={styles.arw}><Icons size={25} color="#fff" icon='Right-arw' /></span>
                </button>
            </form>
        </div>
    );
};

export default ContactForm;
