@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.timeline_sec{
    h2{
        margin-bottom: 30px;
    }
}
.story_top{
    position: relative;
    .leaf{
        position: absolute;
        right: 0;
        top: -145px;
        @include max(1600) {
            width: 20%;
            top: -145px;
        }
        @include max(992) {
            right: -45px;
            top: -110px;
            width: 30%;
        }
    }
}
.story_bottom{
    position: relative;
    .abt_leaf{
        position: absolute;
        left: -10%;
        bottom: -47%;
        z-index: -1;
    }
}
:global(body.rtl){
    .story_top{
        .leaf{
            right:auto;
            left: 0;
        }
    }
}
// .purpose_sec{
//     .container{
//         position: relative;
//     }
//     video{
//         height: 880px;
//         width: 100%;
//         object-fit: cover;
//         display: block;
//         @include max(1060) {
//             height: 500px;  
//         }
//         @include max(767) {
//             height: 400px;  
//         }
//     }
//     h2{
//         position: absolute;
//         bottom: 64px;
//         left: 0;
//         text-transform: capitalize;
//     }
// }
.our_values_list{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 50px;
    @include max(1300) {
        gap: 40px;
    }
    li{
        margin-bottom: 0;

        @include max(700) {
            width: 100%;
        }
    }

    @include max(700) {
        grid-template-columns: repeat(1,1fr); gap: 25px;
    }
}
.our_values_sec{
    h2{
        text-align: center;
        margin-bottom: 45px;
        @include max(700) {
            margin-bottom: 25px;
        }
    }
}
.founder_team{
    display: grid;
    grid-template-columns: repeat(3,1fr);
    gap: 30px;
    @include max(992) {
        gap: 20px;
        grid-template-columns: repeat(2,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
    }
}
.founder_team_sec{
    h2{
        text-align: center;
        margin-bottom: 45px;
        @include max(1300) {
            margin-bottom: 35px;
        }
        @include max(767) {
            margin-bottom: 25px;
        }
    }
}
.logo_list{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    gap: 30px;
    @include max(1550) {
        gap: 25px;
    }
    @include max(1060) {
        gap: 15px;
    }
    @include max(992) {
        grid-template-columns: repeat(3,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(2,1fr);
    }
    li{
        background-color: #F0EFEB;
        border-radius: 16px;
        height: 275px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        @include max(1550) {
            height: 245px;
        }
        @include max(1060) {
            height: 200px;
        }
        @include max(1060) {
            height: 180px;
        }
        @include max(767) {
            height: 140px;
            gap: 10px;
            border-radius: 13px;
        }

    }
    .logo_img{
        height: fit-content;
        width: fit-content;
       
        img{
            height: fit-content;
            width: fit-content;
            max-width: 223px;
            max-height: 219px;
            @include max(1550) {
                max-width: 190px;
                max-height: 170px;
            }
            @include max(1060) {
                max-width: 150px;
                max-height: 140px;
            }
            @include max(767) {
                max-width: 110px;
                max-height: 95px;
            }
        }
    }   
}
.partners_sec{
    h2{
        margin-bottom: 45px;
    }
}




 