import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  const { postId } = req.query;
  const clientIP = req.headers['x-forwarded-for']?.split(',')[0] || 
                  req.headers['x-real-ip'] || 
                  req.connection.remoteAddress || 
                  '127.0.0.1';
  
  try {
    const dataPath = path.join(process.cwd(), 'data', 'tracking.json');
    
    if (!fs.existsSync(dataPath)) {
      return res.status(200).json({ views: 0, likes: 0, userHasLiked: false });
    }
    
    const trackingData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    const postData = trackingData[postId] || { views: [], likes: [] };
    
    // Check if current IP has liked this post
    const userHasLiked = postData.likes.some(like => like.ip === clientIP);
    
    res.status(200).json({
      views: postData.views.length,
      likes: postData.likes.length,
      userHasLiked: userHasLiked
    });
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({ views: 0, likes: 0, userHasLiked: false });
  }
}

