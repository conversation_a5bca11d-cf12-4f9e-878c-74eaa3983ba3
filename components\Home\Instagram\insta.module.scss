@import "@/public/styles/mixins/mixins";
.instaSection{
    position: relative;
    h2{
        color: #7F4930;
        font-size: size(48px);
        text-align: center;
        font-family: var(--font-obsolete);
        margin-bottom: 30px;
        a{
            color: #7F4930;
        }
        @include max(1300) {
            font-size: size(40px);
        }
        @include max(1060) {
            font-size: size(38px);
        }
        @include max(992) {
            font-size: size(35px);
        }
        @include max(767) {
            font-size: size(27px);
        }
    }
    a{
        display: block;
    }
    .leaf{
        position: absolute;
        top: -100%;
        left: 0;
        @include max(767) {
            top: -50px;
            width: 20%;
        }
    }
}
@keyframes slideFromTop {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideFromBottom {
    0% {
        transform: translateY(100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.slideTop {
    animation: slideFromTop 1s ease-out forwards;
}

.slideBottom {
    animation: slideFromBottom 1s ease-out forwards;
}

:global(body.rtl) {
    .instaSection{
        .leaf{
            left: auto;
            right: 0;
            transform: scaleX(-1);
        }
    }
}