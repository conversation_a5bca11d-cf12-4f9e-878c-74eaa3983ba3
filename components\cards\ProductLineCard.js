import React from 'react';
import styles from './card.module.scss';
import Image from 'next/image';

const ProductLineCard = ({ data }) => {
  const { title, desc, image, prefix } = data;


  return (
    <div className={styles.productline_card}>
      <div className={styles.product_pic}>
        <Image src={image} width={411} height={467} alt='image' quality={100} />
      </div>
      <div className={styles.pro_desc}>
        <h3>{title}</h3>
        <div className={styles.hover_content}>
          <p>
            <span>{prefix} </span> {desc}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProductLineCard;
