@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.active_sec {
    position: relative;

    .leaf {
        position: absolute;
        left: 0;
        top: -60px;
    }
}

.slider_sec {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    h2 {
        width: 40%;
        @include max(1060) {
            width: 100%;
            margin-bottom: 30px;
        }
    }

    .pro_slide_sec {
        width: 60%;
        @include max(1060) {
            width: 100%;
        }
    }
}

.product_slider {
    max-width: 455px;
}

.product_slider {
    :global {
    }
    .product_slide {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3); /* Add depth */
    }
    
}


.lipophilic_sec{
    .container{
        position: relative;
    }
    .leaf{
        position: absolute;
        top: -120px;
        right: 0;
        left: 0px;
        margin: 0 auto;
        width: 301px;
        z-index: -1;
        @include max(992) {
            top: 115px;
            width: 240px;
        }
        @include max(767) {
            top: -105px;
            right: auto;
            left: -35px;
            width: 150px;
        }
    }
}
.consmetic_card_sec{
    h2{
        margin-bottom: 45px;
        @include max(992) {
            margin-bottom: 25px;
        }
    }
    :global {
        .cosmetic_slider{
            overflow: initial;
        }
    }
}
.botanical_list{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    gap: 30px;
    @include max(1060) {
        grid-template-columns: repeat(3,1fr);
        gap: 20px;
    }
    @include max(767) {
        grid-template-columns: repeat(2,1fr);
        gap: 15px;
    }
}
.research_sec{
    margin: 0 30px;
    background-color: rgb(0, 0, 0,.3);
    border-radius: 20px;
    @include max(767) {
        margin: 0 15px;
    }
    h5{
        color: #fff;
        font-weight: 600;
        font-size: size(28px);
        line-height: size(35px);
        @include max(1060) {
            font-size: size(23px);
            line-height: size(33px);
        }
        @include max(767) {
            font-size: size(18px);
            line-height: size(22px);
        }
    }
}
.stydy_list{
    display: grid;
    grid-template-columns: repeat(3,1fr);
    gap: 34px;
    margin-top: 45px;
    @include max(1060) {
        gap: 20px;
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
        gap: 15px;
        margin-top: 30px;
    }
    .study_set{
        background-color: #34241D;
        border-radius: 16px;
        overflow: hidden;
    }
}
.graph_list{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    gap: 25px;
    @include max(1060) {
        gap: 15px;
    }
    @include max(992) {
        grid-template-columns: repeat(2,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
    }
}


.two_cl_layout{
    margin: 0; padding: 0; display: flex; flex-wrap: wrap; justify-content: space-between;
    li{ list-style: none; width: 47.9%;cursor: pointer;
        @include max(1550) {
            width: 48.1%;
            margin-bottom: 40px;
        }
        @include max(767) {
            width: 100%;
        }
    }
}







// viring---------------

.virgin_sec{
    position: relative;
    h2{
        margin-bottom: 0;
    }
    .leaf{
        position: absolute;
        bottom: -180px;
        left: 0;
        @include max(1060) {
            width: 20%;
        }
    }
    .leaf_2{
        position: absolute;
        right: 0;
        top: 0;
        @include max(1060) {
            width: 20%;
        }
    }
    .leaf_3{
        position: absolute;
        left: 0;
        top: -120px;
        @include max(1060) {
            width: 20%;
            top: -40px;

        }
    }
}
.botanical_list{
    
    h5{
        color: #FCE1CB;
        font-size: size(28px);
        font-weight: 500;
        margin: 40px 0;
        @include max(992) {
            font-size: size(25px);
        }
        @include max(767) {
            font-size: size(20px);
            line-height: size(25px);
            margin: 30px 0 25px 0;
        }
    }
    .bt_list{
        display: grid;
        grid-template-columns: repeat(4,1fr);
        gap: 33px;
        @include max(1060) {
            gap: 20px;
        }
        @include max(992) {
            grid-template-columns: repeat(2,1fr);
        }
    }
}
.research_sec{
    margin: 0 30px;
    background-color: rgb(0, 0, 0,.3);
    border-radius: 20px;
    @include max(767) {
        margin: 0 15px;
        padding: 40px 0;
    }
    h5{
        font-size: size(28px);
        line-height: size(38px);
        font-weight: 600;
        color: #fff;
        @include max(1060) {
            font-size: size(22px);
            line-height: size(32px);
            margin-top: 10px;
        }
        @include max(767) {
            font-size: size(18px);
            line-height: size(25px);
        }
    }
}
.research_list{
    margin-top: 45px;
    display: grid;
    grid-template-columns: repeat(3,1fr);
    gap: 34px;
    @include max(1060) {
        gap: 25px;
    }
    @include max(992) {
        grid-template-columns: repeat(2,1fr);
    }
    @include max(767) {
        grid-template-columns: repeat(1,1fr);
    }
}
:global(body.rtl) {
    .virgin_sec{
        .leaf{
            left:auto;
            right: 0; 
            transform: scaleX(-1);
        }
        .leaf_2{
            right:auto;
            left: 0;
            transform: scaleX(-1);
        }
        .leaf_3{
            left: auto;
            right: 0;
            transform: scaleX(-1);
        }
    }
}






