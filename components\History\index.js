'use client';
import React, { useEffect, useRef } from 'react';
import styles from './historyComponent.module.scss';
import Image from 'next/image';
import BotanicalCard from '@/components/cards/BotanicalCard';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import AOS from 'aos';
import 'aos/dist/aos.css';
import parse from "html-react-parser";

gsap.registerPlugin(ScrollTrigger);

const PeregrinaIntroSection = ({
  title = 'A rare natural asset with hidden gemssssss',
  paragraphs = [],
  botanicalData = [],
  mainImage = {
    src: '/images/history_pic.jpg',
    width: 626,
    height: 860,
    alt: 'image',
  },
 
}) => {
  const IntitleRef = useRef(null);
  const paragraphsRef = useRef([]);

  const imgRef = useRef(null);

  useEffect(() => {
    AOS.init({
      easing: 'ease-out-cubic',
      once: false,
      offset: 50,
    });

    gsap.from(IntitleRef.current, {
      y: 50,
      opacity: 0,
      duration: 1,
      ease: 'power3.out',
      scrollTrigger: {
        trigger: IntitleRef.current,
        start: 'top 80%',
        toggleActions: 'play reverse play reverse',
      },
    });

    paragraphsRef.current.forEach((el, i) => {
      gsap.from(el, {
        y: 30,
        opacity: 0,
        duration: 0.8,
        delay: i * 0.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: el,
          start: 'top 85%',
          toggleActions: 'play reverse play reverse',
        },
      });
    });

    gsap.fromTo(
      imgRef.current,
      { opacity: 0, scale: 0.9 },
      {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: imgRef.current,
          start: 'top 80%',
          toggleActions: 'play reverse play reverse',
          scrub: false,
          once: false,
        },
      }
    );
  }, []);

  return (
    <div className={`${styles.pt_120} ${styles.pb_120} ${styles.history_top}`}>
      <div className={`container ${styles.container}`}>
        <div className={styles.left_sec}>
          <div className={styles.left_desc}>
            {title && (
            <h2 ref={IntitleRef} className="main_title">
              {parse(title)}
              </h2>
            )}
            {paragraphs && (
              <div
                ref={(el) => (paragraphsRef.current[0] = el)}
              >
                {parse(paragraphs)}
              </div>
            )}
          </div>

          <div className={styles.box_list}>
            {botanicalData && botanicalData.map((item, index) => (
              <div className={styles.box} key={index} data-aos="fade-up" data-aos-duration="1000">
                <BotanicalCard
                  icon={item.icon}
                  title={item.title}
                  subTitle={item.subTitle}
                  parentClass={index === 2 ? 'no_pad' : ''}
                />
              </div>
            ))}
          </div>
        </div>
        {mainImage && (
        <div className={styles.right_sec}>
          <Image
            src={mainImage.src}
            width={mainImage.width}
            height={mainImage.height}
            alt={mainImage.alt}
            quality={100}
            ref={imgRef}
          />
        </div>
        )}
      </div>

      {/* <div className={styles.leaf}>
        <Image
          src={leafImage.src}
          width={leafImage.width}
          height={leafImage.height}
          alt={leafImage.alt}
          quality={100}
        />
      </div> */}
    </div>
  );
};

export default PeregrinaIntroSection;
