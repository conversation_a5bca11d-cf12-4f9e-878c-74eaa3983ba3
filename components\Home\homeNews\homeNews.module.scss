@import "@/public/styles/mixins/mixins";
.home_news{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .news_main{
        width: 51%;
        transition: all .3s ease-in-out;
        @include max(1600) {
            width: 49%;
        }
        @include max(992) {
            width: 100%;
        }
        p{
            color: #fff;
            font-size: size(18px);
            line-height: size(26px);
            margin:20px 40px 0 40px;
            @include max(1060) {
                line-height: size(20px);
                margin: 10px 30px 0 20px;
            }
            @include max(767) {
                font-size: size(15px);
                line-height: size(25px);
                margin:10px 5px 0 15px;
            }
        }
        &:hover{
            img{
                transform: scale(1.1);
            }
        }
        .image{
            overflow: hidden;
            figure{
                // height:420px ;
                @include max(1600) {
                    height: 400px;
                }
                @include max(767) {
                    height: 300px;
                }
            }
        }
    }
    img{
        display: block;
        width: 100%;
        height: 100%;
        transition: all .3s ease-in-out;
        object-fit: cover;

        @include max(992) {
            height:100%;
        }
    }
}
.news_main_img{
    position: relative;
    figure{
        // @include aspectRatio(675,477);
        position: relative;
        &::after{
            content: "";
            width: 100%;
            height: 100%;
            background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0) 50%,
            rgba(0, 0, 0, 0.7) 100%);
            position: absolute;
            left: 0;
            top: 0;
            
        }
    }
    
    .news_main_img{
        img{
            width: 100%;
            height: 477px;
            object-fit: cover;
        }
    }
}
.news_main_texts{
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding:30px 40px 40px 40px;
    z-index: 1;
    pointer-events: none;
    @include max(1060) {
        padding: 20px 25px 25px 25px;
    }
    @include max(767) {
        padding: 15px;
    }
    h5{
        font-size: size(32px);
        line-height: size(42px);
        color: #fff;
        font-weight: 600;
        @include max(1600) {
            font-size: size(28px);
            line-height: size(38px);
        }
        @include max(1300) {
            font-size: size(26px);
            line-height: size(36px);
        }
        @include max(1060) {
            font-size: size(25px);
            line-height: size(30px);
        }
        @include max(767) {
            font-size: size(20px);
            line-height: size(25px);
        }
    }
    .date{
        font-size: size(13px);
        line-height: size(19px);
        display: flex;
        align-items: center;
        color: #fff;
        @include max(767) {
            font-size: size(12px);
            line-height: size(15px);
        }
        span{
            font-size: size(40px);
            line-height: size(50px);
            font-weight: bold;
            margin-right: 10px;
            color: #fff;
            @include max(1600) {
                font-size: size(35px);
            }
            @include max(767) {
                font-size: size(30px);
            }
        }
    }
    
}
.news_list{
    width: 46%;
    @include max(1600) {
        width: 47.5%;
    }
    @include max(992) {
        width: 100%;
        margin-top: 50px;
    }
    img{
        @include max(1550) {
            height: 165px;
        }
    }
}
.hm_news_sec{
    h2{
        margin-bottom: 30px;
    }

    .btn_sec{
        text-align: center;
        margin-top:35px;
    }
}
:global(body.rtl) {
    .news_main_texts{
        .date{
            span{
                margin-right: 0;
                margin-left: 10px;
            }
        }
    }
}