import { useState, useEffect } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";
import gsap from "gsap";
import "swiper/css";
import "swiper/css/navigation";
import styles from "./timeline.module.scss";
import AnimatedTitle from "../anim";
import parse from "html-react-parser";

const TimelineSlider = ({ timelineData, title }) => {
  const [activeSlide, setActiveSlide] = useState(0);
  const [swiperInstance, setSwiperInstance] = useState(null);
  const [slidesPerView, setSlidesPerView] = useState("auto");

  useEffect(() => {
    if (!timelineData[activeSlide]) return; // Prevent undefined errors

    const tl = gsap.timeline();

    tl.fromTo(
      `.${styles.content}`,
      { opacity: 0, y: 20 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power3.out" }
    ).fromTo(
      `.${styles.images}`,
      { opacity: 0, x: 80 },
      { opacity: 1, x: 0, duration: 1, ease: "power3.out" },
      "-=0.5"
    );
  }, [activeSlide]);

  useEffect(() => {
    setTimeout(() => {
      const swiper = document.querySelector(
        `.${styles.timelineSwiper}`
      )?.swiper;
      if (swiper?.navigation) {
        swiper.navigation.init();
        swiper.navigation.update();
      }
    }, 500);
  }, []);

  useEffect(() => {
    const updateSlidesPerView = () => {
      if (window.innerWidth > 1025) {
        setSlidesPerView("auto");
      } else if (window.innerWidth >= 768) {
        setSlidesPerView(4);
      } else {
        setSlidesPerView(2); // 🔹 Ensure only one item is visible on mobile
      }
    };

    updateSlidesPerView();
    window.addEventListener("resize", updateSlidesPerView);
    return () => window.removeEventListener("resize", updateSlidesPerView);
  }, []);

  const handleNext = () => {
    if (swiperInstance) {
      let nextIndex = activeSlide + 1;
      if (nextIndex < timelineData.length) {
        swiperInstance.slideTo(nextIndex);
        setActiveSlide(nextIndex);
      }
    }
  };

  const handlePrev = () => {
    if (swiperInstance) {
      let prevIndex = activeSlide - 1;
      if (prevIndex >= 0) {
        swiperInstance.slideTo(prevIndex);
        setActiveSlide(prevIndex);
      }
    }
  };

  const currentImages = timelineData[activeSlide]?.images || [];
  const parentClass =
    currentImages.length === 1 ? styles.singleImage : styles.twoImages;

  return (
    <div className={styles.timelineContainer}>
      <AnimatedTitle
        title={title}
        tag="h2"
        className={`main_title ${styles.main_title}`}
      />
      <div className={styles.timeline_set}>
        <div className={styles.timelineTop}>
          <div className={styles.content}>
            {timelineData[activeSlide].jr_title && (
              <h3>{parse(timelineData[activeSlide].jr_title)}</h3>
            )}
            <div className={styles.paragraph}>
             {timelineData[activeSlide].jr_description &&
                parse(timelineData[activeSlide].jr_description)}
            </div>
          </div>
          <div className={`${styles.images} ${styles.singleImage}`}>
            {timelineData[activeSlide].js_image && (
              <div className={styles.singleWrapper}>
                <div className={styles.imageBox}>
                  <Image
                    src={timelineData[activeSlide].js_image?.url}
                    width={550}
                    height={394}
                    alt="Event"
                  />
                  {/* {currentImages[0].caption && <p>{currentImages[0].caption}</p>} */}
                </div>
            </div>
            )}          
          </div>
        </div>
        <div className="container">
          <div className={styles.timelineBottom}>
            <div className={styles.custom_nav}>
              <div className="swiper_button_prev" onClick={handlePrev}>
                Prev
              </div>
              <div className="swiper_button_next" onClick={handleNext}>
                Next
              </div>
            </div>
            <Swiper
              slidesPerView={slidesPerView}
              spaceBetween={0} // Ensures items are tightly packed
              navigation={{
                nextEl: ".swiper_button_next",
                prevEl: ".swiper_button_prev",
              }}
              modules={[Navigation, Autoplay]}
              autoplay={{
                delay: 4000,
                disableOnInteraction: true,
              }}
              speed={1200}
              onSwiper={(swiper) => {
                setSwiperInstance(swiper);
                setTimeout(() => {
                  if (swiper?.navigation) {
                    swiper.navigation.init();
                    swiper.navigation.update();
                  }
                }, 100);
              }}
              onSlideChange={(swiper) => {
                setActiveSlide(swiper.realIndex); // Ensure correct slide is active
              }}
              centeredSlides={false} // Prevent Swiper from jumping past slides
              loop={false} // Ensures linear progression
              watchOverflow={true} // Prevents Swiper from skipping slides
              className={styles.timelineSwiper}
              onMouseEnter={() => swiperInstance?.autoplay?.stop()}
              onMouseLeave={() => swiperInstance?.autoplay?.start()}
            >
              {timelineData &&
                timelineData.map((item, index) => (
                <SwiperSlide
                  key={index}
                  className={activeSlide === index ? "activeYear" : ""}
                >
                  <div
                    onClick={() => setActiveSlide(index)}
                    className={`${styles.year_item} year_item`}
                  >                    
                    <span>{item.jr_year}</span>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineSlider;
