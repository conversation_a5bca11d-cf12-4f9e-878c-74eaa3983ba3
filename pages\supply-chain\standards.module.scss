@import "@/public/styles/variable";
@import "@/public/styles/base";
@import "@/public/styles/mixins/mixins";

.sustainable_sec {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    min-height: 755px;
    background-attachment: fixed;
    max-width: calc(100% - 60px);
    margin: 0 auto;
    border-radius: 20px;
    position: relative;
    padding: 55px 115px;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    overflow: hidden;
    @include max(1060) {
        padding: 40px 50px;
    }
    @include max(992) {
        padding: 40px 30px;
        justify-content: flex-start;
        min-height: 550px;
        border-radius: 12px;
    }
    @include max(767) {
        padding: 15px;
        min-height: 470px;
    }
    &::after {
        content: "";
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg,
                rgba(0, 0, 0, 0) 0%,
                rgba(0, 0, 0, 1) 100%);
        position: absolute;
        left: 0;
        top: 0;
    }

    .sustainable_contents {
        position: relative;
        z-index: 1;
        max-width: 620px;
    }

    p {
        color: #fff;
        margin-bottom: 20px;
        line-height: size(30px);
        @include max(1060) {
            line-height: size(25px);
        }
        @include max(767) {
            font-size: .8rem;
            line-height: size(20px);
        }
        span{
            font-size: size(18px);
            font-weight: 600;
            @include max(1060) {
                font-size: size(16px);
            }
            @include max(767) {
                font-size: size(15px);
            }
        }
    }
    
}

.slider_content {
    border-radius: 20px;
    overflow: hidden;
    transition: all 2s ease-in-out;
    height: 425px;
    @include max(1600) {
        height: 400px;
    }
    @include max(767) {
        height: auto;
    }
    h3 {
        position: absolute;
        top: 40px;
        left: 40px;
        color: #fff;
        font-size: size(26px);
        line-height: size(36px);
        font-weight: 300;
        max-width: 245px;
        z-index: 9;
        transition: all 1s ease-in-out;
        @include max(1600) {
            font-size: size(23px);
            line-height: size(32px);
        }
        @include max(767) {
            font-size: size(22px);
            line-height: size(32px);
            margin-bottom: 15px;
            position: initial;
        }
    }

    p {
        color: #fff;
        line-height: size(26px);
    }

    .slide_cnt {
        padding: 125px 40px 40px 40px;
        background-color: rgba(0, 0, 0, .3);
        height: 100%;
        display: flex;
        align-items: center;
        position: absolute;
        left: 0;
        top: 0;
        width: 350px;
        transition: all 2s ease-in-out;
        @include max(1600) {
            padding: 100px 40px 40px 40px;
        }
        @include max(767) {
            position: initial;
            width: 100%;
            padding:30px 25px;
            flex-wrap: wrap;
        }
       
    }

    .slide_img {
        height: 100%;
        position: relative;
        z-index: 1;
        transition: all 2s ease-in-out;
        @include max(767) {
            height: 300px;
        }
        &:after{
            content: "";
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    img {
        display: block;
        height: 100%;
        width: 100%;
        object-fit: cover;
    }
}
.slider_set{
    margin-left: calc((100% - 1400px) / 2) !important;
    position: relative;
    @include max(1600) {
        margin-left: calc((100% - 1200px) / 2) !important;
    }
    @include max(1250) {
        margin-left: 60px !important;
    }
    @include max(767) {
        margin:0 20px !important;
    }
}
.for_yoy_sec{
    h2{
        margin-bottom: 40px;
        @include max(767) {
            margin-bottom: 25px;
        }
    }
}
.forSlider {
    padding-bottom: 100px !important;
    @include max(767) {
        padding-bottom: 60px !important;
    }
    :global {
        .swiper-slide {
            width: 350px;
            transition: all 1s ease-in-out;
            overflow: hidden;
            border-radius: 20px;
            overflow: hidden;
            @include max(767) {
                border-radius: 0px;
                border-top-left-radius: 15px;
                border-top-left-radius: 15px;
            }
        }

        .swiper-slide-active {
            width: 700px;
            transition: all 2s ease-in-out;
            .slider_content {
                display: grid;
                position: relative;
                align-items: center;
                grid-template-columns: repeat(2,1fr);
                @include max(767) {
                    grid-template-columns: repeat(1,1fr);
                    height: auto;
                }
                
            }
            .slide_cnt{
                position: initial;
            }
        }
        .swiper-pagination{
            bottom: 0;
        }
        .swiper-pagination-fraction {
            color:#FCE1CB ;
            left: auto;
            right: auto;
            padding-right: calc((100% - 1135px) / 2);
            text-align: right;
            @include max(1250) {
                padding-right:30px;
            }
            @include max(767) {
                padding-right: 10px;
            }
        }
    }
}
.custom_navigation{
    display: flex;
    align-items: center;
    gap: 20px;
    justify-content: flex-end;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: -13px;
    z-index: 8;
    width: fit-content;
    margin-right: calc((100% - 1020px) / 2) ;
    @include max(1060) {
        margin-right: 80px;
    }
    @include max(992) {
        margin-right: 65px;
    }
    @include max(767) {
        gap: 10px;
    }
    .slider_btn{
        width: 47px;
        height: 47px;
        background-color: rgba(0, 0, 0,.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all .3s ease-in-out;
        svg{
            @include max(1060) {
                width: 20px !important;
                height: 20px !important;
            }
        }
        &:hover{
            background-color: #FCE1CB;
        }
    }
    .prevButton{
        svg{
            transform: rotate(180deg);
        }
    }
}
:global(body.rtl) {
    .slider_set{
        margin-left: 0 !important;
        margin-right: calc((100% - 1400px) / 2) !important;
    }
    .custom_navigation {
        margin-right:0;
        margin-left: calc((100% - 1020px) / 2);
        right: auto;
        left: 0;
        .prevButton{
            order: 2;
        }   
    }
    .forSlider {
        :global {
            .swiper-pagination-fraction {
                padding-right: 0;
                padding-left: calc((100% - 1135px) / 2);
                text-align: left;
            }
        }
    }
}
.top_title_sec{
    .container{
        display: flex;
        align-items: center;
        column-gap: 45px;
        @include max(767) {
            flex-wrap: wrap;
        }
    }
    .top_right{
        @include max(767) {
            padding-top: 30px;
        }
        h6{
            font-weight: 600;
            @include max(1060) {
                font-size: 1.2rem;
            }  
            @include max(767) {
                font-size: 1rem;
                line-height: 1.2rem;
            } 
        }
        ul{
            margin-top: 30px;
            @include max(767) {
                margin-top: 15px;
            }
            li{
                font-size: size(18px);
                color: #fff;
                font-weight: 300;
                margin-bottom:10px;
                &:last-child{
                    margin-bottom: 0;
                }
                @include max(1060) {
                    font-size: size(17px);
                }
                @include max(767) {
                    font-size: size(15px);
                }
            }
        }
    }
}
.standard_first{
    position: relative;
    .leaf_ht{
        position: absolute;
        right: 0;
        bottom:-70px;
        @include max(1060) {
            bottom: -40px;
            width: 20%;
        }
        @include max(767) {
            width: 25%;
        }
    }
}



.two_cl_layout{
    margin: 0; padding: 0; display: flex; flex-wrap: wrap; justify-content: space-between;
    li{ list-style: none; width: 47.9%;
        @include max(1300) {
            margin-bottom: 40px;
            width: 48%;
        }
        @include max(820) {
            width: 100%;
        }
        
    }
}

 






.statsList {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
     
    justify-content: center;
    list-style: none;
    margin: 0;
    @include max(1300) {
        gap: 1rem;
    }
    @include max(900) {

      gap: 1rem;
      padding: 2rem 0;
    }
    @include max(600) {
      padding: 1rem 0;
    }
  }
  
  .statsItem {
    background: #f6e2ce;
    border-radius: 24px;
    padding: 3rem 2rem;
    width: 27.5%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0;
    @include max(1300) {
        border-radius: 15px;
        padding: 2.5rem 1rem;
    }
    @include max(600) {
        width: 100%;
      padding: 2rem 1rem;
      border-radius: 10px;
    }
  }
  
  .statsItem h3 {
    font-size: size(84px);
    margin: 0;
    font-weight: 300;
    color: #33241D;
    @include max(1300) {
        font-size: size(60px);
    }
    @include max(900) {
      font-size: size(40px);
    }
    @include max(600) {
      font-size: size(32px);
    }
  }
  
  .statsItem p {
    margin-top: 1.5rem;
    font-size: size(18px);
    color: #33241D;
    font-weight: 400;
    @include max(1300) {
        font-size: size(15px);
      }
    @include max(900) {
      font-size: size(18px);
    }
    @include max(600) {
      font-size: size(16px);
      margin-top: 0.5rem;
    }
  }









// peregrina---style------------
.top_sec{
    .container{
        display: flex;
        flex-wrap: wrap;
        @include max(1600) {
            max-width: 100%;
            padding-left: calc((100% - 1200px) / 2) !important;
            padding-right: 30px;
        }
        @include max(1250) {
            padding-left: 30px !important;
        }
        @include max(992) {
            gap: 30px;
        }
        @include max(767) {
            padding-right: 0px;
        }
    }
    .standard_set{
        align-items:flex-end;
        @include max(1300) {
            max-width: 90%;
            justify-content: space-between;
        }
    }
    .top_left{
        width: 50%;
        @include max(1600) {
            width: 40%;
        }
        @include max(1060) {
            padding-right: 20px;
        }
        @include max(992) {
            width: 100%;
        }
        @include max(767) {
            padding-right: 0;
        }
        p{
            color: #fff;
            max-width: 620px;
            font-size: size(18px);
            line-height: size(29px);
            @include max(1300) {
                font-size: size(15px);
                line-height: size(25px);
            }
            @include max(1060) {
                font-size: size(17px);
                line-height: size(25px);
            }
            @include max(767) {
                font-size: size(15px);
            }
        }
        .top_left_img{
            img{
                @include max(1300) {
                    width: 25%;
                }
            }
        }
    }
    .left_image{
        max-width: 465px;
        margin-top: 55px;
        @include max(1600) {
            max-width: 410px;
        }
        @include max(1300) {
            max-width: 375px;
        }
        @include max(767) {
            max-width: 100%;
            margin-top: 40px;
        }
        figure{
            border-radius: 20px;
            height: 545px;
            @include max(1600) {
                height: 480px;
            }
            @include max(1300) {
                height: 415px;
                border-radius: 15px;
            }
            @include max(767) {
                height: 340px;
                border-radius: 12px;
            }
            img{
                object-fit: cover;
            }
        }
    }
    .top_right{
        width: 50%;
        @include max(1060) {
            width: 40%;
            margin-left: auto;
        }
        @include max(992) {
            width: 80%;
        }
        @include max(767) {
            width: 100%;
            padding-right: 0px;
            margin-top: 25px;
        }
        figure{
            border-radius: 20px;
            -webkit-border-radius:20px;
            -moz-border-radius:20px;
            overflow: hidden;
            @include max(1300) {
                border-radius: 15px;
                -webkit-border-radius:15px;
                -moz-border-radius:15px;
            }
            @include max(767) {
                border-radius: 12px;
                -webkit-border-radius:12px;
                -moz-border-radius:12px;
            }
            img{
                object-fit: cover;
                object-position: left;
            }
        }
    }
    .right_big_img{
        max-width: 620px;
        margin-left: auto;
        position: relative;
        top: 80px;
        @include max(1600) {
            max-width: 550px;
        }
        @include max(1250) {
            max-width: 490px;
        }
        @include max(767) {
            top: 15px;
        }
        figure{
            height: 520px;
            @include max(1600) {
                height: 440px;
            }
            @include max(1250) {
                height: 400px;
            }
            @include max(767) {
                height: 325px;
                
            }
        }
    }
    .right_small_img{
        max-width: 385px;
        top:-15px;
        position: relative;
        left: -65px;
        @include max(1600) {
            max-width: 350px;
            top: -70px;
        }
        @include max(1300) {
            max-width: 315px
        }
        @include max(767) {
            left: 0;
            right: 0;
            margin: 0 auto;
            max-width: 260px;
            top: -30px;
        }
        figure{
            height: 320px;
            @include max(1300) {
                height: 270px;
            }
            @include max(1060) {
                height: 255px;
            }
            @include max(767) {
                height: 210px;
            }

        }
    }
    @include max(767) {
        padding-bottom: 0px;
    }
}
:global(body.rtl) {
    .top_sec {
        .right_small_img{
            left: auto;
            right: -65px;
        }
    }
}








