import React, { useEffect, useRef, useState } from "react";
import styles from "@/components/contactForm/contactForm.module.scss";
import parse from "html-react-parser";
import { useRouter } from "next/router";
import Image from "next/image";
import Icons from '@/public/Icons';

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;

const InquireNow = ({ crycform }) => {
  const router = useRouter();
  const [fileName, setFileName] = useState("");
  const [file, setFile] = useState(null);
  const [resetFileUpload, setResetFileUpload] = useState(false);
  const [isChecked, setIsChecked] = useState(false);

  const [formTouched, setFormTouched] = useState(false);
  const [formValid, setFormValidation] = useState(false);
  const [formSent, setFormSent] = useState(false);
  const [formSuccess, setFormSuccess] = useState(false);
  const [formError, setFormError] = useState("");
  const [selectedService, setSelectedService] = useState("");
  const [serviceError, setServiceError] = useState("");

  const fullnameInputRef = useRef();
  const CompanynameInputRef = useRef();
  const emailIdInputRef = useRef();
  const phoneNumberInputRef = useRef();
  const messageInputRef = useRef();

  const [validationMessages, setValidationMessages] = useState({
    fullName: "",
    companyName: "",
    email: "",
    phone: "",
    file: "",
  });

  useEffect(() => {
    const restrictSpace = document.querySelectorAll('input[type="tel"], input[type="text"], input[type="email"]');
    restrictSpace.forEach(function (input) {
      input.addEventListener('keypress', function (e) {
        if (e.which === 32 && !this.value.length) e.preventDefault();
      });
    });

    const restrictSymbols = document.querySelectorAll('#organization-name, #first-name, #last-name');
    restrictSymbols.forEach(function (input) {
      input.setAttribute("onkeydown", "return /[a-zA-Z ]/.test(event.key)");
    });

    const phoneInputs = document.querySelectorAll('input[id="phone"], input[id="mobile"]');
    phoneInputs.forEach(function (input) {
      input.addEventListener('keypress', function (e) {
        if (!/^\d$/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete') {
          e.preventDefault();
        }
      });

      input.addEventListener('input', function (e) {
        input.value = input.value.replace(/[^\d]/g, '');
      });
    });
  }, [router]);

  const validateService = () => {
    if (!selectedService) {
      setServiceError(router.locale === "ar" ? ` يرجى اختيار نوع المشروع .` : `Project Type is required.`);
      return false;
    }
    setServiceError("");
    return true;
  };

  const validateField = (name, value, label) => {
    let message = "";
    
    // Trim whitespace and check if empty
    const trimmedValue = typeof value === 'string' ? value.trim() : value;
    
    if (!trimmedValue) {
      message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
    } else if (name === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(trimmedValue)) {
        message = router.locale === "ar" ? `تنسيق البريد الإلكتروني غير صالح.` : `Invalid email format.`;
      }
    } else if (name === "phone") {
      const phoneDigits = trimmedValue.replace(/\D/g, "");
      if (phoneDigits.length < 3) {
        message = router.locale === "ar" ? `رقم هاتف صالح مطلوب.` : `Valid phone number is required.`;
      }
    } else if (name === "file" && !value) {
      message = router.locale === "ar" ? `${label} مطلوب.` : `${label} is required.`;
    }
    
    return message;
  };

  const fieldChangeHandler = (label) => (e) => {
    const message = validateField(e.target.name, e.target.value, label);
    setValidationMessages((prev) => ({ ...prev, [e.target.name]: message }));
    e.target.classList.toggle("form-invalid", !!message);
    e.target.classList.toggle("form-valid", !message);
    setFormValidation(!message);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      const allowedExtensions = ['pdf', 'doc', 'docx'];
      const ext = selectedFile.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(ext)) {
        setValidationMessages(prev => ({
          ...prev,
          file: router.locale === "ar"
            ? "صيغة الملف غير مدعومة. يرجى تحميل ملف PDF أو DOC أو DOCX."
            : "Invalid file format. Please upload a PDF, DOC, or DOCX file."
        }));
        setFileName("");
        setFile(null);
        e.target.value = "";
        return;
      }
      setFileName(selectedFile.name);
      setFile(selectedFile);
      setValidationMessages(prev => ({ ...prev, file: "" }));
    } else {
      setFileName("");
      setFile(null);
    }
  };

  const contactFormHandler = (e) => {
    e.preventDefault();

    // Check if terms accepted
    if (!isChecked) {
      setFormError(router.locale === "ar" ? `يجب تحديد خانة الموافقة على الشروط والأحكام.` : `You must agree to the terms and conditions.`);
      return;
    }

    // Get current values with trim
    const fullNameValue = fullnameInputRef.current.value.trim();
    const companyNameValue = CompanynameInputRef.current.value.trim();
    const emailValue = emailIdInputRef.current.value.trim();
    const phoneValue = phoneNumberInputRef.current.value.trim();

    // Validate each field
    const fullNameMessage = validateField("fullName", fullNameValue, router.locale === "ar" ? `الاسم الكامل` : `Full Name`);
    const companyNameMessage = validateField("companyName", companyNameValue, router.locale === "ar" ? `موقع المزرعة` : `Farm Location`);
    const emailMessage = validateField("email", emailValue, router.locale === "ar" ? `البريد الإلكتروني` : `Email Address`);
    const phoneMessage = validateField("phone", phoneValue, router.locale === "ar" ? `رقم الهاتف` : `Phone Number`);
    const fileMessage = validateField("file", file, router.locale === "ar" ? `رفع ملف` : `File`);
    // const isServiceValid = validateService();

    // Update validation messages
    setValidationMessages({
      fullName: fullNameMessage,
      companyName: companyNameMessage,
      email: emailMessage,
      phone: phoneMessage,
      file: fileMessage,
    });

    // Check if form is valid
    const isValid = !fullNameMessage && !companyNameMessage && !emailMessage && !phoneMessage && !fileMessage;
    
    // Debug log - remove this in production
    console.log('Validation Results:', {
      fullNameMessage,
      companyNameMessage,
      emailMessage,
      phoneMessage,
      fileMessage,
      // isServiceValid,
      isValid,
      values: {
        fullNameValue,
        companyNameValue,
        emailValue,
        phoneValue,
        // selectedService,
        file: file ? file.name : 'No file'
      }
    });

    setFormValidation(isValid);

    if (isValid) {
      let formData = new FormData();
      formData.append("full-name", fullNameValue);
      formData.append("company-name", companyNameValue);
      formData.append("your-email", emailValue);
      formData.append("your-phone", phoneValue);
      // formData.append("your-service", selectedService);
      formData.append("your-message", messageInputRef.current.value);
    //   formData.append("file-attached", file);
      formData.append("_wpcf7_unit_tag", "123");

      setFormSent(true);
      setFormError(""); // Clear any previous errors

      fetch(`${baseURL}wp-json/contact-form-7/v1/contact-forms/1115/feedback`, {
        method: "POST",
        body: formData,
        redirect: "follow",
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.status === "mail_sent") {
            document.getElementById("contact-form").reset();
            fullnameInputRef.current.value = "";
            CompanynameInputRef.current.value = "";
            emailIdInputRef.current.value = "";
            phoneNumberInputRef.current.value = "";
            messageInputRef.current.value = "";
            // setSelectedService("");
            setFile(null);
            setFileName("");
            setResetFileUpload(true);
            setIsChecked(false);

            setFormSuccess(data.message);
            setTimeout(() => setFormSuccess(""), 5000);
          } else {
            setFormError(data.message);
          }
          setFormSent(false);
        })
        .catch((error) => {
          console.error('Form submission error:', error);
          setFormError(router.locale === "ar" 
            ? `حدث خطأ أثناء الإرسال. يرجى المحاولة مرة أخرى.`
            : `An error occurred while submitting. Please try again.`);
          setFormSent(false);
        });
    } else {
      setFormError(router.locale === "ar"
        ? `يوجد خطأ في حقل واحد أو أكثر. يرجى المراجعة والمحاولة مرة أخرى`
        : `One or more fields have an error. Please check and try again.`);
      setFormTouched(true);
    }
  };

  return (
    <form onSubmit={contactFormHandler} className={styles.inquiryForm} id="contact-form">      
      <ul className={`${styles.formGroup} `}>
        <li>
          <input
            type="text"
            name="fullName"
            id="full-name"                           
            ref={fullnameInputRef}
            placeholder={crycform?.fr_full_name && crycform?.fr_full_name}
            onChange={fieldChangeHandler(router.locale === "ar" ? `الاسم الكامل` : `Full Name`)}
          />                      
          <span className={styles.inp_line}></span>
          {validationMessages.fullName && <span className="form-error">{validationMessages.fullName}</span>}
        </li>       
        
        <li>
          <input
            type="email"
            name="email"
            id="email-id" 
            placeholder={crycform?.fr_email && crycform?.fr_email}            
            ref={emailIdInputRef}
            onChange={fieldChangeHandler(router.locale === "ar" ? `بريد إلكتروني` : `Email`)}                  
          />
          <span className={styles.inp_line}></span>
          {validationMessages.email && <span className="form-error">{validationMessages.email}</span>}
        </li>
        
        <li>
          <input
            type="text"
            name="phone"
            id="phone" 
            placeholder={crycform?.fr_phone_number && crycform?.fr_phone_number}            
            ref={phoneNumberInputRef}
            onChange={fieldChangeHandler(router.locale === "ar" ? `هاتف` : `Phone`)}                 
          />
          <span className={styles.inp_line}></span>
          {validationMessages.phone && <span className="form-error">{validationMessages.phone}</span>}
        </li>
         <li>
          <input
            type="text"
            name="companyName"
            id="company-name"              
            ref={CompanynameInputRef}
            placeholder={crycform?.fr_location && crycform?.fr_location}
            onChange={fieldChangeHandler(router.locale === "ar" ? `موقع المزرعة` : `Farm Location`)}
          />
          <span className={styles.inp_line}></span>
          {validationMessages.companyName && <span className="form-error">{validationMessages.companyName}</span>}
        </li>
        
        
      </ul>
      
      <div className={styles.t_wrap} data-aos="fade-up" data-aos-duration="1000"> 
        <textarea
          name="Message"
          id="message" 
          ref={messageInputRef}
          placeholder={crycform?.fr_message && crycform?.fr_message}
          onChange={fieldChangeHandler}
        />                    
        <span className={styles.inp_line}></span>
      </div>

      {/* File Upload */}
      <div className={styles.fileUpload} data-aos="fade-up" data-aos-duration="1000">
        <label className={styles.uploadBtn}>
          {fileName ? router.locale === "ar" ? "تحديث الملف" : "Update File" : router.locale === "ar" ? "تحميل ملف" : "Upload a File"}
          <input 
            type="file" 
            accept=".pdf,.doc,.docx"
            onChange={handleFileChange} 
          />
        </label>
        <span>{fileName || crycform?.fr_file_upload_text}</span>                    
      </div>
      {validationMessages.file && <span className="form-error">{validationMessages.file}</span>}
      
      <div className={styles.terms}>
        <input
          type="checkbox"
          id="terms"
          checked={isChecked}
          onChange={(e) => setIsChecked(e.target.checked)}
        />
        <label htmlFor="terms">{crycform?.fr_checkbox_text && parse(crycform?.fr_checkbox_text)}</label>
      </div>

      <button type="submit" className={styles.submitBtn} disabled={formSent}>
        <span className={styles.btn_text}>
          {formSent ? (router.locale === "ar" ? "جارٍ الإرسال..." : "Sending...") : crycform?.fr_submit}
        </span>
        <span className={styles.arw}><Icons size={25} color="#fff" icon='Right-arw' /></span>
      </button>
      
      {formSuccess && <div className="msg_success"><span className="form-success">{formSuccess}</span></div>}
      {formError && <div className="msg_error"><span className="form-error">{formError}</span></div>}
    </form>
  );
};

export default InquireNow;