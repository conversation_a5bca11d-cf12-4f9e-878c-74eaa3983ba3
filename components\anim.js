"use client";

import { useEffect, useRef } from "react";
import { useInView } from "react-intersection-observer";
import anime from "animejs";

const AnimatedTitle = ({ title, tag: Tag = "h1", className = "", moduleClass = "" }) => {
  const titleRef = useRef(null);
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.3,
  });

  useEffect(() => {
    if (inView) {
      const textWrapper = titleRef.current.querySelector(".letters");
      // Split the title by break tags and process each part
      const parts = title.split(/<\/?br\s*\/?>/);
      let processedHTML = '';
      
      parts.forEach((part, index) => {
        if (part.trim()) {
          const letters = part.split('').map(letter => 
            `<span class='letter'>${letter}</span>`
          ).join('');
          processedHTML += letters;
        }
        if (index < parts.length - 1) {
          processedHTML += '<br/>';
        }
      });

      textWrapper.innerHTML = processedHTML;

      anime.timeline({ loop: false })
        .add({
          targets: ".ml1 .letter",
          scale: [0.3, 1],
          opacity: [0, 1],
          translateZ: 0,
          easing: "easeOutExpo",
          duration: 600,
          delay: (el, i) => 70 * (i + 1),
        })
        .add({
          targets: ".ml1 .line",
          scaleX: [0, 1],
          opacity: [0.5, 1],
          easing: "easeOutExpo",
          duration: 700,
          offset: "-=875",
          delay: (el, i, l) => 80 * (l - i),
        })
        .add({
          targets: ".ml1",
          opacity: 1,
          duration: 1000,
          easing: "easeOutExpo",
          delay: 1000,
        });
    }
  }, [inView, title]);

  return (
    <div ref={ref} className="">
      <Tag
        ref={titleRef}
        className={`ml1 font-black ${className} ${moduleClass}`}
      >
        <span className="">
          <span className="letters" dangerouslySetInnerHTML={{ __html: title }} />
        </span>
      </Tag>


 {/* call like below in pages */}

      {/* <AnimatedTitle
        title={service.title}
        tag="h4"
        className="text-blue-500 text-center"
        moduleClass={styles.customTitle}
      /> */}

      
    </div>
  );
};

export default AnimatedTitle;