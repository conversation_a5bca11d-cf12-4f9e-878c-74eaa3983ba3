import React, { useEffect, useRef, useState } from "react";
import styles from "./boxDesc.module.scss";
import Parallax from "@/components/Paralax";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import Image from "next/image";
import Icons from "@/public/Icons";
import Link from "next/link";

// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import CardText from "@/widget/CardText";
import { Autoplay } from "swiper/modules";
import BottomCardSlider from "../BottomCardSlider";
import parse from "html-react-parser";

gsap.registerPlugin(ScrollTrigger);

const BoxDesc = ({
  imageSrc,
  title,
  desc_list,
  shortTtile,
  content,
  certification = [],
  buttonLink,
  buttonText,
  buttonType = "link",
  subTitle = [],
  position = "left",
  bgColor = "default",
  BottomSliderData = false,
  bottomSliderDataList = [],
}) => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const shortTtileRef = useRef(null);
  const contentRef = useRef(null);
  const imageRef = useRef(null);
  const buttonRef = useRef(null);
  const certRef = useRef(null);

  useEffect(() => {
    if (
      (!content || content.length === 0) &&
      !buttonText &&
      certification.length === 0
    ) {
      return;
    }

    const ctx = gsap.context(() => {
      gsap.fromTo(
        titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            toggleActions: "play reverse play reverse",
            scrub: false,
            once: false,
          },
        }
      );

      gsap.fromTo(
        shortTtileRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            toggleActions: "play reverse play reverse",
            scrub: false,
            once: false,
          },
        }
      );

      if (content?.length > 0 || desc_list?.length > 0 || buttonText) {
        gsap.fromTo(
          contentRef.current,
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            ease: "power2.out",
            stagger: 0.3,
            scrollTrigger: {
              trigger: sectionRef.current,
              start: "top 85%",
              toggleActions: "play reverse play reverse",
              scrub: false,
              once: false,
            },
          }
        );
      }

      gsap.fromTo(
        imageRef.current,
        { opacity: 0, 
          // scale: 0.1
         },
        {
          opacity: 1,
          // scale: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            toggleActions: "play reverse play reverse",
            scrub: false,
            once: false,
          },
        }
      );

      if (buttonText) {
        gsap.fromTo(
          buttonRef.current,
          { opacity: 0, y: 20 },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
              trigger: sectionRef.current,
              start: "top 90%",
              toggleActions: "play reverse play reverse",
              scrub: false,
              once: false,
            },
          }
        );
      }

      if (certification.length > 0) {
        gsap.fromTo(
          certRef.current,
          { opacity: 0, y: 20 },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
            stagger: 0.2,
            scrollTrigger: {
              trigger: sectionRef.current,
              start: "top 95%",
              toggleActions: "play reverse play reverse",
              scrub: false,
              once: false,
            },
          }
        );
      }
    }, sectionRef);

    return () => {
      // Clean up all ScrollTriggers and animations
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      gsap.globalTimeline.clear();
    };
  }, [content, buttonText, certification, desc_list]);

  const [isBottomTabActive, setIsBottomTabActive] = useState(false);

  const SliderAcitveHandler = () => {
    setIsBottomTabActive((prev) => !prev);
  };

  return (
    <>
      <div
        ref={sectionRef}
        className={`${styles.boxDesc} ${position === "right" ? styles.right : styles.left
          } ${bgColor === "white" ? styles.whiteBg : ""}`}
      >
        <div className={styles.box_image} ref={imageRef}>
          <Parallax mediaSrc={imageSrc} altText={title} />
        </div>
        <div className={styles.box_contents}>
          {shortTtile && <h4 ref={shortTtileRef}>{shortTtile}</h4>}
          {title && (
            <h2
              ref={titleRef}
              dangerouslySetInnerHTML={{
                __html: title.replace(/\n/g, "<br />"),
              }}
            />
          )}
          {subTitle && <h3>{subTitle}</h3>}

          {(content || desc_list?.length > 0 || buttonText) && (
            <div ref={contentRef}>
              
              {content && parse(content)}

              {desc_list?.length > 0 && (
                <ul className={styles.desc_list}>
                  {desc_list.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              )}

              {/* ---------if slider button isnt active----------- */}

              {buttonText && !BottomSliderData && (
                <div ref={buttonRef} className={styles.button_sec}>
                  {buttonType === "download" ? (
                    <a
                      href={buttonLink}
                      className="main_btn download_btn"
                      download
                      target="_blank"
                    >
                      <span className="button_text">{buttonText}</span>
                      <span className="arrow">
                        <Icons size={25} color="#fff" icon="Right-arw" />
                      </span>
                    </a>
                  ) : (
                    buttonText && buttonLink && <Link
                      href={buttonLink}
                      className={`main_btn ${styles.link_btn}`}
                    >
                      <span className="button_text">{buttonText}</span>
                      <span className="arrow">
                        <Icons size={25} color="#fff" icon="Right-arw" />
                      </span>
                    </Link>
                  )}
                </div>
              )}

              {/* ---------if slider button  active----------- */}

              {/* {BottomSliderData && (
                <div ref={buttonRef} className={styles.button_sec}>
                  <button onClick={SliderAcitveHandler} className="main_btn  ">
                    <span className="button_text">{buttonText}</span>
                    <span className="arrow">
                      <Icons size={25} color="#fff" icon="Right-arw" />
                    </span>
                  </button>
                </div>
              )} */}
            </div>
          )}

          {certification.length > 0 && (
            <ul className={styles.certificationList} ref={certRef}>
              {certification.map((cerPic, index) => (
                <li key={index}>
                  <Image
                    src={cerPic}
                    width={133}
                    height={137}
                    alt={`Certification ${index + 1}`}
                    quality={100}
                  />
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* <div
        className={`${BottomProduct.bottom_product_list} ${isBottomTabActive == false && BottomProduct.product_list_hidden
          }`}
      >
        <Swiper
          slidesPerView={1}
          spaceBetween={10}
          loop={true}
          autoplay={{
            disableOnInteraction: false,
            delay: 2000,
          }}
          speed={1500}
          className="mySwiper product_list_card_swiper"
          breakpoints={{
            700: {
              spaceBetween: 20,
              slidesPerView: 2,
            },

            1024: {
              spaceBetween: 20,
              slidesPerView: 3,
            },
          }}
          modules={[Autoplay]}
        >
          {bottomSliderDataList.map((data, index) => (
            <SwiperSlide key={index}>
              <div className={BottomProduct.product_list_item}>{data}</div>

              <CardText data={data} />
            </SwiperSlide>
          ))}
        </Swiper> 
      </div>*/}


      {/* <div className={isBottomTabActive == false && styles.product_list_hidden}> */}
      {BottomSliderData &&
        <BottomCardSlider products={bottomSliderDataList} />
      }
      {/* </div> */}
    </>
  );
};

export default BoxDesc;
