import React from 'react';
import styles from './single_title_banner.module.scss';
import Image from 'next/image';
import parse from "html-react-parser";

const SingleTitleBanner = ({ title, videoSrc, imageSrc, description, logoSrc }) => {
  return (
    <div className={styles.banner_container}>
      {videoSrc && (
        <video
          width="100%"
          height="auto"
          autoPlay
          muted
          loop
          playsInline
          className={styles.media}
        >
          <source src={videoSrc} type="video/mp4" />
        </video>
      )}
      {imageSrc && (
        <Image 
          src={imageSrc} 
          alt={title || 'Banner background'} 
          layout="fill" 
          objectFit="cover" 
          className={styles.media}
        />
      )}
      <div className={`container ${styles.container} ${logoSrc ? styles.with_logo : ''}`}>
      {logoSrc && (
          <div className={styles.logo_wrapper}>
            <Image
              src={logoSrc}
              alt="Logo"
              width={200}
              height={100}
              className={styles.logo}
            />
          </div>
        )}
        <div className={styles.title_wrapper}>
           {title &&
              <h2 className={`main_title ${styles.main_title}`} data-aos="fade-up" data-aos-duration="1000">
                {title && parse(title)}
              </h2>
            }
            {description && (
              <div 
                className={styles.description} 
                data-aos="fade-up" 
                data-aos-duration="1000" 
                data-aos-delay="200"
              >
                {description && parse(description)}
              </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default SingleTitleBanner; 
