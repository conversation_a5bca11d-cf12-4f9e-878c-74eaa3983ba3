import React, { useEffect, useState } from "react";
import InnerBanner from '@/components/innerBanner/InnerBanner';
import styles from '../news.module.scss';
import Image from "next/image";
import Icons from "@/public/Icons";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import NewsCard from "@/components/cards/NewsCard";

import { useRouter } from "next/router";
import Yoast from "@/components/yoast";
import parse from "html-react-parser";
import { getNewspage, getInsightsPosts, getInsightspostSlug } from "@/utils/lib/server/publicServices"; 
import NewsletterForm from "@/components/newsletter/Newsletter";

const Index = (props) => {
    const [viewCount, setViewCount] = useState(0);
    // Remove: const [likeCount, setLikeCount] = useState(0);
    // Remove: const [hasLiked, setHasLiked] = useState(false);

    useEffect(() => {
        document.documentElement.classList.add('news_detail_page');
        return () => {
            document.documentElement.classList.remove('news_detail_page');
        };
    }, []);

    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    const router = useRouter();
    const currentUrl = typeof window !== "undefined"
        ? window.location.href
        : `https://alula-peregrina-dev.vercel.app/${router.asPath}`;
    const postTitle = props?.InsightsData?.title?.rendered || "";

    // Social share links
    const shareLinks = [
        {
            name: "X",
            icon: "twitter",
            url: `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(postTitle)}`
        },
        {
            name: "Facebook",
            icon: "Facebook",
            url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`
        },
        {
            name: "LinkedIn",
            icon: "linkedin2",
            url: `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(currentUrl)}&title=${encodeURIComponent(postTitle)}`
        },
        // {
        //     name: "WhatsApp",
        //     icon: "whatsapp",
        //     url: `https://api.whatsapp.com/send?text=${encodeURIComponent(postTitle + " " + currentUrl)}`
        // }
    ];

    const yoastData = props?.InsightsData?.yoast_head_json;
    if (!props?.InsightsData) {
            return null;
    }

    useEffect(() => {
        // Track page view on component mount
        const trackView = async () => {
            try {
                await fetch('/api/track-post', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        postId: props?.InsightsData?.id,
                        action: 'view'
                    })
                });
                
                // Get current counts
                const counts = await fetch(`/api/get-post-stats?postId=${props?.InsightsData?.id}`);
                const data = await counts.json();
                setViewCount(data.views || 0);
            } catch (error) {
                console.error('Failed to track view:', error);
            }
        };
        
        if (props?.InsightsData?.id) {
            trackView();
        }
    }, [props?.InsightsData?.id]);

    return (
        <div>
            {yoastData && <Yoast meta={yoastData} />}
            {props?.pageData?.acf && (
                (props.pageData.acf.banner_text ||
                    props.pageData.acf.sub_text ||
                    props.pageData.acf.banner_image) && (
                    <InnerBanner
                        bannerBg={props.pageData.acf.banner_image?.url}
                        title={props.pageData.acf.banner_text && parse(props.pageData.acf.banner_text)}
                        content={props.pageData.acf.sub_text && parse(props.pageData.acf.sub_text)}
                    />
                )
            )}
            {/* <InnerBanner bannerBg="/images/news_banner.jpg" title="News" /> */}
            <div className={`${styles.pb_100} ${styles.pt_120} ${styles.news_sec}`}>
                <div className='container'>
                    <div className={styles.news_details}>
                        <div className={styles.detail_left}>
                            <ul className={styles.top_desc} data-aos="fade-up" data-aos-duration="1000">
                                {props?.InsightsData?.acf?.news_tag &&
                                    <li>{parse(props?.InsightsData?.acf?.news_tag)}</li>
                                }
                                {props?.InsightsData?.acf?.blog_writer_by &&
                                    <li>{router.locale === 'ar' ? 'بواسطة' : 'by'} <span>{parse(props?.InsightsData?.acf?.blog_writer_by)}</span></li>
                                }
                            </ul>
                            <h2 className={`${styles.main_title} main_title`} data-aos="fade-up" data-aos-duration="1000">
                                {parse(props?.InsightsData?.title?.rendered)}
                            </h2>
                            <div className={styles.detail_left_content} data-aos="fade-up" data-aos-duration="1000">                                
                                <div data-aos="fade-up" data-aos-duration="1000">
                                    {parse(props?.InsightsData?.content?.rendered)}                                   
                                </div>
                                <ul className={styles.news_date_desc} data-aos="fade-up" data-aos-duration="1000">
                                    {props?.InsightsData?.date &&
                                        <li>{new Date(props?.InsightsData?.date).toLocaleDateString('en-US', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        })}</li>
                                    }
                                    <li><span><Image src='/images/eye.png' width={22} height={16} alt="image" quality={100} /></span>{viewCount.toLocaleString()} views</li>
                                    <li><span><Image src='/images/heart.png' width={18} height={17} alt="image" quality={100} /></span>5</li>
                                </ul>
                            </div>
                        </div>
                        <div className={styles.detail_right}>
                            <ul className={styles.social} data-aos="fade-up" data-aos-duration="1000">
                                <li>{router.locale === 'ar' ? ': شارك' : 'Share :'}</li>
                                {shareLinks.map((item, idx) => (
                                    <li key={idx}>
                                        <Link href={item.url} target="_blank" rel="noopener noreferrer">
                                            <Icons size={20} icon={item.icon} />
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                            {props?.pageData?.acf?.subscribe_now && (
                                <div className={styles.news_subscription_form}>
                                    <h4 data-aos="fade-up" data-aos-duration="1000">{props?.pageData?.acf?.subscribe_now && parse(props?.pageData?.acf?.subscribe_now)}</h4>
                                    <NewsletterForm lang={router.locale} />                               
                                    <p>{props?.pageData?.acf?.sub_title && parse(props?.pageData?.acf?.sub_title)}</p>
                                </div>
                            )}                           
                        </div>
                    </div>
                </div>
            </div>
            {props?.newsData && props?.newsData.length > 0 && (
                <div className={`${styles.related_news} ${styles.pb_120}`}>
                    <div className="container">
                        <h2 className="main_title" data-aos="fade-up" data-aos-duration="1000">Related News</h2>
                        <Swiper
                            slidesPerView={2}
                            pagination={{
                                clickable: true,
                            }}
                            breakpoints={{
                                0: {
                                    slidesPerView: 1,
                                },
                                768: {
                                    slidesPerView: 2,
                                    spaceBetween: 20,
                                },
                                1024: {
                                    slidesPerView: 3,
                                    spaceBetween: 40,
                                },
                            }}
                            className="news_swiper"
                        >
                            {props?.newsData.map((news, index) => (
                                <SwiperSlide key={index}>
                                    <NewsCard
                                        date={new Date(news.date).getDate().toString()}
                                        monthYear={`${new Date(news.date).toLocaleDateString('en-US', { month: 'long' })} ${new Date(news.date).getFullYear()}`}
                                        title={news.title?.rendered}
                                        imageUrl={news?.acf?.listing_image?.url || '/images/no-image.png'}
                                        description={news.acf?.description}
                                        link={news.acf?.external_news_link ? news?.acf?.external_news_link?.url : `/news/${news.slug}`}
                                        target={news.acf?.external_news_link ? '_blank' : '_self'}
                                        extraClass="news_page_card"
                                    />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
            )}
           
        </div>
    );
};

export default Index;


export async function getStaticPaths(locale) {  
   
const InsightsPostsslug = await getInsightsPosts(locale.locales); // Fetch all advisory posts
    const paths = InsightsPostsslug.map((post) => ({
        params: { slug: post.slug },
    }));   

  return {
    paths,
    fallback: "blocking", // Allow SSR for paths not generated at build time
  };
}


export const getStaticProps = async ({params, locale}) => {
  const slug = params.slug; 
  const PageData = await getNewspage(locale); 
    const InsightsData = await getInsightspostSlug(slug, locale);  
    const InsightsPostsData = await getInsightsPosts(locale.locale); 
   
  let InsightsPosts = [];
  if (InsightsData[0] && InsightsData[0]?.acf && Array.isArray(InsightsData[0]?.acf?.related_news)) {
    InsightsPosts = InsightsData[0]?.acf?.related_news;
  }

  // Format Insights Posts for use in the component, excluding current post
  let InsightsPostsRelation = [];
  if (InsightsPosts?.length > 0) {
    InsightsPostsRelation = InsightsPosts?.map((id) =>
      InsightsPostsData?.find((post) => post.id === id)
    ).filter(Boolean) // Remove undefined values
     .filter((post) => post.id !== InsightsData[0]?.id); // Exclude current post
  }

  return {
    props: {      
      pageData: PageData || null, // use lowercase 'p'
      InsightsData: InsightsData[0] || null, // use lowercase 'p'
      newsData: InsightsPostsRelation || [],
    },
    revalidate: 10,
  };
};

