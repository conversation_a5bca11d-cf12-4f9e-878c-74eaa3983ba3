import React, { useEffect } from "react";
import InnerBanner from '@/components/innerBanner/InnerBanner';
import styles from '../news.module.scss';
import Image from "next/image";
import Icons from "@/public/Icons";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import NewsCard from "@/components/cards/NewsCard";

const newsData = [
    {
        date: '11',
        monthYear: 'September 2024',
        title: "February 22nd marks more than a moment in history",
        description: "it’s the foundation of a legacy. A day that shaped a nation, rooted in strength, vision, and unity. From 1727 to today, the spirit of Saudi Arabia continues to rise. ",
        imageUrl: "/images/news_1.jpg",
        id: 1
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "This Peregrina Season, we celebrated the heart of AlUla",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_2.jpg",
        id: 2
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "Ten farmers received the Gold Farmers Excellence Award.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_3.jpg",
        id: 3
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "Sustainability is at the heart of AlUla Peregrina.",
        description: "With responsible harvesting and eco-friendly practices, we protect AlUla’s natural beauty, support local communities, and ensure a greener future. ",
        imageUrl: "/images/news_4.jpg",
        id: 4
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "AlUla Peregrina Oil harnesses nature with sustainable methods.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_5.jpg",
        id: 5
    },
    {
        date: '11',
        monthYear: 'September 2024',
        title: "AlUla Peregrina Oil is lightweight, non-comedogenic.",
        description: "its people, traditions, and ambitions. A journey of connection, growth, and discovery captured in every moment. #AlUlaPeregrina",
        imageUrl: "/images/news_6.jpg",
        id: 6
    },
]

const Index = () => {
    useEffect(() => {
        document.documentElement.classList.add('news_detail_page');
        return () => {
            document.documentElement.classList.remove('news_detail_page');
        };
    }, []);

    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    return (
        <div>
            <InnerBanner bannerBg="/images/news_banner.jpg" title="News" />
            <div className={`${styles.pb_100} ${styles.pt_120} ${styles.news_sec}`}>
                <div className='container'>
                    <div className={styles.news_details}>
                        <div className={styles.detail_left}>
                            <ul className={styles.top_desc} data-aos="fade-up" data-aos-duration="1000">
                                <li>Tips & tricks</li>
                                <li>by <span>Admin</span></li>
                            </ul>
                            <h2 className={`${styles.main_title} main_title`} data-aos="fade-up" data-aos-duration="1000">
                                Saudi Arabia’s Avalon Pharma partners with AlUla Peregrina Trading
                            </h2>
                            <div className={styles.detail_left_content} data-aos="fade-up" data-aos-duration="1000">
                                <h3>The patented formulas that will emerge from the collaboration will undergo a few months of formulation, tests and assessment process</h3>
                                <Image src='/images/news_detail.jpg' width={936} height={522} alt="image" quality={100} data-aos="fade-up" data-aos-duration="1000" />
                                <div data-aos="fade-up" data-aos-duration="1000">
                                    <p>Saudi Arabia’s Avalon Pharma has partnered with AlUla Peregrina Trading to create homegrown premium cosmetic and dermatologic products.</p>
                                    <p>The solutions will be grounded in science and the potency of natural patented AlUla Peregrina active ingredients.</p>
                                    <p>Inspired by the powers of the arid and sizzling Desert of Arabia, the luxury skin and hair care journey will feature exclusive products made in Saudi designed to provide solutions grounded in science and the potency of natural ingredients.</p>
                                </div>
                                <ul className={styles.news_date_desc} data-aos="fade-up" data-aos-duration="1000">
                                    <li>October 11, 2024</li>
                                    <li><span><Image src='/images/eye.png' width={22} height={16} alt="image" quality={100} /></span>1,353 views</li>
                                    <li><span><Image src='/images/heart.png' width={18} height={17} alt="image" quality={100} /></span>5</li>
                                </ul>
                            </div>
                        </div>
                        <div className={styles.detail_right}>
                            <ul className={styles.social} data-aos="fade-up" data-aos-duration="1000">
                                <li>Share:</li>
                                <li><Link href='https://x.com/alulaperegrina?s=21' target="_blank"><Icons size={19} icon="twitter" /></Link></li>
                                <li><Link href='https://www.instagram.com/alula_peregrina?igsh=Nm5rcjJ6cHJ0ZWNs' target="_blank"><Icons size={20} icon="Instagram" /></Link></li>
                                <li><Link href='https://www.facebook.com/share/ErmBgUdJCrH4wXPz/?mibextid=LQQJ4d' target="_blank"><Icons size={20} icon="Facebook" /></Link></li>
                                <li><Link href='https://in.linkedin.com/' target="_blank"><Icons size={21} icon="linkedin2" /></Link></li>
                            </ul>
                            <div className={styles.news_subscription_form}>
                                <h4 data-aos="fade-up" data-aos-duration="1000">Subscribe Now</h4>
                                <form data-aos="fade-up" data-aos-duration="1000">
                                    <ul>
                                        <li><input type="email" className={styles.inpField} placeholder="Your Email" required /></li>
                                        <li><input type="submit" className={styles.submit} value='Subscribe' />
                                            <span></span>
                                        </li>
                                    </ul>

                                </form>
                                <p>Be the first to know about the latest news and events!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className={`${styles.related_news} ${styles.pb_120}`}>
                <div className="container">
                    <h2 className="main_title" data-aos="fade-up" data-aos-duration="1000">Related News</h2>
                    <Swiper
                        slidesPerView={2}
                        pagination={{
                            clickable: true,
                        }}
                        breakpoints={{
                            0: {
                                slidesPerView: 1,
                            },
                            768: {
                                slidesPerView: 2,
                                spaceBetween: 20,
                            },
                            1024: {
                                slidesPerView: 3,
                                spaceBetween: 40,
                            },
                        }}
                        className="news_swiper"
                    >
                        {newsData.map((news, index) => (
                            <SwiperSlide key={index}>
                                <NewsCard
                                    date={news.date}
                                    monthYear={news.monthYear}
                                    title={news.title}
                                    imageUrl={news.imageUrl}
                                    description={news.description}
                                    link={`/news/${news.id}`}
                                    extraClass="news_page_card"
                                />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            </div>
        </div>
    );
};

export default Index;
