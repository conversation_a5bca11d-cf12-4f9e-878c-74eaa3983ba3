import React, { useEffect, useState } from "react";
import Header from "@/components/header/header";
import Footer from "@/components/footer/footer";
import Head from "next/head";
// import LenisScroll from "./LenisScroll";
import { Open_Sans, Cabin } from 'next/font/google';
import { useRouter } from "next/router";

const openSans = Open_Sans({ subsets: ['latin'], weight: ['300','400','500','600', '700'], variable: '--font-open-sans' });	

export default function RootLayout({ children }) {


	const [language, setLanguage] = useState("");
	const router = useRouter();
	const { pathname, asPath } = router;
	useEffect(() => {
	  if (router.locale === "ar") {
		setLanguage("ar");
		document.body.classList.add("rtl");
		document.documentElement.setAttribute("dir", "rtl");
		document.documentElement.setAttribute("lang", "ar");
	  } else {
		setLanguage("en");
		document.body.classList.remove("rtl");
		document.documentElement.setAttribute("dir", "ltr");
		document.documentElement.setAttribute("lang", "en");
	  }
	}, [router]);




	
	return (
		<>
			<Head>
				<title>Alula Peregrina</title>
				<meta name="description" content="From the desert of arabia to exceptional ingredients" />
				<meta name="viewport" content="width=device-width, initial-scale=1" />
				<link rel="icon" href="/favicon.ico" />
			</Head>
			<style jsx global>{`
                :root {
                    --font-open-sans: ${openSans.style.fontFamily};
                }
            `}</style>

			<Header></Header>
			{/* <LenisScroll> */}
				<main className={openSans.className} id="main-element"> {children}</main>
			{/* </LenisScroll> */}
			<Footer></Footer>
		</>
	);
}