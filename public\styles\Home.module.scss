@import "variable", "base", "mixins/mixins";

.preregrina_sec{
  position: relative;
  z-index: 9;
}
.home_abt_sec{
  position: relative;
  .container{
    overflow: hidden;
  }
  @include max(992) {
    padding-top: 0;
  }
  @include max(767) {
    margin-top: -30px;
  }
  .container{
     max-width: 1300px;
     @include max(1600) {
        max-width: 1250px;
     }
     @include max(1250) {
      max-width: 90%;
   }
  }
  .abt_leaf_1{
    position: absolute;
    left: 0px;
    top: -338px;
    @include max(992) {
      top: -145px;
      width: 30%;
    }
    @include max(767) {
      width: 35%;
      top: 0;
    }
  }
  .abt_leaf{
    position: absolute;
    right: 0px;
    bottom: -30px;
    @include max(767) {
      width: 35%;
      bottom: 0;
    }
  }
}
.homeVideoSec{
    position: relative;
    .video_leaf{
      position: absolute;
      right: 0px;
      bottom: -115px;
      width: 35%;
      @include max(1060) {
        bottom: -115px;
        width: 25%;
      }
    }
}
:global(body.mob_menu) {
  .preregrina_sec{
    z-index: -1;
  }
}
:global(body.rtl) {
  .abt_leaf_1{
    left: auto;
    right: 0;
  }
  .home_abt_sec{
    .abt_leaf{
      right: auto;
      left: 0px;
    }
  }
  .homeVideoSec {
    .video_leaf{
      right: auto;
      left:115px;
      bottom: -70px;
      width: 25%;
      transform: rotate(78deg);
    }
    h2{
       margin-bottom: 30px;
    }
  }
}
