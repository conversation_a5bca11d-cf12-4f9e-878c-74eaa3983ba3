@import "@/public/styles/mixins/mixins";

.header {
    width: 100%;
    background: transparent;
    transition: all .5s ease-in-out;
    position: absolute;
    top: 0;
    left: 0;
    &::after{
        content: "";
        display: block;
        background: linear-gradient(
            180deg,
            rgba(60, 47, 47, 1) 0%,
            rgba(60, 47, 47, 0) 100%
        );
        height: 160%;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        transition: all .3s ease-in-out;
    }
    &.menuHidden {
        transform: translateY(-150px);
        @include max(992) {
            transform: initial;
        }
    }

    &.fixedHeader {
        position: fixed;
        top: 0;
        z-index: 1000;
        
    }

    &.sticky {
        box-shadow: 0px 1px 23px rgba(0, 0, 0, 0.22);
        background-color: var(--background);
        &::after{
            display: none;
        }
        .logo {
            width: 200px;

            @include max(992) {
                width: 220px;
            }

            @include max(767) {
                width: 150px;
            }
        }

        .submenu {
            top: 100px;

            @include max(992) {
                top: 0;
            }
        }

        .main_ul {
            padding: 0px 0 10px 0;
        }

        .header_social {
            svg {
                width: 12px !important;
                height: 12px !important;
            }
        }

        .top_header {
            .top_contact {
                li {
                    a {
                        font-size: size(12px);

                        @include max(992) {
                            font-size: size(15px);
                        }
                    }
                }
            }
        }

        .toggle_line {
            @include max(992) {
                top: 35px;
            }

            @include max(767) {
                top: 30px;
            }
        }
    }

    @include max(992) {
        padding: 10px 0;
    }

    .container {
        max-width: calc(100% - 60px);

        @include max(767) {
            max-width: calc(100% - 40px);
        }
    }

    .top_header {
        position: relative;
        z-index: 9;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        transition: all .3s ease-in-out;

        @include max(992) {
            justify-content: flex-start;
            align-items: center;

        }

        .header_social {
            display: flex;
            gap: 18px;
            padding-top: 10px;
            width: 15%;
            margin: 0;

            @include max(992) {
                width: auto;
                display: none;
            }

            svg {
                transition: all .3s ease-in-out;

                @include max(1600) {
                    width: 13px !important;
                    height: 13px !important;
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
                    width: 13px !important;
                    height: 13px !important;
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
                    width: 13px !important;
                    height: 13px !important;
                }
            }
        }

        .top_contact {
            display: flex;
            justify-content: flex-end;
            padding-top: 20px;
            width: 15%;

            @include max(1600) {
                padding-top: 18px;
            }

            @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
                padding-top: 15px;
            }

            @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
                padding-top: 15px;
            }

            @include max(992) {
                width: auto;
                padding: 0;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
            }

            li {
                border-right: 1px solid #FCE1CB;
                margin-right: 20px;
                padding-right: 20px;
                line-height: 11px;
                margin-bottom: 0;

                @include max(1600) {
                    margin-right: 18px;
                    padding-right: 18px;
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
                    margin-right: 18px;
                    padding-right: 18px;
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
                    margin-right: 18px;
                    padding-right: 18px;
                }

                @include max(992) {
                    display: none;
                }

                a,
                span {
                    font-size: size(14px);
                    color: #FCE1CB;
                    font-weight: 400;
                    white-space: nowrap;
                    letter-spacing: 1px;
                    transition: all .3s ease-in-out;

                    &:hover {
                        color: #eebb86;
                    }

                    @include max(1600) {
                        font-size: size(12px);
                    }

                    @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
                        font-size: size(12px);
                    }

                    @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
                        font-size: size(12px);
                    }

                    @include max(992) {
                        font-size: size(20px);
                    }
                }

                &:last-child {
                    border: none !important;
                    margin: 0 !important;
                    padding: 0 !important;

                    @include max(992) {
                        display: block;
                    }
                }
                &.active{
                    a{
                        color: #eebb86;
                    }
                }
            }
        }
    }

    .main_ul {
        display: flex;
        justify-content: center;
        gap: 28px;
        padding: 5px 0 15px 0;
        position: relative;
        z-index: 9;
        // max-width: 970px;
        transition: all .3s ease-in-out;

        @include max(1600) {
            padding: 8px 0 12px 0;
            gap: 26px;
        }

        @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
            padding: 5px 0 10px 0;
            gap: 25px;
        }

        @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
            padding: 5px 0 10px 0;
            gap: 25px;
        }

        @include max(992) {
            flex-wrap: wrap;
            padding: 0 !important;
            gap: 50px;
        }

        @include max(767) {
            gap: 35px;
        }

        li {
            margin-bottom: 0;
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;

            @include max(992) {
                width: 100%;
            }

            .menu_item {
                font-family: var(--font-obsolete);
                color: #FCE1CB;
                font-size: size(15px);
                font-weight: 300;
                transition: all .3s ease-in-out;
                cursor: pointer;

                @include max(1600) {
                    font-size: size(13.5px);
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
                    font-size: size(14px);
                }

                @media (max-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
                    font-size: size(14px);
                }

                @include max(1300) {
                    font-size: size(12px);
                }

                @include max(992) {
                    font-size: size(25px);
                }

                @include max(767) {
                    font-size: size(18px);
                }

                &:hover,&.active {
                    color: #eebb86;
                }
            }

            &:hover {
                svg {
                    fill: #eebb86 !important;
                }
            }
            .submenu_arw{
                &.active{
                    svg {
                        fill: #eebb86 !important;
                    }
                }
            }

            &.active {
                
                .submenu {
                    opacity: 1;
                    visibility: visible;
                    max-height: 400px;
                    // max-height: calc(50px + 60px * var(--submenu-items));
                    padding: 48px 0;
                }

                .submenu_contents {
                    height: 100%;

                }

                li {
                    opacity: 1;
                    transform: translateY(0);
                }

                h3 {
                    opacity: 1;
                    transform: translateY(0);
                }

                .submenu_img {
                    opacity: 1;
                    transform: translateY(0);
                    transition-delay: 0.4s;
                }

                .submenu_arw {
                    transform: rotate(180deg);
                }
            }

        }
    }

    .main_menu {
        display: flex;
        align-items: center;
        justify-content: center;

        @include max(992) {
            height: 0;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transition: all .8s ease-in-out;
            align-items: flex-start;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #34241d;
            padding: 200px 30px 0 30px;
        }

        @include max(767) {
            padding-top: 150px;
        }

        .header_social,
        .top_contact {
            @include max(992) {
                display: flex;
                gap: 15px;
            }
        }

        .header_social {
            li {
                margin-bottom: 0;

                svg {
                    width: 15px !important;
                    height: 15px !important;
                }
            }
        }

        .top_contact {
            li {
                margin-bottom: 0;

                a {
                    color: #FCE1CB;

                    @include max(992) {
                        font-size: size(14px);
                    }

                    @include max(767) {
                        font-size: size(12px);
                    }
                }
                
            }
        }

        .menunav_links {
            @include max(992) {
                position: absolute;
                width: 100%;
                left: 0;
                bottom: 0;
                padding: 30px;
                display: flex;
                justify-content: space-between;
                transition: all 1s ease-in-out;
                opacity: 0;
                visibility: hidden;
            }
        }

    }

    .logo {
        text-align: center;
        display: block;
        width: 303px;
        transition: all 1s ease-in-out;

        img {

            transition: all 1s ease-in-out;
        }

        @include max(1600) {
            width: 270px;
        }

        @media (min-width: 1200px) and (-webkit-device-pixel-ratio: 1.50) {
            width: 210px;
        }

        @media (min-width: 1200px) and (-webkit-device-pixel-ratio: 1.25) {
            width: 210px;
        }

        @include max(992) {
            order: 2;
            margin: 0 auto;
        }

        @include max(767) {
            width: 170px;
        }
    }

    .submenu {
        position: fixed;
        background-image: url(/images/header_bg.jpg);
        background-color: #fef5ec;
        background-repeat: no-repeat;
        background-size: cover;
        padding: 0;
        min-width: 100%;
        top: 144px;
        left: 0;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
        width: 100%;
        border-radius: 0px 0px 20px 20px;
        overflow: hidden;
        max-width: 970px;
        margin: 0 auto;
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 0;
        max-height: 0;
        transition: all 0.8s ease-in-out;
        box-shadow: 0px 1px 23px rgb(0 0 0 / 22%);

        // @include max(1450) {
        @include max(1550) {
            top: 133px;
        }

        @include max(992) {
            top: 0;
            max-height: fit-content;
            transform: translateX(100%);
            height: 100vh !important;
            max-height: 100vh !important;
            background-image: url(/images/slider_bg.jpg);
            background-repeat: repeat;
            background-size: contain;
            padding: 155px 30px 0 30px !important;
            border-radius: 0;
            z-index: 9;
        }

        @include max(992) {
            padding: 125px 30px 0 30px !important;
        }

        li {
            margin-bottom: 16px;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;

            @include max(992) {
                margin-bottom: 20px;
            }

            a {
                color: #7F4930;
                white-space: nowrap;
                font-family: var(--font-obsolete);
                font-size: size(16px);

                @include max(1300) {
                    font-size: size(14px);
                }

                @include max(992) {
                    font-size: size(20px);
                }

                @include max(767) {
                    font-size: size(15px);
                    white-space: initial;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

    }

    .submenu_arw {
        margin-left: 10px;
        margin-top: 3px;
        transition: all .3s ease-in-out;

        @include max(1600) {
            margin-left: 8px;
            margin-top: 2px;
        }

        @include max(992) {
            transform: rotate(-90deg);
            margin-left: 15px;
        }

        svg {
            @include max(992) {
                width: 20px !important;
                height: 20px !important;
            }

            @include max(767) {
                width: 15px !important;
                height: 15px !important;
            }
        }
    }

    .submenu_contents {
        display: flex;
        justify-content: space-between;
        column-gap: 100px;
        max-width: 970px;
        margin: 0 auto;
        width: 100%;
        height: 0;
        transition: height 0.8s ease-in-out;

        @include max(1300) {
            max-width: 810px;
        }

        @include max(992) {
            flex-wrap: wrap;
            flex-direction: column;
            justify-content: flex-start;
            gap: 30px;
            height: 100%;
        }

        h3 {
            color: #7F4930;
            font-size: size(38px);
            font-weight: 300;
            margin-bottom: 25px;
            opacity: 0;
            transform: translateY(-30px);
            transition: opacity 1s ease-out, transform 1s ease-out;

            @include max(1300) {
                font-size: size(32px);
                margin-bottom: 20px;
            }

            @include max(767) {
                font-size: size(30px);
            }
        }
    }

    .submenu_img {
        border-radius: 16px;
        overflow: hidden;
        opacity: 0;
        transform: translateY(-30px);
        transition: opacity 1s ease-out, transform 1s ease-out;
        max-width: 458px;

        img {
            display: block;
        }

        @include max(1300) {
            max-width: 400px;
        }
    }
}

@for $i from 1 through 10 {
    .submenu_list li:nth-child(#{$i}) {
        transition-delay: #{$i * 0.2s}; // Stagger delay for each item
    }
}

.toggle_line_new {
    width: 45px;
    height: auto;
    aspect-ratio: 1/1;
    background-color: #FCE1CB;
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: center;
    // gap: 5px;
    // align-items: center;
    // flex-direction: column;
    position: absolute;
    right: 20px;
    // top: 50px;
    top: 25px;
    cursor: pointer;
    border-radius: 4px;
    z-index: 99;
    transition: .5s ease-in-out;

    @include min(993) {
        display: none;
    }

    @include max(767) {
        top: 20px;
        width: 35px;

    }

    span {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: 2px;
        background-color: #34241D;
        display: block;
        // position: relative;
        transition: .5s ease-in-out;

        &:nth-child(1) {
            top: 30%;
        }

        &:nth-child(2) {
            width: 30%;
        }

        &:nth-child(3) {
            top: 70%;


        }


    }


}

:global(body.mob_menu) {

    .toggle_line_new {
        span {
            top: 50%;

            &:nth-child(1) {
                transform: translate(-50%, -50%) rotate(45deg);

            }

            &:nth-child(2) {
                width: 0%;
                opacity: 0;

            }

            &:nth-child(3) {
                transform: translate(-50%, -50%) rotate(-45deg);

            }
        }
    }
}


.toggle_line {
    width: 25px;
    height: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    flex-direction: column;
    position: absolute;
    right: 20px;
    top: 50px;
    cursor: pointer;
    z-index: 99;
    transition: .5s ease-in-out;

    @include min(993) {
        display: none;
    }

    @include max(767) {
        top: 32px;
    }

    span {
        width: 100%;
        height: 2px;
        background-color: #FCE1CB;
        display: block;
        position: relative;
        transition:all .5s ease-in-out;
    }
}

:global(body.menu-open) {
    .header {
        position: fixed;
        top: 0;
        z-index: 100;
    }
}

:global(body.mob_menu) {
    .header {
        position: fixed;
        top: 0;
        z-index: 9;

        .main_menu {
            .menunav_links {
                opacity: 1;
                visibility: visible;
            }
        }
    }

    .main_menu {
        @media (max-width: 992px) {
            height: 100vh;
            overflow: hidden;
            opacity: 1;
            visibility: visible;
        }
    }

    .main_ul {
        li {
            .backButton {
                svg {
                    fill: #fff !important;
                    transform: rotate(180deg);
                }
            }
        }
    }

    .toggle_line {
        span {

            &:nth-child(1) {
                // transform: rotate(45deg);
                transform: rotate(45deg) translateY(5px);
                // top: 8px;
            }

            &:nth-child(2) {
                transform: rotate(130deg);
            }
        }
    }

}

:global(body.mob_menu.menu-open) {
    .toggle_line {
        span {
            background-color: #804b32;
        }
    }

    .top_contact {
        li {
            a {
                color: #804b32;
            }

        }
    }

    .main_ul {
        li.active {
            .submenu {
                @media (max-width: 992px) {
                    transform: initial;
                }
            }
        }
    }
}

.backButton {
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
    border-radius: 30px;
    width: 170px;
    justify-content: space-between;
    padding: 0 60px 0 0;
    height: 50px;
    font-size: size(16px);
    color: #7F4930;
    line-height: 1;

    span {
        background-color: var(--hover-color);
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 2px;

        @media (max-width: 767px) {
            width: 43px;
            height: 43px;
        }
    }

    @media (max-width: 767px) {
        width: 150px;
        padding: 0 40px 0 0;
        height: 45px;
        font-size: size(15px);
    }
}

:global(html.html_loader) {
    .header {
        transition: all 2.5s ease-in-out;
        transform: translateY(-100%);
    }
}

:global(body.video_active) {
    .header {
        transform: initial;
    }
}

:global(body.rtl) {
    .header {
        .top_header {
            .top_contact {
                li {
                    border-right: none;
                    margin-right: 0;
                    padding-right: 0;
                    border-left: 1px solid #FCE1CB;
                    margin-left: 20px;
                    padding-left: 20px;
                }
            }
        }

        .submenu_arw {
            margin-left: 0;
            margin-right: 10px;
        }
    }
}