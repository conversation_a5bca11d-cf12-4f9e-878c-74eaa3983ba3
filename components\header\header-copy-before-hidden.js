import React, { useEffect, useRef, useState } from 'react';
import styles from './header.module.scss';
import Link from 'next/link';
import Image from 'next/image';
import Icons from '@/public/Icons';
import Lenis from '@studio-freight/lenis';
import { useRouter } from 'next/router';

const menuItems = [
    {
        title: 'About Us',
        customLink: '/about',
        submenuTitleLink: '/about',
        submenu: [
            { title: 'Our Story', href: '/about' },
            { title: 'Our Purpose', href: '/about#our-purpose' },
            { title: 'Leadership Team', href: '/about#leadership-team' },
            { title: 'Partners', href: '/about#partners' },
            { title: 'Accreditation and Certification', href: '/about#accreditation-and-certification' },
        ],
        image: "/images/header_img.jpg",
    },
    {
        title: 'The Peregrina Tree',
        customLink: '/the-peregrina-tree',
        image: "/images/header_img_2.jpg",
    },
    {
        title: 'Supply Chain',
        customLink: '/supply-chain',
        submenuTitleLink: '/supply-chain',
        submenu: [
            { title: 'Our Standards', href: '/supply-chain#our-standards' },
            { title: 'The Farmer Community', href: '/supply-chain#the-farmer-community' },
            { title: 'AlUla Peregrina Center', href: '/supply-chain#peregrina-center' },

        ],
        image: "/images/supply-chain-img.jpg",
    },
    {
        title: 'Our Actives',
        customLink: '/our-actives',
        submenuTitleLink: '/our-actives',
        submenu: [
            { title: 'Virgin AlUla Peregrina™ Oil', href: '/our-actives#virgin-alUla-peregrina-oil' },
            { title: 'Lipophilic AlUla Peregrina™ Extract', href: '/our-actives#lipophilic-alUla-peregrina-extract' },
            { title: 'Hydrolyzed AlUla Peregrina™ Extract', href: '/our-actives#hydrolyzed-alUla-peregrina-extract' },
            { title: 'Hydrolyzed AlUla Peregrina™ Paste Extract', href: '/our-actives#hydrolyzed-alUla-peregrina-paste-extract' },
        ],
        image: "/images/our-actives_menu.jpg",
    },
    {
        title: 'Hotels and Spas',
        customLink: '/hotels-and-spas',
        submenuTitleLink: '/hotels-and-spas',
        submenu: [
            { title: 'Hotel Toiletries', href: '/hotels-and-spas#hotel-toiletries' },
            { title: 'Spa Solutions', href: '/hotels-and-spas#spa-solutions' },
        ],
        image: "/images/hotels-and-spas-menu.jpg",
    },
    {
        title: 'Sustainability',
        customLink: '/sustainability',
        submenuTitleLink: '/sustainability',
        submenu: [
            { title: 'Environmental Responsibility', href: '/sustainability#environmental-responsibility' },
            { title: 'Social Impact', href: '/sustainability#social-impact' },
            { title: 'Our Commitment', href: '/sustainability#our-commitment' },
        ],
        image: "/images/sustainability_menu.jpg",
    },
];

const Header = () => {
    const [activeMenu, setActiveMenu] = useState(null);
    const menuRef = useRef(null);
    const [isSticky, setIsSticky] = useState(false);
    const headerRef = useRef(null);
    const router = useRouter();
    const isHomePage = router.pathname === '/';
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const { locale } = useRouter();
    const switchLocale = locale === "en" ? "ar" : "en";
    const { asPath } = useRouter();
    const [isScrolling, setIsScrolling] = useState(false);
    const [isReviewing, setIsReviewing] = useState(false);
    const scrollTimeout = useRef(null);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 992);
        };

        checkMobile(); // Initial check
        window.addEventListener("resize", checkMobile); // Listen for window resize

        return () => window.removeEventListener("resize", checkMobile);
    }, []);

    const handleToggleMenu = () => {
        setIsMenuOpen((prev) => !prev);
        if (!isMenuOpen) {
            document.body.classList.add('mob_menu');
        } else {
            document.body.classList.remove('mob_menu');
        }
    };

    useEffect(() => {
        const lenis = new Lenis({
            duration: 1.5,
            easing: (t) => 1 - Math.pow(1 - t, 4),
            smooth: true,
            smoothTouch: true,
        });

        const raf = (time) => {
            lenis.raf(time);
            requestAnimationFrame(raf);
        };
        requestAnimationFrame(raf);

        return () => {
            if (lenis) {
                lenis.destroy();
            }
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setActiveMenu(null);
                document.body.classList.remove('menu-open');
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleMenuClick = (index) => {
        if (activeMenu === index) {
            setActiveMenu(null);
            document.body.classList.remove('menu-open');
        } else {
            setActiveMenu(index);
            document.body.classList.add('menu-open');
        }

        const submenuLength = menuItems[index].submenu ? menuItems[index].submenu.length : 0;
        document.documentElement.style.setProperty('--submenu-items', submenuLength);
    };

    useEffect(() => {
        let lastScrollY = window.scrollY;

        const handleScroll = () => {
            setIsReviewing(false);
            if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
            scrollTimeout.current = setTimeout(() => {
                setIsReviewing(true);
            }, 200); // 200ms after scroll stops, switch to reviewing
            lastScrollY = window.scrollY;
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
            if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
        };
    }, []);

    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 100) {
                setIsSticky(true);
            } else {
                setIsSticky(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <header
            ref={headerRef}
            className={`${styles.header} header_main ${styles.fixedHeader} ${isSticky ? styles.sticky : ''} `}>
            <div className={`${styles.container} container`}>
                <div className={styles.top_header}>
                    <ul className={styles.header_social}>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Instagram" /></Link></li>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="twitter" /></Link></li>
                        <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Facebook" /></Link></li>
                    </ul>
                    <Link href='/' className={`${styles.logo} logo_main`}>
                        <Image src='/images/main_logo.png' width={303} height={100} alt='logo' />
                    </Link>
                    <ul className={styles.top_contact}>
                        <li><Link href='/news' >News</Link></li>
                        <li><Link href='/contact'>Contact Us</Link></li>
                        {/* <li><span onClick={() =>
								window.location.replace(
									switchLocale === "ar" ? `/ar${asPath}` : asPath
								)
							}  >عربي</span></li> */}
                        <li><Link href={"#."}>عربي</Link></li>
                    </ul>
                </div>
                <div className={styles.main_header}>
                    <div className={styles.toggle_line} onClick={handleToggleMenu}>
                        <span></span>
                        <span></span>
                    </div>
                    <div className={styles.main_menu} ref={menuRef}>
                        <ul className={styles.main_ul}>
                            {menuItems.map((item, index) => (
                                <li key={index} className={`${item.submenu ? styles.has_submenu : ''} ${activeMenu === index ? styles.active : ''}`}>
                                    {item.submenu ? (
                                        <>
                                            <span className={styles.menu_item} onClick={() => handleMenuClick(index)}>
                                                {item.title}
                                            </span>
                                            <span className={styles.submenu_arw} onClick={() => handleMenuClick(index)}>
                                                <Icons size={13} color="#FCE1CB" icon="down-arw" />
                                            </span>
                                        </>
                                    ) : (
                                        <>
                                            <Link
                                                href={item.customLink}
                                                className={styles.menu_item}
                                                onClick={() => {
                                                    setActiveMenu(null);
                                                    document.body.classList.remove('menu-open');
                                                    if (isMobile) {
                                                        document.body.classList.remove('mob_menu');
                                                        setIsMenuOpen(false);
                                                    }
                                                }}
                                            >
                                                {item.title}
                                            </Link>
                                        </>
                                    )}

                                    {item.submenu && (
                                        <div className={styles.submenu}>
                                            <div className={styles.submenu_contents}>
                                                {/* Show Back Button only on mobile */}
                                                {isMobile && (
                                                    <div className={styles.backButton} onClick={() => {
                                                        setActiveMenu(null);
                                                        document.body.classList.remove('menu-open');
                                                    }}>
                                                        <span><Icons size={25} color="#fff" icon='Right-arw' /></span> Back
                                                    </div>
                                                )}

                                                <div className={styles.submenu_list}>
                                                    {item.submenuTitleLink ? (
                                                        <Link
                                                            href={item.submenuTitleLink}
                                                            onClick={() => {
                                                                setActiveMenu(null);
                                                                document.body.classList.remove('menu-open');
                                                                if (isMobile) {
                                                                    document.body.classList.remove('mob_menu');
                                                                    setIsMenuOpen(false);
                                                                }
                                                            }}
                                                        >
                                                            <h3 className={styles.submenu_title_link}>{item.title}</h3>
                                                        </Link>
                                                    ) : (
                                                        <h3>{item.title}</h3>
                                                    )}
                                                    <ul>
                                                        {item.submenu.map((subItem, subIndex) => (
                                                            <li key={subIndex} onClick={() => {
                                                                setActiveMenu(null);
                                                                document.body.classList.remove('menu-open');
                                                                if (isMobile) {
                                                                    document.body.classList.remove('mob_menu');
                                                                    setIsMenuOpen(false);
                                                                }
                                                            }}>
                                                                <Link href={subItem.href}>{subItem.title}</Link>
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                                {item.image && (
                                                    <div className={styles.submenu_img}>
                                                        <Image src={item.image} width={458} height={229} alt={item.title} />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}


                                </li>
                            ))}

                        </ul>
                        {isMobile && (
                            <div className={styles.menunav_links}>
                                <ul className={styles.header_social}>
                                    <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Instagram" /></Link></li>
                                    <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="twitter" /></Link></li>
                                    <li><Link href='#.' target="_blank"><Icons size={15} color="#FCE1CB" icon="Facebook" /></Link></li>
                                </ul>
                                <ul className={styles.top_contact}>
                                    <li><Link href='/news' onClick={() => { document.body.classList.remove('mob_menu'); setIsMenuOpen(false); }}>News</Link></li>
                                    <li><Link href='/contact' onClick={() => { document.body.classList.remove('mob_menu'); setIsMenuOpen(false); }}>Contact Us</Link></li>
                                </ul>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
