@import "@/public/styles/mixins/mixins";
 

.two_cl_img_block{
    border-radius: 25px; overflow: hidden;
    @include max(767) {
       border-radius: 10px;
    }
    img{ display: block; width: 100%;transition: all 0.3s ease-in-out;}
 }

 .two_cl_card_main{ width: 100%;
   &:hover{
    .two_cl_img_block{
      img{
         transform: scale(1.05);
      }
    }
   }


}

 .two_cl_txt_block{ 
    margin-top: 15px;

    h3{ color: #34241D;  font-size: size(26px); margin-bottom: 15px; font-weight:600;
   @include max(1300) {
      font-size: size(22px);
      margin-bottom: 10px;
   }
    @include max(767) {
        font-size: size(23px); margin-bottom: 10px;
     }   
   }
    p{ color: #34241D; font-size: size(18px); font-weight: 400;
      @include max(1550) {
         font-size: size(16px);
      }
      @include max(1300) {
         font-size: size(15px);
      }
      @include max(992) {
         font-size: size(16px);
      }
      @include max(767) {
         font-size: size(15px);
      }
   }
 }