import React, { useEffect, useRef, useState } from 'react';
import styles from './header.module.scss';
import Link from 'next/link';
import Image from 'next/image';
import Icons from '@/public/Icons';
import Lenis from '@studio-freight/lenis';
import { useRouter } from "next/router";
import { getThemeoptions } from "@/utils/lib/server/publicServices";
import parse from "html-react-parser";

const Header = () => {
    const [activeMenu, setActiveMenu] = useState(null);
    const menuRef = useRef(null);
    const [isSticky, setIsSticky] = useState(false);
    const [isMenuHidden, setIsMenuHidden] = useState(false); // New state for hiding menu on idle
    const headerRef = useRef(null);
    const router = useRouter();
    const isHomePage = router.pathname === '/';
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const { locale } = useRouter();
    const switchLocale = locale === "en" ? "ar" : "en";
    const { asPath } = useRouter();
    const [isScrolling, setIsScrolling] = useState(false);
    const [isReviewing, setIsReviewing] = useState(false);
    const scrollTimeout = useRef(null);
    const idleTimeout = useRef(null); // New ref for idle timeout
    const [isHoveringHeader, setIsHoveringHeader] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 992);
        };

        checkMobile(); // Initial check
        window.addEventListener("resize", checkMobile); // Listen for window resize

        return () => window.removeEventListener("resize", checkMobile);
    }, []);

    const handleToggleMenu = () => {
        setIsMenuOpen((prev) => !prev);
        if (!isMenuOpen) {
            document.body.classList.add('mob_menu');
        } else {
            document.body.classList.remove('mob_menu');
        }
        setIsMenuHidden(false);
    };

    useEffect(() => {
        const lenis = new Lenis({
            duration: 1.5,
            easing: (t) => 1 - Math.pow(1 - t, 4),
            smooth: true,
            smoothTouch: true,
        });

        const raf = (time) => {
            lenis.raf(time);
            requestAnimationFrame(raf);
        };
        requestAnimationFrame(raf);

        return () => {
            if (lenis) {
                lenis.destroy();
            }
        };
    }, []);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setActiveMenu(null);
                document.body.classList.remove('menu-open');
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    const handleMenuClick = (index) => {
        if (activeMenu === index) {
            setActiveMenu(null);
            document.body.classList.remove('menu-open');
        } else {
            setActiveMenu(index);
            document.body.classList.add('menu-open');
        }

        const submenuLength = headeroption?.main_menu[index].sub_menu ? headeroption?.main_menu[index].sub_menu.length : 0;
        document.documentElement.style.setProperty('--submenu-items', submenuLength);
    };

    useEffect(() => {
        let lastScrollY = window.scrollY;

        const handleScroll = () => {
            setIsReviewing(false);
            if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
            scrollTimeout.current = setTimeout(() => {
                setIsReviewing(true);
            }, 200); // 200ms after scroll stops, switch to reviewing
            lastScrollY = window.scrollY;
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
            if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
        };
    }, []);

    // Updated useEffect for sticky behavior (removed delay)
    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 100) {
                setIsSticky(true);
            } else {
                setIsSticky(false);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // useEffect for idle detection and menu hiding
    useEffect(() => {
        const resetIdleTimer = () => {
            setIsMenuHidden(false);
            if (idleTimeout.current) {
                clearTimeout(idleTimeout.current);
            }

            // Only set the timer if not hovering over header
            if (!isHoveringHeader) {
                idleTimeout.current = setTimeout(() => {
                    setIsMenuHidden(true);
                }, 1500); // Increased timeout to 1.5 seconds
            }
        };

        const handleUserActivity = () => {
            resetIdleTimer();
        };

        // Events to track user activity - added more events
        const events = [
            'mousedown',
            'mousemove',
            'keypress',
            'scroll',
            'touchstart',
            'click'
        ];

        // Add event listeners for user activity
        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        // Start the idle timer initially
        resetIdleTimer();

        return () => {
            // Clean up event listeners and timeout
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (idleTimeout.current) {
                clearTimeout(idleTimeout.current);
            }
        };
    }, [isHoveringHeader]);

    // Handle hover state changes - clear timeout when hovering
    useEffect(() => {
        if (isHoveringHeader) {
            setIsMenuHidden(false);
            if (idleTimeout.current) {
                clearTimeout(idleTimeout.current);
            }
        }
    }, [isHoveringHeader]);

    const targetLocale = router.locale === "en" ? "ar" : "en";
    const [headeroption, setOptions] = useState(null);
    
        useEffect(() => {
      
          const fetchMyAcfOptions = async (locale) => {
            try {
              const FooterPostsData = await getThemeoptions(locale);
              //  console.log("header options:", router.locale);
              setOptions(FooterPostsData);
            } catch (error) {
              console.error("Error fetching options:", error);
            }
          };
          fetchMyAcfOptions(router.locale);
      
        }, [router]);
      
        if (!headeroption) {
          return null;
      }

    return (
        <header
            ref={headerRef}
            onMouseEnter={() => setIsHoveringHeader(true)}
            onMouseLeave={() => setIsHoveringHeader(false)}
            className={`${styles.header} header_main ${styles.fixedHeader} ${isSticky ? styles.sticky : ''} ${
                // Only apply menuHidden class when ALL these conditions are true:
                // 1. Menu should be hidden (based on idle timer)
                // 2. Header is sticky
                // 3. No active dropdown menu
                // 4. Mobile menu is not open
                // 5. User is NOT hovering over the header
                isMenuHidden && isSticky && !activeMenu && !isMenuOpen && !isHoveringHeader ? styles.menuHidden : ''
            }`}>
            <div className={`${styles.container} container`}>
                <div className={styles.top_header}>
                    {headeroption?.social_media && 
                        headeroption?.social_media.length > 0 && (
                        <ul className={styles.header_social}>            
                            {headeroption?.social_media.map((social_media, sindex) => (
                                <li key={sindex}>
                                <Link href={social_media?.social_link} target="_blank">
                                    <Icons size={15} color="#FCE1CB" icon={social_media?.social_icon} />
                                </Link>
                                </li>
                            ))}             
                        </ul>
                    )}
                    {headeroption?.header_logo && (
                        <Link href='/' className={`${styles.logo} logo_main`}>
                            <Image src={headeroption?.header_logo?.url} width={303} height={100} alt='logo' />
                        </Link>
                    )}
                    {(headeroption?.topbar_menu || headeroption?.language_name) && (
                        <ul className={styles.top_contact}>
                            {headeroption?.topbar_menu &&
                                headeroption?.topbar_menu?.length > 0 &&
                                headeroption?.topbar_menu.map((link, liindex) => (
                                    <li key={liindex} className={asPath === link?.top_menu?.url ? styles.active : ''}>
                                        <Link href={link?.top_menu?.url}>{link?.top_menu?.title && parse(link?.top_menu?.title)}</Link>
                                    </li>
                                ))}
                            {headeroption?.language_name &&
                                <li>
                                    <Link
                                    href={asPath}
                                    locale={targetLocale}>
                                        {parse(headeroption?.language_name)}
                                    </Link>
                                </li>
                            }
                        </ul>
                    )}
                </div>
                <div className={styles.main_header}>
                    {/* <div className={styles.toggle_line} onClick={handleToggleMenu}>
                        <span></span>
                        <span></span>
                    </div> */}
                    <div className={styles.toggle_line_new} onClick={handleToggleMenu}>
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    {headeroption?.main_menu &&
                        headeroption?.main_menu?.length > 0 && (
                    <div className={styles.main_menu} ref={menuRef}>
                        
                        <ul className={styles.main_ul}>
                            {headeroption?.main_menu.map((item, index) => {
                                // Determine if this menu or any of its submenu items is active
                                const isMenuActive =
                                    asPath === item?.main_nav?.url ||
                                    (item?.sub_menu && item?.sub_menu.some(sub => asPath === sub.href));
                                return (
                                    <li key={index} className={`${item.submenu ? styles.has_submenu : ''} ${activeMenu === index ? styles.active : ''}`}>
                                        {item?.is_dropdown === "dropdown_yes" ? (
                                            <>
                                                <span className={`${styles.menu_item} ${isMenuActive ? styles.active : ''}`} onClick={() => handleMenuClick(index)}>
                                                    {item?.main_nav?.title && parse(item?.main_nav?.title)}
                                                </span>
                                                <span className={`${styles.submenu_arw} ${isMenuActive ? styles.active : ''}`} onClick={() => handleMenuClick(index)}>
                                                    <Icons size={13} color="#FCE1CB" icon="down-arw" />
                                                </span>
                                            </>
                                        ) : (
                                            <>
                                                {item?.main_nav && (                                                        
                                                    <Link
                                                        href={item?.main_nav?.url}
                                                        className={`${styles.menu_item} ${asPath === item?.main_nav?.url ? styles.active : ''}`}
                                                        onClick={() => {
                                                            setActiveMenu(null);
                                                            document.body.classList.remove('menu-open');
                                                            if (isMobile) {
                                                                document.body.classList.remove('mob_menu');
                                                                setIsMenuOpen(false);
                                                            }
                                                        }}
                                                    >
                                                        {item?.main_nav?.title && parse(item?.main_nav?.title)}
                                                    </Link>
                                                )}
                                            </>
                                        )}

                                        {item.is_dropdown === "dropdown_yes" && (
                                            <div className={styles.submenu}>
                                                <div className={styles.submenu_contents}>
                                                    {/* Show Back Button only on mobile */}
                                                    {isMobile && (
                                                        <div className={styles.backButton} onClick={() => {
                                                            setActiveMenu(null);
                                                            document.body.classList.remove('menu-open');
                                                        }}>
                                                            <span><Icons size={25} color="#fff" icon='Right-arw' /></span> 
                                                            {router.locale === "en" ? "Back" : "عودة"}
                                                        </div>
                                                    )}

                                                    <div className={styles.submenu_list}>
                                                        {item?.main_nav?.url ? (
                                                            <Link
                                                                href={item?.main_nav?.url}
                                                                onClick={() => {
                                                                    setActiveMenu(null);
                                                                    document.body.classList.remove('menu-open');
                                                                    if (isMobile) {
                                                                        document.body.classList.remove('mob_menu');
                                                                        setIsMenuOpen(false);
                                                                    }
                                                                }}
                                                            >
                                                                <h3 className={styles.submenu_title_link}>
                                                                    {item?.main_nav?.title && parse(item?.main_nav?.title)}
                                                                </h3>
                                                            </Link>
                                                        ) : (
                                                            <h3>{item?.main_nav?.title && parse(item?.main_nav?.title)}</h3>
                                                        )}
                                                        {item?.sub_menu && item?.sub_menu?.length > 0 && (
                                                            <ul>
                                                                {item.sub_menu.map((subItem, subIndex) => (
                                                                    subItem?.sub_nav && (
                                                                    <li key={subIndex}
                                                                        className={asPath === subItem.sub_nav?.url ? styles.active : ''}
                                                                        onClick={() => {
                                                                            setActiveMenu(null);
                                                                            document.body.classList.remove('menu-open');
                                                                            if (isMobile) {
                                                                                document.body.classList.remove('mob_menu');
                                                                                setIsMenuOpen(false);
                                                                            }
                                                                        }}
                                                                    >
                                                                       
                                                                            <Link href={subItem?.sub_nav?.url}>
                                                                                {subItem?.sub_nav?.title && parse(subItem?.sub_nav?.title)}
                                                                            </Link>
                                                                    </li>
                                                                    )
                                                                ))}
                                                            </ul>
                                                        )}
                                                    </div>
                                                    {item?.sub_menu_image && (
                                                        <div className={styles.submenu_img}>
                                                            <Image src={item?.sub_menu_image?.url} width={458} height={229} alt={item?.sub_menu_image?.alt} />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}


                                    </li>
                                );
                            })}

                        </ul>
                            {isMobile && (
                            <div className={styles.menunav_links}>
                                {headeroption?.social_media && 
                                headeroption?.social_media.length > 0 && (
                                    <ul className={styles.header_social}>            
                                        {headeroption?.social_media.map((social_media, sindex) => (
                                            <li key={sindex}>
                                            <Link href={social_media?.social_link} target="_blank">
                                                    <Icons size={15} color="#FCE1CB" icon={social_media?.social_icon} />
                                                    </Link>
                                            </li>
                                        ))}             
                                    </ul>
                                )}

                                {headeroption?.topbar_menu &&
                                headeroption?.topbar_menu?.length > 0 && (
                                    <ul className={styles.top_contact}>
                                        {headeroption?.topbar_menu.map((link, liindex) => (
                                            <li key={liindex} className={asPath === link?.top_menu?.url ? styles.active : ''}>
                                                <Link
                                                    href={link?.top_menu?.url}
                                                    onClick={() => { document.body.classList.remove('mob_menu'); setIsMenuOpen(false); }}
                                                >
                                                    {link?.top_menu?.title && parse(link?.top_menu?.title)}
                                                </Link>
                                            </li>
                                        ))}
                                    </ul>
                                )}
                                
                            </div>
                        )}
                     </div>
                  )}
                </div>
            </div>
        </header>
    );
};

export default Header;