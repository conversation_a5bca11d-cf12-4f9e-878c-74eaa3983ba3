"use client";
import React, { useEffect, useState, useRef } from "react";
import style from "./peregrina.module.scss";
import Image from "next/image";
import Link from "next/link";
import Icons from "@/public/Icons";

// Restore splitText for styled title
const splitText = ({ smallText, bigText }) => {
  return (
    <span>
      <span className={style.smallText}>
        {smallText.split("").map((char, i) => (
          <span key={`small-${i}`} className="char">{char}</span>
        ))}
      </span>
      <span className={style.bigText}>
        {bigText.split("").map((char, i) => (
          <span key={`big-${i}`} className="char">{char}</span>
        ))}
      </span>
    </span>
  );
};

const Page = () => {
  const [isRTL, setIsRTL] = useState(false);
  const text2Ref = useRef(null);
  const sectionRef = useRef(null);
  const img1Ref = useRef(null);
  const img2Ref = useRef(null);

  // Function to check RTL status
  const checkRTL = () => {
    return typeof document !== 'undefined' && document.body.classList.contains("rtl");
  };

  useEffect(() => {
    setIsRTL(checkRTL());
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          setIsRTL(checkRTL());
        }
      });
    });
    observer.observe(document.body, { attributes: true });
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    let gsap, ScrollTrigger, ctx;
    Promise.all([
      import("gsap"),
      import("gsap/ScrollTrigger")
    ]).then(([gsapMod, stMod]) => {
      gsap = gsapMod.default;
      ScrollTrigger = stMod.ScrollTrigger;
      gsap.registerPlugin(ScrollTrigger);
      ctx = gsap.context(() => {
        gsap.set([text2Ref.current, img1Ref.current, img2Ref.current], { opacity: 0 });
        gsap.set(text2Ref.current, { scale: 0.5 });
        gsap.set(img1Ref.current, { x: isRTL ? 100 : -100 });
        gsap.set(img2Ref.current, { x: isRTL ? -100 : 100 });
        gsap.to(text2Ref.current, {
          scale: 1,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            once: true
          }
        });
        gsap.to(img1Ref.current, {
          x: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            once: true
          }
        });
        gsap.to(img2Ref.current, {
          x: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power2.out",
          scrollTrigger: {
            trigger: sectionRef.current,
            start: "top 80%",
            once: true
          }
        });
      });
    });
    return () => ctx && ctx.revert && ctx.revert();
  }, [isRTL]);

  return (
    <>
      <section className={style.peri_tree} ref={sectionRef}>
        <div className={style.tree_wrap}>
          <div className={style.content}>
            <div className={style.text2} ref={text2Ref}>
              <h2 className={style.title}>
                {splitText({ smallText: "Uncover the secret of The Peregrina Tree ", bigText: "Powered with Local Communities" })}
              </h2>
              <Link href="/the-peregrina-tree" className="main_btn">
                <span className='button_text'>Learn More</span>
                <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
              </Link>
            </div>
             
          </div>
          <div className={style.images}>
            <figure ref={img1Ref}>
              <Image src="/images/tree.png" width={538} height={698} alt="tree" loading="lazy" />
            </figure>
            <figure ref={img2Ref}>
              <Image src="/images/people.png" width={342} height={412} alt="people" loading="lazy" />
            </figure>
          </div>
        </div>
      </section>
    </>
  );
};

export default Page;
