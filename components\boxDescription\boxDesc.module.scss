@import "@/public/styles/mixins/mixins";

.boxDesc {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  gap: 50px;

  @include max(992) {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
  }

  @include max(767) {
    gap: 20px;
  }

  .box_image {
    figure {
      border-radius: 20px;
      aspect-ratio: 626/563;

      // height: 470px;
      // @include max(1600) {
      //     height: 400px;
      // }
      @include max(1060) {
        height: 390px;
      }

      @include max(767) {
        height: 260px;
        border-radius: 12px;
        width: 100%;
      }

      img {
        object-fit: cover;
        object-position: top;
      }
    }
  }

  .box_contents {
    h2 {
      font-size: size(54px);
      line-height: size(70px);
      color: #fff;
      font-weight: 300;
      margin-bottom: 15px;
      padding-right: 20px;

      @include max(1550) {
        font-size: size(48px);
      }

      @include max(1300) {
        font-size: size(40px);
        line-height: size(55px);
      }

      @include max(1060) {
        font-size: size(38px);
        line-height: size(48px);
      }

      @include max(767) {
        font-size: size(25px);
        line-height: size(35px);
        padding-right: 0;
      }

      span {
        font-weight: 600;
      }
    }

    h4 {
      color: #ffffff;
      font-size: size(26px);
      line-height: size(36px);
      font-weight: 600;
      margin-bottom: 15px;

      @include max(1300) {
        font-size: size(20px);
        line-height: size(28px);
      }

      @include max(1060) {
        font-size: size(20px);
        line-height: size(28px);
        margin-bottom: 10px;
      }

      @include max(767) {
        font-size: size(16px);
        line-height: size(20px);
      }
    }

    h3 {
      font-size: size(28px);
      line-height: size(38px);
      font-weight: 700;
      color: #fff;
      margin-bottom: 15px;

      @include max(1300) {
        font-size: size(20px);
        line-height: size(30px);
      }

      @include max(1060) {
        font-size: size(25px);
        line-height: size(35px);
      }

      @include max(767) {
        font-size: size(18px);
        line-height: size(28px);
      }
    }

    p {
      color: #fff;
      margin-bottom: 25px;
      font-size: size(18px);
      line-height: size(29px);

      @include max(1300) {
        font-size: size(15px);
        line-height: size(25px);
      }

      @include max(1060) {
        margin-bottom: 15px;
        line-height: size(23px);
      }

      @include max(767) {
        margin-bottom: 12px;
        font-size: size(14px);
        line-height: size(25px);
      }
    }

    .button_sec {
      margin-top: 40px;
    }

    p {
      span {
        font-weight: 600;
        font-size: size(18px);
      }

      &+ {
        .button_sec {
          margin-top: 20px;
        }
      }
    }
  }

  &.right {
    .box_image {
      order: 2;

      @include max(767) {
        order: initial;
      }
    }
  }

  &.whiteBg {

    h2,
    p {
      color: #34241d;
    }
  }
}

.certificationList {
  display: flex;
  gap: 15px;
  margin-top: 50px;

  @include max(1060) {
    margin-top: 30px;
  }

  @include max(767) {
    gap: 10px;
  }

  li {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.download_btn {
  svg {
    transform: rotate(90deg);
  }
}

:global(body.rtl) {
  .boxDesc {
    .box_contents {
      h2 {
        padding-right: 0;
        padding-left: 20px;
      }
    }

    .download_btn {
      svg {
        transform: rotate(270deg);
      }
    }
  }
}

.desc_list {
  li {
    color: #ffffff;
    font-size: size(16px);
    padding-left: 20px;
    font-weight: 300;
    position: relative;
    margin-bottom: 10px;

    &::before {
      content: "";
      display: block;
      width: 5px;
      height: 5px;
      background-color: #fff;
      border-radius: 50%;
      position: absolute;
      left: 0;
      top: 10px;
    }
  }
}

.product_list_hidden {
  :global(.product_list_item) {
    height: 0px;
    opacity: 0;
    padding: 0;
    margin: 0;
    min-height: 0;
    overflow: hidden;
    color: transparent;
  }
}