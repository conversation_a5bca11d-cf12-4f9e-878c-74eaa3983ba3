import React, { useState, useEffect, useRef } from 'react';
import styles from './homeservice.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import Icons from '@/public/Icons';
import AOS from "aos";
import "aos/dist/aos.css";
import Parallax from "@/components/Paralax";
import AnimatedTitle from '@/components/anim';

const services = [
    {
        id: 1,
        title: 'Our Actives',
        desc:"Active oil and Extracts from Seeds to Skin & Hair",
        image: '/images/property1.jpg',
        link:"/our-actives"
    },
    {
        id: 2,
        title: 'Hotels & Spas Solutions',
        desc:"Natural hotel and Spa products deeply rooted in the desert of Arabia's tradition",
        image: '/images/property2.jpg',
        link:"/hotels-and-spas"
    },
];

const HomeServices = () => {
    const [hoveredIndex, setHoveredIndex] = useState(null);
    const [isHovered, setIsHovered] = useState(false);
    const serviceContainerRef = useRef(null);

    useEffect(() => {
        AOS.init({
            easing: "ease-out-cubic",
            once: false,
            offset: 50,
        });
    }, []);

    const handleMouseMove = (e) => {
        if (serviceContainerRef.current) {
            const containerWidth = serviceContainerRef.current.offsetWidth;
            const mouseX = e.clientX - serviceContainerRef.current.getBoundingClientRect().left;
            
            // Calculate the visible area for each section
            const firstSectionEnd = containerWidth * 0.5; // First section ends at 50%
            const secondSectionStart = containerWidth * 0.5; // Second section starts at 50%
            
            // Determine which section the mouse is over based on visible area
            let newHoveredIndex = null;
            
            if (mouseX <= firstSectionEnd) {
                // Mouse is in first section's visible area
                newHoveredIndex = 0;
            } else if (mouseX >= secondSectionStart) {
                // Mouse is in second section's visible area
                newHoveredIndex = 1;
            }
            
            setHoveredIndex(newHoveredIndex);
            setIsHovered(newHoveredIndex !== null);
        }
    };

    const handleMouseLeave = () => {
        setHoveredIndex(null);
        setIsHovered(false);
    };

    return (
        <div
            ref={serviceContainerRef}
            className={`${styles.homeService} ${isHovered ? styles.hovered : ''}`}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
        >
            <div className={styles.service_item}>
                {services.map((service, index) => (
                    <div
                        key={service.id}
                        className={`${styles.service_img} ${
                            hoveredIndex === index 
                                ? styles.active 
                                : hoveredIndex !== null 
                                    ? styles.inactive 
                                    : ''
                        }`}
                        style={hoveredIndex === index ? { zIndex: 10 } : hoveredIndex === null ? {} : { zIndex: 'initial' }}
                    >
                        <Parallax mediaSrc={service.image} altText={service.title} />

                        <div className={styles.service_texts}>
                            <div className={`${styles.text_cnts} ${
                                hoveredIndex === index 
                                    ? styles.active 
                                    : hoveredIndex !== null 
                                        ? styles.inactive 
                                        : ''
                            }`}>
                                {/* <AnimatedTitle title={service.title} tag="h4" /> */}
                                <h4>{service.title}</h4>
                                <div className={styles.hover_cnt}>
                                    <p>{service.desc}</p>
                                    <Link href={service.link} className="main_btn">
                                        <span className='button_text'>Learn More</span>
                                        <span className='arrow'><Icons size={25} color="#fff" icon='Right-arw' /></span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default HomeServices;
