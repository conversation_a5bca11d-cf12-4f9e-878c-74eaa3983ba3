import React, { useRef, useState, useEffect } from 'react';
import styles from './archaeological.module.scss';
import { Swiper, SwiperSlide } from 'swiper/react';
import Image from 'next/image';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/effect-fade';
import { Navigation, Autoplay, EffectFade } from 'swiper/modules';
import Icons from '@/public/Icons';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from "next/router";


const ArchaeologicalSlider = ({ slides = [] }) => {
    const router = useRouter();
    const direction = router.locale === "ar" || router.locale === "rtl" ? "rtl" : "ltr";
    const swiperRef = useRef(null);
    const [activeIndex, setActiveIndex] = useState(0);
    const [progress, setProgress] = useState(0);
    const [isBeginning, setIsBeginning] = useState(true);
    const [isEnd, setIsEnd] = useState(false);

    useEffect(() => {
        const swiper = swiperRef.current;
        if (swiper) {
            // Update navigation states when slide changes
            const updateState = () => {
                setActiveIndex(swiper.realIndex);
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
                updateProgress(swiper.realIndex);
            };

            swiper.on('slideChange', updateState);

            // Set initial states
            setIsBeginning(swiper.isBeginning);
            setIsEnd(swiper.isEnd);
        }
    }, []);

    const updateProgress = (index) => {
        const totalSlides = slides.length;
        const newProgress = ((index + 1) / totalSlides) * 100;
        setProgress(newProgress);
    };

    return (
        <div className={styles.slider_container}>
            <Swiper
                onSwiper={(swiper) => (swiperRef.current = swiper)}
                modules={[Navigation, Autoplay, EffectFade]}
                effect="fade"
                fadeEffect={{
                    crossFade: true
                }}
                speed={1200}
                dir={direction}
                className="archaeology_slider"
            >
                {slides.map((slide, index) => (
                    <SwiperSlide key={slide.id}>
                        <AnimatePresence mode="wait">
                            {activeIndex === index && (
                                <motion.div
                                    key={activeIndex}
                                    className={styles.slider_content}
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    exit={{ opacity: 0 }}
                                    transition={{ duration: 0.6, ease: 'easeInOut' }}
                                >
                                    {/* Left Section */}
                                    <motion.div
                                        className={styles.slider_left}
                                        style={{ backgroundImage: `url(/images/slider_bg.jpg)` }}
                                        // initial={{ x: '-100%', opacity: 0 }}
                                        // animate={{ x: 0, opacity: 1 }}
                                        // exit={{ x: '-100%', opacity: 0 }}
                                        // transition={{ duration: 1, ease: 'easeInOut' }}
                                    >
                                        <motion.div
                                            className={styles.slider_text}
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            exit={{ opacity: 0, y: 20 }}
                                            transition={{ delay: 0.3, duration: 0.8, ease: 'easeOut' }}
                                        >
                                             <h3>{slide.title}</h3>
                                             <div className={styles.slider_text_content}>
                                                <p>{slide.text}</p>
                                             </div>
                                        </motion.div>
                                    </motion.div>

                                    {/* Right Section */}
                                    <motion.div
                                        className={styles.slider_right}
                                        initial={{ x: '20%', opacity: 0 }}
                                        animate={{ x: 0, opacity: 1 }}
                                        exit={{ x: '80%', opacity: 0 }}
                                        transition={{ duration: 0.6, ease: 'easeInOut' }}
                                    >
                                        <Image src={slide.image} width={699} height={608} alt='image' />
                                    </motion.div>

                                    {/* Custom Navigation Buttons */}
                                    <motion.div
                                        className={styles.swiper_nav}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: 20 }}
                                        transition={{ delay: 1.5, duration: 0.8, ease: 'easeOut' }}
                                    >
                                        <div
                                            className={`${styles.prev_button} ${styles.btn_nav} ${isBeginning ? styles.disabled : ''}`}
                                            onClick={() => swiperRef.current?.slidePrev()}
                                        >
                                            <Icons size={38} color="#fff" icon="Right-arw" />
                                        </div>
                                        <div
                                            className={`${styles.next_button} ${styles.btn_nav} ${isEnd ? styles.disabled : ''}`}
                                            onClick={() => swiperRef.current?.slideNext()}
                                        >
                                            <Icons size={38} color="#fff" icon="Right-arw" />
                                        </div>
                                    </motion.div>

                                </motion.div>
                            )}
                        </AnimatePresence>
                    </SwiperSlide>
                ))}
            </Swiper>

            {/* Progress Bar with Slide Count */}
            <div className={styles.progress_container}>
                <div className={styles.progress_bar}>
                    <div className={styles.progress} style={{ width: `${progress}%` }}></div>
                </div>
                <div className={styles.slider_numbers}>
                    <span className={styles.slide_number}>{String(activeIndex + 1).padStart(2, '0')}</span>
                    <span className={styles.slide_number}>{String(slides.length).padStart(2, '0')}</span>
                </div>
            </div>
        </div>
    );
};

export default ArchaeologicalSlider;
