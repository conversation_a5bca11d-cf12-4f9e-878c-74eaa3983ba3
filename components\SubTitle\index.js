import React, { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import styles from "./subTitle.module.scss";
import Image from "next/image";

gsap.registerPlugin(ScrollTrigger);

const SubTitle = ({ title  }) => {
  const titleRefs = useRef([]);

  useEffect(() => {
    titleRefs.current.forEach((el, index) => {
      if (el) {
        gsap.fromTo(
          el,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            scrollTrigger: {
              trigger: el,
              start: "top 90%",
              toggleActions: "play none none reverse",
            },
            duration: 1,
            delay: index * 0.1,
            ease: "power3.out",
          }
        );
      }
    });
  }, [title]);

  return (
    <div className={styles.subtitle_wrapper}>
      
        <h2 className={styles.subtitle} >
          {title}
        </h2>
      
    </div>
  );
};

export default SubTitle;
