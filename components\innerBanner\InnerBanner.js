import React, { useEffect, useRef } from "react";
import styles from './banner.module.scss';
import { gsap } from "gsap";
import Image from "next/image";
import parse from "html-react-parser";

const InnerBanner = ({ 
    bannerBg, 
    title, 
    content,
    subImageUrl,
    showSubImage = true,
    isSmallText = false
}) => {
    const textRef = useRef(null);

    useEffect(() => {
        if (!textRef.current) return;

        const titleElement = textRef.current.querySelector("h1");

        gsap.fromTo(
            ".banner_img",
            { backgroundSize: "120%" },
            { backgroundSize: "100%", duration: 2, ease: "power2.out" }
        );

        gsap.fromTo(
            titleElement,
            { opacity: 0, y: 20 },
            {
                opacity: 1,
                y: 0,
                duration: 1.2,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: textRef.current,
                    start: "top 90%",
                    toggleActions: "play none none reset",
                },
            }
        );
    }, []);

    return (
        <div className={`${styles.inner_banner} innerBanner`}>
            <div className={`${styles.banner_img} banner_img`}>
                <Image src={bannerBg} alt="Banner Background" width={1600} height={901} quality={100} loading="lazy"/>
                <div className={`${styles.p_relative} container`}>
                    {showSubImage && subImageUrl && (
                        <div className={styles.banner_sub_img}>
                            <Image 
                                src={subImageUrl} 
                                alt="Banner Sub Image" 
                                width={423} 
                                height={209} 
                                quality={100} 
                                loading="lazy"
                            />
                        </div>
                    )}
                    <div className={styles.banner_text} ref={textRef}>
                        {title && (
                            <h1>{parse(title)}</h1>
                        )}                      
                        {content && (
                            <p className={isSmallText ? styles.small_txt : ''}>
                                {parse(content)}
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InnerBanner;
