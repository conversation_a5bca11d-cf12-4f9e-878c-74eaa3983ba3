"use client";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import styles from "./product.module.scss";
import ProductCard from "../cards/productCard";
import Icons from '@/public/Icons';

const productData = [
    { image: "/images/pro_1.jpg", title: "Virgin AlUla Peregrina™ Oil", buttonText: "Explore AlUla Peregrina™ Actives", buttonLink: "#." },
    { image: "/images/pro_2.jpg", title: "Lipophilic AlUla Peregrina™ Extract", buttonText: "Explore AlUla Peregrina™ Actives", buttonLink: "#." },
    { image: "/images/pro_3.jpg", title: "Hydrolyzed AlUla Peregrina™ Extract", buttonText: "Explore AlUla Peregrina™ Actives", buttonLink: "#." },
    { image: "/images/pro_4.jpg", title: "Hydrolyzed AlUla Peregrina™ Paste Extract", buttonText: "Explore AlUla Peregrina™ Actives", buttonLink: "#." }
];

export default function ProductSlider() {
    const [activeIndex, setActiveIndex] = useState(0);
    const [isRTL, setIsRTL] = useState(false);
    const totalSlides = productData.length;

    // Detect RTL mode on mount
    useEffect(() => {
        setIsRTL(document.body.classList.contains("rtl"));
    }, []);

    const nextSlide = () => {
        setActiveIndex((prev) => (isRTL ? (prev - 1 + totalSlides) % totalSlides : (prev + 1) % totalSlides));
    };

    const prevSlide = () => {
        setActiveIndex((prev) => (isRTL ? (prev + 1) % totalSlides : (prev - 1 + totalSlides) % totalSlides));
    };

    return (
        <div className={styles.sliderContainer}>
            <div className={styles.slider}>
                {productData.map((product, index) => {
                    const position = (index - activeIndex + totalSlides) % totalSlides;
                    const zIndex = totalSlides - position;

                    const scaleValues = [1, 0.8, 0.65, 0.45];
                    const translateXValues = [1, 200, 420, 790];

                    const scale = scaleValues[position] || 0.3;
                    const xOffset = translateXValues[position] || 1000;
                    const isActive = index === activeIndex;

                    return (
                        <div
                            key={index}
                            className={`${styles.slide} ${isActive ? styles.activeSlide : ""}`}
                            style={{
                                zIndex,
                                transform: `scale(${scale}) translateX(${isRTL ? -xOffset : xOffset}px)`,
                            }}
                        >
                            <ProductCard
                                image={product.image}
                                title={
                                    <motion.div
                                        key={activeIndex}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -20 }}
                                        transition={{ duration: 0.5 }}
                                    >
                                        {product.title}
                                    </motion.div>
                                }
                                buttonText={
                                    <motion.div
                                        key={activeIndex}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: -20 }}
                                        transition={{ duration: 0.5, delay: 0.2 }}
                                    >
                                        {product.buttonText}
                                    </motion.div>
                                }
                                buttonLink={product.buttonLink}
                            />
                        </div>
                    );
                })}
            </div>

            {/* Controls */}
            <div className={styles.sliderControls}>
                <div onClick={isRTL ? nextSlide : prevSlide} className={`${styles.controlButton} ${styles.prev}`}>
                    <Icons size={35} color="#fff" icon='Right-arw' />
                </div>
                <div onClick={isRTL ? prevSlide : nextSlide} className={`${styles.controlButton} ${styles.next}`}>
                    <Icons size={35} color="#fff" icon='Right-arw' />
                </div>
                <span className={styles.pagination}>
                    {activeIndex + 1}/{totalSlides}
                </span>
            </div>
        </div>
    );
}
