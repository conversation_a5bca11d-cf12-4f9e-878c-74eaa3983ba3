import React from 'react';
import Link from 'next/link';
import Icons from '@/public/Icons';
import styles from './brudcrumb.module.scss'

const Breadcumbs = ({ breadcrumps }) => {
    return (
        <>
            <div className='container'>
                <ul className={styles.breadcrumbs}>
                    {breadcrumps.map((item, index) => (
                        <li key={index}>
                            {
                                item.path ? <Link href={item.path}>
                                    {index === 0 ? (
                                        <Icons size={25} color="#FCE1CB" icon="home" />
                                    ) : (
                                        item.page
                                    )}
                                </Link>
                                    : <p>{item.page}</p>
                            }
                            <span><Icons size={13} color="#FCE1CB" icon="right-arw" /></span>
                        </li>
                    ))}
                </ul>
            </div>
        </>
    )
}

export default Breadcumbs