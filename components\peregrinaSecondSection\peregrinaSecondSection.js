import React, { useEffect, useRef } from "react";
import styles from './peregrinaSecondSection.module.scss';
import { gsap } from "gsap";
import Image from "next/image";
import Innovation from "../Innovation/Innovation";
import TwoClLayout from "../TwoClLayout";
import TwoClCard from "../TwoClLayout";

const PeregrinaSecondSection = ({ bannerBg, title,content }) => {
    const textRef = useRef(null); // Declare ref first

    // useEffect(() => {
       
    // }, []);

    return (
  <>


   <Innovation/>
     
  </>
    );
};

export default PeregrinaSecondSection;
