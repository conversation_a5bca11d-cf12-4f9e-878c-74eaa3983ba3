// components/ImageBg.js
import React, { useState, useRef, useEffect } from 'react';
import styles from './twocol.module.scss';
import Image from 'next/image';
import gsap from "gsap";
const items = [
    {
      title: "Blossoming",
      period: "From March to April",
      description:
        "Soft pink and white, the five-petal flowers bloom with a sweet, honeyed fragrance—signaling the tree's peak vitality.",
      image: "/images/image_1.jpg",
      icon: "/images/icon_1.svg",
    },
    {
      title: "Pod Growth",
      period: "From May to June",
      description:
        "Seed pods develop, maturing in size and color as the season progresses.",
      image: "/images/image_2.png",
      icon: "/images/icon_2.svg",
    },
    {
      title: "Harvest",
      period: "From June to August",
      description:
        "Pods are ready for harvest, containing mature seeds for propagation or use.",
      image: "/images/image_3.png",
      icon: "/images/icon_3.svg",
    },
    {
      title: "Trimming & Mulching",
      period: "From September to December",
      description:
        "Pruning and mulching help prepare the tree for the next growth cycle.",
      image: "/images/image_4.png",
      icon: "/images/icon_4.svg",
    },
  ];











const PeregrinaSlider = ({}) => {


    const [activeIndex, setActiveIndex] = useState(0);
    const centerWrapperRef = useRef(null);
    const scrollTween = useRef(null);
    const prevIndexRef = useRef(0);

    useEffect(() => {
        prevIndexRef.current = activeIndex;
      }, [activeIndex]);

      useEffect(() => {
        if (!centerWrapperRef.current) return;
    
        // ✅ Import inside useEffect to avoid SSR issues
        import("gsap/ScrollTrigger").then(({ ScrollTrigger }) => {
          gsap.registerPlugin(ScrollTrigger);
    
          let ctx = gsap.context(() => {
            scrollTween.current = gsap.to(
              {},
              {
                scrollTrigger: {
                  trigger: centerWrapperRef.current,
                  start: "top top",
                  end: `+=${items.length * 25}%`,
                  pin: true,
                  scrub: true,
                  anticipatePin: 1,
                  snap: {
                    snapTo: (value) => {
                      const snapIndex = Math.round(value * (items.length - 1)) / (items.length - 1);
                      return snapIndex;
                    },
                    duration: { min: 0.1, max: 0.1 },
                    ease: "power1.inOut"
                  },
                  onUpdate: (self) => {
                    const idx = Math.round(self.progress * (items.length - 1));
                    setActiveIndex(idx);
                  },
                },
              }
            );
          }, centerWrapperRef);
    
          return () => {
            if (scrollTween.current) scrollTween.current.scrollTrigger.kill();
            ctx.revert();
          };
        });
      }, []);
      const activeItem = items[activeIndex];
    return (
     
     

        <div className={styles.centerWrapper} ref={centerWrapperRef}>
        <div className={styles.header_container}>
          <h1 className={styles.heading}>Peregrina Tree Life Cycle</h1>
        </div>
        <div className={styles.lifeCycleContainer}>
          <div className={styles.imageSection}>
            <div
              key={activeItem.image}
              className={styles.imageFadeContainer}
            >
              <Image
                src={activeItem.image}
                alt={activeItem.title}
                fill
                style={{ objectFit: "cover", borderRadius: "24px" }}
                sizes="600px"
                priority
              />
            </div>
            <div className={styles.imageOverlay}>
              <h2>{activeItem.title}</h2>
              <h3>{activeItem.period}</h3>
              <p>{activeItem.description}</p>
            </div>
          </div>
          <div className={styles.listSection}>
            <ul className={styles.list}>
              {items.map((item, idx) => (
                <li
                  key={item.title}
                  className={idx === activeIndex ? styles.active : ""}
                  // Remove onClick for scroll-driven change
                >
                  <span className={styles.iconWrapper}>
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={48}
                      height={48}
                    />
                  </span>
                  <span>
                    <strong>{item.title}</strong>
                    <br />
                    <span className={styles.period}>{item.period}</span>
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
};

export default PeregrinaSlider;
