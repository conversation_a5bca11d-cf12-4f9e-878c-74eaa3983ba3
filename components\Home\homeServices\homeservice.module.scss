@import "@/public/styles/mixins/mixins";

.homeService {
    position: relative;
    height: 670px;
    width: 100%;
    overflow: hidden;
    transition: all 1s ease-in-out;
    @include max(1600) {
        height: 620px;
    }
    @include max(1200) {
        height: 479px;
    }
    @include max(767) {
        height: auto;
    }
    figure{
        height: 100%;
        @include max(767) {
            height: 335px;
        }
        &::after {
            content: "";
            width: 100%;
            height: 100%;
            display: block;
            background: linear-gradient(180deg,
                    rgba(0, 0, 0, 0) 0%,
                    rgba(0, 0, 0, 0.7) 100%);
            position: absolute;
            top: 0;
            left: 0;
        }
        img{
            transform: scale(1.1) !important;
        }
    }
    .service_img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        transition: clip-path 1s ease-in-out;
        overflow: hidden;
        figure{
            height: 670px;
            @include max(1600) {
                height: 605px;
            }
            @include max(1060) {
                height: 490px;
            }
            @include max(767) {
                height: 380px;
            }
        }
        @include max(767) {
            position: relative;
        }
        &:first-child {
            clip-path: polygon(0 0, 50% 0, 50% 100%, 0% 100%);
            transition: clip-path 1s ease-in-out;
            z-index: 9;
           
            @include max(767) {
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
            }
            .hover_cnt{
                transform: translate(-30%, 30%);
                @include max(767) {
                    transform:initial;
                }
            }
        }

        &:nth-child(2) {
            clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
            z-index: 9;
            transition: clip-path 1s ease-in-out;
            @include max(767) {
                clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
            }
            .service_texts{
                left: calc(100% - (50% - 70px));
                
                @include max(1600) {
                    left: calc(100% - (50% - 50px));
                }
                @include max(992) {
                    left: calc(100% - (55% - 70px));
                }
                @include max(767) {
                    left: 30px;
                }
            }
        }

        img {
            width: 100%;
            display: block;
            height: 100%;
            object-fit: cover;
            transition: all 1s ease-in-out;
            object-position: center right !important;
        }
        &.inactive{
            &:first-child{
                .service_texts{
                    @include max(1600) {  
                        left: 25px;
                    }
                }  
            }
            &:nth-child(2){
                .service_texts{
                    left: calc(100% - (22.5% - 70px)) !important;
                    @include max(992) {
                        left: calc(100% - (50% - 50px)) !important;
                    }
                    h4{
                        width: 18%;
                        @include max(992) {
                            width: 100%;
                        }
                    }
                }
            }
        }
        
    }

    &.hovered {
        .service_img {
            &:first-child {
                &:hover {
                    clip-path: polygon(0 0, 80% 0, 80% 100%, 0% 100%);
                    @include max(992) {
                        clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
                    }
                }
            }

            &:nth-child(2) {
                &:hover {
                    clip-path: polygon(20% 0, 100% 0, 100% 100%, 20% 100%);
                    z-index: 10;
                    @include max(992) {
                        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
                    }
                }
                .service_texts{
                    left: calc(100% - (80% - 70px));
                    @include max(992) {
                        left: 50px !important;
                    }
                }
            }
        }
        .service_texts{
            bottom: 10px !important;
            @include max(767) {
                bottom: 35px !important;
            }
        }
    }

    .service_texts {
        position: absolute;
        bottom: 90px;
        left: 60px;
        width: 100%;
        transition: all 1s ease-in-out;
        pointer-events: auto;
        z-index: 20;
        @include max(1600) {
            bottom: 111px;
        }
        @include max(992) {
            left: 50px;
            bottom: 35px;
        }
        @include max(767) {
            left: 30px !important;
            bottom: 35px !important;
        }
        .text_cnts {
            position: relative;
            z-index: 20;
            transition: all 1s ease-in-out;
            pointer-events: auto;
            &.active{
                z-index: 20;
                bottom: 111px;
                @include max(992) {
                    bottom: 0px;
                }
                @include max(767) {
                    bottom: 10px !important;
                }
                .hover_cnt{
                    opacity: 1;
                    visibility: visible;
                    transform: initial;
                    height: 120px;
                    @include max(1600) {
                        height: 150px;
                    }
                    @include max(767) {
                        height: 155px;
                    }
                }
            }
            @include max(992) {
                width: 70%;
            }
            @include max(767) {
                width: 80%;
            }
        }

        h4 {
            font-size: size(54px);
            color: #fff;
            font-weight: 300;
            transition: all 1s ease-in-out;
            @include max(1600) {
                font-size: size(48px);
            }
            @include max(1200) {
                font-size: size(38px);
            }
            @include max(992) {
                font-size: size(28px);
            }
            @include max(767) {
                font-size: size(25px);
            }
        }
        p{
            font-size: size(15px);
            color: #fff;
            max-width: 620px;
            transition: all 1s ease-in-out;
            @include max(1600) {
                max-width: 520px;
            }
            @include max(992) {
                max-width: 100%;
                font-size: size(13px);
                line-height: size(23px);
            }
            @include max(767) {
                font-size: size(12px);
                line-height: size(18px);
            }
        }
        a{
            margin-top: 40px;
            transition: all 1s ease-in-out;
            position: relative;
            z-index: 20;
            pointer-events: auto;
            @include max(1600) {
                margin-top: 30px;
            }
            @include max(767) {
                margin-top: 15px;
            }
        }
    }
    .hover_cnt{
        opacity: 0;
        visibility: hidden;
        height: 0;
        transition: all 1s ease-in-out;
        transform: translate(10%,10%);
        position: relative;
        z-index: 20;
        pointer-events: auto;
        @include max(767) {
            opacity: 1 !important;
            visibility: visible !important;
            transform: initial !important;
            height: auto !important;
        }
        p{
            margin-top: 30px;
            @include max(1600) {
                margin-top: 20px;
            }
        }
    }
}
