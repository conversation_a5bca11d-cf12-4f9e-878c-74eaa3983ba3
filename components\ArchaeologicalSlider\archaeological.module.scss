@import "@/public/styles/mixins/mixins";

.slider_content {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    overflow: hidden;
    @include max(767) {
        flex-wrap: wrap;
    }
    .slider_left {
        width: 40%;
        background-color: #fff;
        min-height: 400px;  
        display: flex;
        align-items: center;
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
        position: relative;
        &::after{
            content: "";
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background-color: #e8e1d9;
            background-image: url('/images/slider_bg.jpg');
        }
        @include max(1600) {
            min-height: 340px;
        }
        @include max(1300) {
            min-height: 320px;
        }
        @include max(992) {
            min-height: 280px;
        }
        @include max(767) {
            width: 100%;
            border-radius: 0;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            min-height: 250px;
        }
    }

    .slider_text {
        margin:0 55px;
        position: relative;
        z-index: 1;
        @include max(1550) {
            margin: 0 40px;
        }
        @include max(992) {
            margin:0 45px 0 25px;
        }
        @include max(767) {
            max-width: 100%;
            width: 100%;
            padding: 30px;
            margin: 0;
        }
    }
    h3{
        color: #34241D;
        font-size: size(26px);
        line-height: size(36px);
        margin-bottom: 15px;
        font-weight: 600;
        @include max(1300) {
            margin-bottom: 10px;
            font-size: size(23px);
            line-height: size(33px);
        }
    }
    p {
        color: #34241D;
        max-width: 355px;
        font-weight: 500;
        line-height: size(26px);
        @include max(1300) {
            line-height: size(22px);
        }
        @include max(992) {
            max-width: 290px;
            line-height: size(22px);
        }
        @include max(767) {
            max-width: 100%;
        }
    }
    .slider_text_content{
        height: 182px;
        overflow: hidden;
        overflow-y: auto;
        // padding-right: 20px;
        scrollbar-color: #694121 #eae3d9;
        scrollbar-width: thin;
        @include max(1550) {
            padding-right: 55px;
            height: 160px;
        }
        @include max(1300) {
            height: 140px;
        }
        @include max(992) {
            height: 135px;
            padding-right: 10px;
        }
        @include max(767) {
            height: auto;
            padding-right: 0px;
        }
    }
    .slider_right {
        width: 55%;
        position: relative;
        z-index: 5;
        @include max(1550) {
            width: 53%;
        }
        @include max(1300) {
            width: 49%;
        }
        @include max(767) {
            width: 100%;
        }
        img {
            border-radius: 20px;
            @include max(767) {
                border-radius: 0px;
                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
                height: 300px;
                object-fit: cover;
            }
        }
    }
}

.swiper_nav {
    position: absolute;
    left: 45%; 
    top: 50%;
    margin-top: -64px;
    margin-left: -34px;
    z-index: 5;
    @include max(1550) {
        margin-left: 15px;
    }
    @include max(1450) {
        margin-left:-8px;
    }
    @include max(1300) {
        margin-left: 40px;
    }
    @include max(1060) {
        margin-left: 29px;
    }
    @include max(992) {
        margin-top: -40px;
        margin-left:18px;    
    }
    @include max(767) {
        display: flex;  
        justify-content: center;
        gap: 10px;
        bottom: 35px;
        top: auto;
        margin: 0 auto;
        left: 0;
        right: 0;
    }
    .btn_nav {
        width: 64px;
        height: 64px;
        background-color: rgba(0, 0, 0, .5);
        overflow: hidden;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        cursor: pointer;

        &.disabled {
            opacity: .6;
        }
        @include max(1600) {
            width: 60px;
            height: 60px;
        }
        @include max(1300) {
            width: 50px;
            height: 50px;
        }
        @include max(992) {
            width: 45px;
            height: 45px;
        }
        @include max(767) {
            width: 45px;
            height: 45px;
            margin: 0 !important;
        }
        svg{
            @include max(1550) {
                width:30px !important;
                height:30px !important;
            }
            @include max(992) {
                width: 22px !important;
                height: 22px !important;
            }
            @include max(767) {
                width: 20px !important;
                height: 20px !important;
            }
        }
        &:hover{
            background-color: var(--hover-color);
        }
    }

    .prev_button {
        margin-bottom: 25px;
        @include max(992) {
            margin-bottom: 15px;
        }
        svg {
            transform: rotate(180deg);
        }
    }

   

}
:global(body.rtl){

    .swiper_nav {left: unset;
     right:42%}
     
 }

.progress_container {
    width: 40%;
    margin-top: 20px;
    margin-left: calc(100% - 55%);
    @include max(1550) {
        margin-left: calc(100% - 52%);
    }
    
    @include max(1300) {
        margin-left: calc(100% - 49%);
    }
    @include max(767) {
        width: 100%;
        margin-left: 0;
    }
}
.slider_numbers{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}

.slide_number {
    font-size: size(16px);
    font-weight: bold;
    color: #FCE1CB;
    font-family: 'Obsolete';
    @include max(1300) {
        font-size: size(14px);
    }
}

.progress_bar {
    flex-grow: 1;
    height: 2px;
    background: #7F4930;
    border-radius: 5px;
    // overflow: hidden;
    margin: 0 10px;
    position: relative;
    &:after {
        content: "";
        display: block;
        width: 5px;
        height: 5px;
        border: 2px solid #7F4930;
        border-radius: 50%;
        position: absolute;
        right: -8px;
        bottom: -3px;
    }
}

.progress {
    height: 100%;
    background: #FCE1CB;
    transition: width 0.5s ease-in-out;
}

.slider_container {
    max-width: 1270px;
    margin-left: auto;
    position: relative;
    @include max(1550) {
        max-width: 1100px;
    }
    @include max(767) {
        margin-top: 35px;
    }
    
}
:global(body.rtl) {
    .slider_content {
        .slider_left{
            border-radius: 0;
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
        }
        .slider_text{
            margin-left: 0;
            margin-right: 55px;
        }
    }
    .progress_container{
        margin-left: 0;
        margin-right: auto;
    }
    .slider_container{
        &::after{
            right: auto;
            left: 2px;
        }
    }
}