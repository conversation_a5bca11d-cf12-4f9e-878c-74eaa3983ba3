import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const NewsCard = ({ date, monthYear, title, imageUrl, description, link, target, extraClass = '' }) => {
    const cardRef = useRef(null);  

    return (
        <Link 
            href={link} 
            className={`${styles.news_card} ${extraClass ? styles[extraClass] : ''}`}
            target={target}
            ref={cardRef}
        >
            <div className={`${styles.date} date`}>
                <span>{date}</span>
                {monthYear.split(' ').map((part, index) => (
                    <React.Fragment key={index}>
                        {part}
                        {index !== monthYear.split(' ').length - 1 && <br />}
                    </React.Fragment>
                ))}
            </div>

            <div className={styles.news_desc}>
                <h3>{title}</h3>
                <div className={`${styles.news_img} news_img`}>
                    <Image src={imageUrl} width={308} height={156} alt='news image' />
                </div>
                <p>{description}</p>
            </div>
        </Link>
    );
};

export default NewsCard;
