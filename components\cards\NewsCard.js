import React, { useEffect, useRef } from 'react';
import styles from './card.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const NewsCard = ({ date, monthYear, title, imageUrl, description, link, extraClass = '' }) => {
    const cardRef = useRef(null);
    // useEffect(() => {
    //     const el = cardRef.current;
        
    //     gsap.set(el, { opacity: 1 }); 
    //     console.log("GSAP triggered on homepage");
    
    //     const timeline = gsap.timeline({
    //         scrollTrigger: {
    //             trigger: el,
    //             start: 'top 85%',
    //             end: 'bottom 50%',
    //             toggleActions: 'play none none reverse',
    //             immediateRender: false, 
    //         }
    //     });
    
    //     timeline.from(el, {
    //         opacity: 0,
    //         y: 50,
    //         duration: 0.8,
    //         ease: 'power2.out',
    //     });
    
    //     ScrollTrigger.refresh();
    // }, []);
    

    return (
        <Link 
            href={link} 
            className={`${styles.news_card} ${extraClass ? styles[extraClass] : ''}`}
            ref={cardRef}
        >
            <div className={`${styles.date} date`}>
                <span>{date}</span>
                {monthYear.split(' ').map((part, index) => (
                    <React.Fragment key={index}>
                        {part}
                        {index !== monthYear.split(' ').length - 1 && <br />}
                    </React.Fragment>
                ))}
            </div>

            <div className={styles.news_desc}>
                <h3>{title}</h3>
                <div className={`${styles.news_img} news_img`}>
                    <Image src={imageUrl} width={308} height={156} alt='news image' />
                </div>
                <p>{description}</p>
            </div>
        </Link>
    );
};

export default NewsCard;
